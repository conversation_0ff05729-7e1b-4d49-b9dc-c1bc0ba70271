<?php

/**
 * Module: StepFlow class.
 *
 * @package MEE\Modules\StepFlow
 * @since ??
 */

namespace MEE\Modules\StepFlow;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

use ET\Builder\Framework\DependencyManagement\Interfaces\DependencyInterface;
use ET\Builder\Packages\ModuleLibrary\ModuleRegistration;


/**
 * `StepFlow` is consisted of functions used for Static Module such as Front-End rendering, REST API Endpoints etc.
 *
 * This is a dependency class and can be used as a dependency for `DependencyTree`.
 *
 * @since ??
 */
class StepFlow implements DependencyInterface
{
	use StepFlowTrait\RenderCallbackTrait;
	use StepFlowTrait\ModuleClassnamesTrait;
	use StepFlowTrait\ModuleStylesTrait;
	use StepFlowTrait\ModuleScriptDataTrait;

	/**
	 * Loads `StepFlow` and registers Front-End render callback and REST API Endpoints.
	 *
	 * @since ??
	 *
	 * @return void
	 */
	public function load()
	{
		$module_json_folder_path = DIVIOPTIMIZER_MODULES_JSON_PATH . 'StepFlow/';

		add_action(
			'init',
			function () use ($module_json_folder_path) {
				ModuleRegistration::register_module(
					$module_json_folder_path,
					[
						'render_callback' => [StepFlow::class, 'render_callback'],
					]
				);
			}
		);
	}
}
