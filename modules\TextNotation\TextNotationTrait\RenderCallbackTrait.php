<?php

/**
 * TextNotation::render_callback()
 *
 * @package MEE\Modules\TextNotation
 * @since ??
 */

namespace MEE\Modules\TextNotation\TextNotationTrait;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

// phpcs:disable ET.Sniffs.ValidVariableName.UsedPropertyNotSnakeCase -- WP use snakeCase in \WP_Block_Parser_Block

use ET\Builder\Packages\Module\Module;
use ET\Builder\Framework\Utility\HTMLUtility;
use ET\Builder\FrontEnd\BlockParser\BlockParserStore;
use ET\Builder\Packages\Module\Options\Element\ElementComponents;
use MEE\Modules\TextNotation\TextNotation;
use MEE\Modules\TextNotation\TextNotationTrait\svgPatterns;

trait RenderCallbackTrait
{

	/**
	 * TextNotation render callback which outputs server side rendered HTML on the Front-End.
	 *
	 * @since ??
	 * @param array          $attrs    Block attributes that were saved by VB.
	 * @param string         $content  Block content.
	 * @param WP_Block       $block    Parsed block object that being rendered.
	 * @param ModuleElements $elements ModuleElements instance.
	 *
	 * @return string HTML rendered of TextNotation.
	 */
	public static function render_callback($attrs, $content, $block, $elements)
	{

		$notation_type = $attrs['notationText']['advanced']['type']['desktop']['value'] ?? '';
		$notation_color = $attrs['notationText']['advanced']['color']['desktop']['value'] ?? '';
		$notation_stroke_width = $attrs['notationText']['advanced']['strokeWidth']['desktop']['value'] ?? '';

		// beforeText.
		$beforeText = $elements->render(
			[
				'attrName' => 'beforeText',
			]
		);

		// notationText.
		$notationText = $elements->render(
			[
				'attrName' => 'notationText',
			]
		);

		$svg_content = '';
		if ($notation_type === 'underline') {
			$svg_content = '<path 
    d="M-0.1879694452509284 38.58940829243511 C53.65968786782624 37.36705815598551, 109.95888422051523 37.65952005788866, 189.12335267756134 38.45266435947269" 
    fill="none" 
    stroke="' . $notation_color . '" 
    stroke-width="' . $notation_stroke_width . '" 
    style="stroke-dashoffset: 189.32; stroke-dasharray: 189.32; animation: 402.23ms ease-out 0ms 1 normal forwards running rough-notation-dash"
/><path 
    d="M189.5573282679543 40.88838821742684 C150.71372374036534 36.71283613018021, 104.74943649640677 40.28412993124947, 2.420356406830251 41.08661410305649" 
    fill="none" 
    stroke="' . $notation_color . '" 
    stroke-width="' . $notation_stroke_width . '" 
    style="stroke-dashoffset: 187.221; stroke-dasharray: 187.221; animation: 397.77ms ease-out 402.23ms 1 normal forwards running rough-notation-dash"
/>';
		} elseif ($notation_type === 'box') {
			$svg_content = '<path 
    d="M-6.485457307272022 1.3395652852578752 C38.46522297070365 -1.4381357772069188, 81.13193999579619 0.5817743590458493, 192.89266555480705 -2.1702185224512536" 
    fill="none" 
    stroke="' . $notation_color . '" 
    stroke-width="' . $notation_stroke_width . '" 
    style="stroke-dashoffset: 199.424; stroke-dasharray: 199.424; animation: 165.723ms ease-out 0ms 1 normal forwards running rough-notation-dash"
/><path 
    d="M197.71697130333632 0.017349143512547016 C194.11543751396238 12.638658062554898, 197.20212284363805 22.555885446257893, 195.25159394275397 37.78677647281438" 
    fill="none" 
    stroke="' . $notation_color . '" 
    stroke-width="' . $notation_stroke_width . '" 
    style="stroke-dashoffset: 37.9658; stroke-dasharray: 37.9658; animation: 31.5499ms ease-out 165.723ms 1 normal forwards running rough-notation-dash"
/><path 
    d="M194.83540189615945 41.152878254832494 C119.36276198733087 41.75525303523593, 41.391628963278045 44.59145495947317, -6.449216950825635 42.03945625431754" 
    fill="none" 
    stroke="' . $notation_color . '" 
    stroke-width="' . $notation_stroke_width . '" 
    style="stroke-dashoffset: 201.315; stroke-dasharray: 201.315; animation: 167.294ms ease-out 197.272ms 1 normal forwards running rough-notation-dash"
/><path 
    d="M-5.914126758463681 38.2497421996668 C-4.955051342397928 33.01748596969992, -5.040385017544031 20.47741895634681, -2.1367720710113645 0.9378602104261518" 
    fill="none" 
    stroke="' . $notation_color . '" 
    stroke-width="' . $notation_stroke_width . '" 
    style="stroke-dashoffset: 37.5149; stroke-dasharray: 37.5149; animation: 31.1752ms ease-out 364.567ms 1 normal forwards running rough-notation-dash"
/><path 
    d="M-7.651113684724358 -1.3577287300440182 C69.87998923812145 1.488362311382409, 148.27944141122902 -0.46187339084954226, 196.51810232830778 0.5254274654922344" 
    fill="none" 
    stroke="' . $notation_color . '" 
    stroke-width="' . $notation_stroke_width . '" 
    style="stroke-dashoffset: 204.19; stroke-dasharray: 204.19; animation: 169.683ms ease-out 395.742ms 1 normal forwards running rough-notation-dash"
/><path 
    d="M195.07413228880614 -0.4647212428972125 C197.67159569896756 15.887024716474116, 195.5127842787653 30.87117595691234, 196.29609280359 38.791287888772786" 
    fill="none" 
    stroke="' . $notation_color . '" 
    stroke-width="' . $notation_stroke_width . '" 
    style="stroke-dashoffset: 39.331; stroke-dasharray: 39.331; animation: 32.6844ms ease-out 565.425ms 1 normal forwards running rough-notation-dash"
/><path 
    d="M196.83144112979463 37.64910533980317 C139.02450124973177 41.60468641525214, 83.21538358039446 41.52206235614724, -5.865085829675422 38.81828319753769" 
    fill="none" 
    stroke="' . $notation_color . '" 
    stroke-width="' . $notation_stroke_width . '" 
    style="stroke-dashoffset: 202.787; stroke-dasharray: 202.787; animation: 168.518ms ease-out 598.109ms 1 normal forwards running rough-notation-dash"
/><path 
    d="M-5.174178068526089 38.31295417714864 C-6.896006361395121 26.774001293815672, -4.560027704387903 17.882865068502724, -7.896558259613812 -1.7012498183175921" 
    fill="none" 
    stroke="' . $notation_color . '" 
    stroke-width="' . $notation_stroke_width . '" 
    style="stroke-dashoffset: 40.1594; stroke-dasharray: 40.1594; animation: 33.3728ms ease-out 766.627ms 1 normal forwards running rough-notation-dash"
/>';
		} elseif ($notation_type === 'circle') {
			$svg_content = '<path 
				d="M97.87176939378784 -0.2211345684572592 C112.09608412083357 -0.13758377175795822, 129.70316120158589 0.7433544017273244, 143.13898835498955 2.2396338646406733 C156.57481550839321 3.735913327554022, 169.56210538132297 6.194496288821556, 178.48673231420975 8.756542209022834 C187.41135924709653 11.318588129224112, 194.2298243341865 14.641329386891579, 196.68674995231015 17.611909385848346 C199.1436755704338 20.58248938480511, 198.06605484864855 23.555952820919934, 193.22828602295172 26.580022202763434 C188.3905171972549 29.604091584606934, 178.70262532754182 33.78385013256396, 167.6601369981292 35.75632567690935 C156.61764866871655 37.728801221254734, 141.23962040976022 37.40669195546429, 126.97335604647589 38.414875468835746 C112.70709168319155 39.4230589822072, 96.97508201090746 42.07219385989403, 82.06255081842322 41.8054267571381 C67.15001962593898 41.53865965438217, 50.343998510800866 38.70822718696626, 37.498168891570465 36.81427285230018 C24.652339272340065 34.920318517634094, 12.409203590135157 33.062807640870346, 4.98757310304083 30.441700749141614 C-2.434057384053495 27.82059385741288, -6.158629958349557 24.048332713003013, -7.031614030995485 21.08763150192779 C-7.904598103641412 18.126930290852563, -6.1724147506142275 15.409749829612805, -0.2503313328347332 12.677493482690274 C5.671752084944761 9.945237135767742, 16.257548268266596 6.773494317075755, 28.500886475681483 4.694093420392596 C40.74422468309637 2.614692523709436, 56.46346906462412 1.0775203704082186, 73.20969791165459 0.2010881025913136 C89.95592675868505 -0.6753441652255914, 115.68625333538425 -1.3395203327268987, 128.9782595578643 -0.5645001865088339 C142.27026578034435 0.21051995970923087, 153.68467872604066 4.460730690928965, 152.9617352465349 4.8512089798997025" 
				fill="none" 
				stroke="' . $notation_color . '" 
				stroke-width="' . $notation_stroke_width . '" 
				style="stroke-dashoffset: 487.338; stroke-dasharray: 487.338; animation: 418.946ms ease-out 0ms 1 normal forwards running rough-notation-dash"
			/>
			<path 
				d="M99.40088211344649 1.7024443733893264 C114.19797296133395 2.221415534796113, 130.1585961274617 3.118385963107806, 143.64794490876037 4.249242483101618 C157.13729369005904 5.38009900309543, 171.13772836744238 6.642999279189348, 180.33697480123857 8.4875834933522 C189.53622123503476 10.332167707515055, 196.37259294442256 12.385937945019291, 198.84342351153754 15.316747768078738 C201.3142540786525 18.247557591138182, 200.41390129396794 22.831388976673168, 195.16195820392835 26.07244243170888 C189.91001511388876 29.31349588674459, 178.54179827182514 32.71960468433129, 167.3317649713 34.763068498293016 C156.12173167077486 36.806532312254745, 142.54438357987638 37.066091992456236, 127.90175840077757 38.33322531547927 C113.25913322167875 39.6003586385023, 95.03066582408536 42.89682541335875, 79.47601389670709 42.36586843643119 C63.92136196932881 41.83491145950364, 47.20591618828769 37.36687034563717, 34.5738468365079 35.147483453913935 C21.941777484728107 32.9280965621907, 10.703601845750676 31.454408150993604, 3.6835977860283293 29.049547086091785 C-3.336406273694017 26.644686021189965, -6.8383425098972195 23.959001217834025, -7.546177521826181 20.718317064503015 C-8.254012533755143 17.477632911172005, -6.517951764195071 12.574248904461285, -0.5634122855454393 9.605442166105727 C5.391127193104192 6.636635427750168, 15.871136453849187 4.84204534702633, 28.181059350071607 2.9054766343696627 C40.490982246294024 0.9689079217129952, 61.34716795667941 -1.5792318006750452, 73.29612509178908 -2.013970109834279 C85.24508222689875 -2.448708418993513, 95.43242071881714 -0.038763077214383046, 99.87480216072963 0.2970467794142593 C104.31718360264212 0.6328566360429015, 100.07519408265003 -0.19163324585576605, 99.95041374326402 0.0008890299375750033" 
				fill="none" 
				stroke="' . $notation_color . '" 
				stroke-width="' . $notation_stroke_width . '" 
				style="stroke-dashoffset: 443.26; stroke-dasharray: 443.26; animation: 381.054ms ease-out 418.946ms 1 normal forwards running rough-notation-dash"
			/>';
		} elseif ($notation_type === 'highlight') {
			$svg_content = '<path 
      d="M-5.525205412879586 24.872014889493585 C45.09751771498305 14.487227613349436, 92.0991070385244 14.269127721210001, 192.46564879082143 20.42834753729403" 
      fill="none" 
      stroke="' . $notation_color . '" 
      stroke-width="28.5" 
      style="stroke-dashoffset: 198.65; stroke-dasharray: 198.65; animation: 407.105ms ease-out 0ms 1 normal forwards running rough-notation-dash"
    />
    <path 
      d="M192.96233398653567 19.919429859146476 C152.28903629133777 21.113634637997052, 99.64168807428928 17.338785044357678, 1.2963517997413874 16.260225316509604" 
      fill="none" 
      stroke="' . $notation_color . '" 
      stroke-width="' . $notation_stroke_width . '" 
      style="stroke-dashoffset: 191.716; stroke-dasharray: 191.716; animation: 392.895ms ease-out 407.105ms 1 normal forwards running rough-notation-dash"
    />';
		} elseif ($notation_type === 'strike') {
			$svg_content = '<path 
      d="M-2.660303517244756 22.55141907837242 C52.52675670735123 19.58077043728933, 108.16909437487935 21.8083238347064, 190.84630106482655 20.89426603820175" 
      fill="none" 
      stroke="' . $notation_color . '" 
      stroke-width="' . $notation_stroke_width . '" 
      style="stroke-dashoffset: 193.534; stroke-dasharray: 193.534; animation: 402.035ms ease-out 0ms 1 normal forwards running rough-notation-dash"
    />
    <path 
      d="M191.82966896798462 23.046319381333888 C146.69117082527796 20.582126455741857, 99.47878867225636 23.44048910065267, 0.2854014029726386 20.6736228922382" 
      fill="none" 
      stroke="' . $notation_color . '" 
      stroke-width="' . $notation_stroke_width . '" 
      style="stroke-dashoffset: 191.575; stroke-dasharray: 191.575; animation: 397.965ms ease-out 402.035ms 1 normal forwards running rough-notation-dash"
    />';
		} elseif ($notation_type === 'cross') {
			$svg_content = '<path 
      d="M-0.5781619884073734 2.6051575876772404 C42.96595131972921 8.123838843473838, 85.87119532943004 16.06961754492824, 192.86831721290946 32.63575014844537" 
      fill="none" 
      stroke="' . $notation_color . '" 
      stroke-width="' . $notation_stroke_width . '" 
      style="stroke-dashoffset: 195.769; stroke-dasharray: 195.769; animation: 202.011ms ease-out 0ms 1 normal forwards running rough-notation-dash"
    />
    <path 
      d="M192.1047268845141 37.3670081757009 C119.30994009812196 27.67331324981253, 44.58784082641492 14.040072941199945, 2.0309302546083927 6.096820201724768" 
      fill="none" 
      stroke="' . $notation_color . '" 
      stroke-width="' . $notation_stroke_width . '" 
      style="stroke-dashoffset: 192.65; stroke-dasharray: 192.65; animation: 198.793ms ease-out 202.011ms 1 normal forwards running rough-notation-dash"
    />
    <path 
      d="M190.361657705158 5.674651477485895 C121.85937987984576 18.068415503457707, 53.067026304443345 27.833615615472716, -1.628268275409937 36.92457768693566" 
      fill="none" 
      stroke="' . $notation_color . '" 
      stroke-width="' . $notation_stroke_width . '" 
      style="stroke-dashoffset: 194.521; stroke-dasharray: 194.521; animation: 200.723ms ease-out 400.804ms 1 normal forwards running rough-notation-dash"
    />
    <path 
      d="M0.9784314371645451 32.92640336975455 C45.31221514510168 26.6916373034464, 93.22308704421279 19.320500671629773, 191.54935297742486 6.913139071315527" 
      fill="none" 
      stroke="' . $notation_color . '" 
      stroke-width="' . $notation_stroke_width . '" 
      style="stroke-dashoffset: 192.341; stroke-dasharray: 192.341; animation: 198.474ms ease-out 601.526ms 1 normal forwards running rough-notation-dash"
    />';
		} elseif ($notation_type === 'bracket') {
			$svg_content = '<path 
      d="M-1.4384912601672113 -4.349119531456381 C-3.2092958078719676 -4.947304465947672, -8.173312387894839 -5.029577874718234, -9.74724139785394 -5.027015807572752" 
      fill="none" 
      stroke="' . $notation_color . '" 
      stroke-width="' . $notation_stroke_width . '" 
      style="stroke-dashoffset: 8.35775; stroke-dasharray: 8.35775; animation: 51.1532ms ease-out 0ms 1 normal forwards running rough-notation-dash"
    />
    <path 
      d="M-11.35952848661691 -2.7370774848386645 C-10.918699560919777 8.98676570504904, -7.524554400006309 17.376003677956763, -7.344909165985882 42.95214869547635" 
      fill="none" 
      stroke="' . $notation_color . '" 
      stroke-width="' . $notation_stroke_width . '" 
      style="stroke-dashoffset: 45.9094; stroke-dasharray: 45.9094; animation: 280.986ms ease-out 51.1532ms 1 normal forwards running rough-notation-dash"
    />
    <path 
      d="M-11.158432026859373 46.39013147121295 C-7.157193299848586 44.808882602443916, -5.7931210729293525 46.429723226418716, 1.31651925528422 45.763471824582666" 
      fill="none" 
      stroke="' . $notation_color . '" 
      stroke-width="' . $notation_stroke_width . '" 
      style="stroke-dashoffset: 12.5829; stroke-dasharray: 12.5829; animation: 77.013ms ease-out 332.139ms 1 normal forwards running rough-notation-dash"
    />
    <path 
      d="M190.4353169328533 -3.720768441911787 C191.86467925133184 -5.914359428128227, 195.19084399519488 -4.548391513666138, 199.34678375488147 -6.305802320595831" 
      fill="none" 
      stroke="' . $notation_color . '" 
      stroke-width="' . $notation_stroke_width . '" 
      style="stroke-dashoffset: 9.49312; stroke-dasharray: 9.49312; animation: 58.1022ms ease-out 409.152ms 1 normal forwards running rough-notation-dash"
    />
    <path 
      d="M197.90444539953023 -2.4205534802749753 C201.14239185512997 2.8310866728425026, 200.4513626343105 16.755176891572773, 202.2555215554312 42.37656801473349" 
      fill="none" 
      stroke="' . $notation_color . '" 
      stroke-width="' . $notation_stroke_width . '" 
      style="stroke-dashoffset: 45.2059; stroke-dasharray: 45.2059; animation: 276.68ms ease-out 467.254ms 1 normal forwards running rough-notation-dash"
    />
    <path 
      d="M198.83198523288593 45.854742432478815 C198.15042094001547 44.6820797401946, 194.43485768092796 46.35678207161836, 189.90919063752517 44.63682977342978" 
      fill="none" 
      stroke="' . $notation_color . '" 
      stroke-width="' . $notation_stroke_width . '" 
      style="stroke-dashoffset: 9.16031; stroke-dasharray: 9.16031; animation: 56.0652ms ease-out 743.935ms 1 normal forwards running rough-notation-dash"
    />';
		} elseif ($notation_type === 'dot-line') {
			$svg_content = '<path 
      d="M0 20 L190 20" 
      fill="none" 
      stroke="' . $notation_color . '" 
      stroke-width="' . $notation_stroke_width . '"
      stroke-dasharray="5,5"
      style="stroke-dashoffset: 190; 
             animation: 400ms ease-out 0ms 1 normal forwards running rough-notation-dash" 
    />';
		} elseif ($notation_type === 'wavy-line') {
			$svg_content = '<path 
				d="M0 19 Q 20 4, 40 19 T 80 19 T 120 19 T 160 19 T 200 19" 
				fill="none" 
				stroke="' . $notation_color . '" 
				stroke-width="' . $notation_stroke_width . '"
				style="stroke-dashoffset: 240; stroke-dasharray: 240; animation: 500ms ease-out 0ms 1 normal forwards running rough-notation-dash"
			/>
			<path 
				d="M0 21 Q 20 6, 40 21 T 80 21 T 120 21 T 160 21 T 200 21" 
				fill="none" 
				stroke="' . $notation_color . '" 
				stroke-width="' . $notation_stroke_width . '"
				style="stroke-dashoffset: 240; stroke-dasharray: 240; animation: 500ms ease-out 0ms 1 normal forwards running rough-notation-dash"
			/>';
		} elseif ($notation_type === 'zig-zag') {
			$svg_content = '<path 
      d="M0 20 L 30 5 L 60 35 L 90 5 L 120 35 L 150 5 L 180 35 L 210 5" 
      fill="none" 
      stroke="' . $notation_color . '" 
      stroke-width="' . $notation_stroke_width . '"
      style="stroke-dashoffset: 300; 
             stroke-dasharray: 300; 
             animation: 600ms ease-out 0ms 1 normal forwards running rough-notation-dash" 
    />';
		} elseif ($notation_type === 'double-line') {
			$svg_content = '<path 
      d="M0 15 L 190 15" 
      fill="none" 
      stroke="' . $notation_color . '" 
      stroke-width="' . $notation_stroke_width . '"
      style="stroke-dashoffset: 190; 
             stroke-dasharray: 190; 
             animation: 400ms ease-out 0ms 1 normal forwards running rough-notation-dash" 
    />
    <path 
      d="M0 25 L 190 25" 
      fill="none" 
      stroke="' . $notation_color . '" 
      stroke-width="' . $notation_stroke_width . '"
      style="stroke-dashoffset: 190; 
             stroke-dasharray: 190; 
             animation: 400ms ease-out 400ms 1 normal forwards running rough-notation-dash" 
    />';
		}

		$svg_wrapper = HTMLUtility::render(
			[
				'tag'               => 'svg',
				'attributes'        => [
					'class' => 'rough-annotation',
					'style' => 'position: absolute; top: 5px; left: 0; overflow: visible; pointer-events: none; width: 100%; height: 100%; z-index: ' . ($notation_type === 'highlight' ? '1' : 'auto'),
					'viewBox' => '0 0 200 50',
					'preserveAspectRatio' => 'none',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $svg_content,
			]
		);

		// Content container.
		$notation_wrapper = HTMLUtility::render(
			[
				'tag'               => 'span',
				'attributes'        => [
					'class' => 'dotm_text_notation_wrapper',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $notationText . $svg_wrapper,
			]
		);

		// afterText.
		$afterText = $elements->render(
			[
				'attrName' => 'afterText',
			]
		);

		$parent       = BlockParserStore::get_parent($block->parsed_block['id'], $block->parsed_block['storeInstance']);
		$parent_attrs = $parent->attrs ?? [];

		return Module::render(
			[
				// FE only.
				'orderIndex'          => $block->parsed_block['orderIndex'],
				'storeInstance'       => $block->parsed_block['storeInstance'],

				// VB equivalent.
				'attrs'               => $attrs,
				'elements'            => $elements,
				'id'                  => $block->parsed_block['id'],
				'name'                => $block->block_type->name,
				'moduleCategory'      => $block->block_type->category,
				'classnamesFunction'  => [TextNotation::class, 'module_classnames'],
				'stylesComponent'     => [TextNotation::class, 'module_styles'],
				'scriptDataComponent' => [TextNotation::class, 'module_script_data'],
				'parentAttrs'         => $parent_attrs,
				'parentId'            => $parent->id ?? '',
				'parentName'          => $parent->blockName ?? '',
				'children'            => [
					ElementComponents::component(
						[
							'attrs'         => $attrs['module']['decoration'] ?? [],
							'id'            => $block->parsed_block['id'],

							// FE only.
							'orderIndex'    => $block->parsed_block['orderIndex'],
							'storeInstance' => $block->parsed_block['storeInstance'],
						]
					),
					HTMLUtility::render(
						[
							'tag'               => 'h1',
							'attributes'        => [
								'class' => 'dotm_text_notation_container',
							],
							'childrenSanitizer' => 'et_core_esc_previously',
							'children'          => $beforeText . $notation_wrapper . $afterText,
						]
					),
				],
			]
		);
	}
}
