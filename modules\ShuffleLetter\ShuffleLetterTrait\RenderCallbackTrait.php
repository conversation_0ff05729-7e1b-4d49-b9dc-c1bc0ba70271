<?php

/**
 * ShuffleLetter::render_callback()
 *
 * @package MEE\Modules\ShuffleLetter
 * @since ??
 */

namespace MEE\Modules\ShuffleLetter\ShuffleLetterTrait;

if (!defined('ABSPATH')) {
	die('Direct access forbidden.');
}

// phpcs:disable ET.Sniffs.ValidVariableName.UsedPropertyNotSnakeCase -- WP use snakeCase in \WP_Block_Parser_Block

use ET\Builder\Packages\Module\Module;
use ET\Builder\Framework\Utility\HTMLUtility;
use ET\Builder\FrontEnd\BlockParser\BlockParserStore;
use ET\Builder\Packages\Module\Options\Element\ElementComponents;
use MEE\Modules\ShuffleLetter\ShuffleLetter; // Assuming this is your main module class

trait RenderCallbackTrait
{

	/**
	 * Static module render callback which outputs server side rendered HTML on the Front-End.
	 *
	 * @since ??
	 * @param array          $attrs    Block attributes that were saved by VB.
	 * @param string         $content  Block content.
	 * @param \WP_Block      $block    Parsed block object that being rendered.
	 * @param mixed          $elements ModuleElements instance (type hint may vary).
	 *
	 * @return string HTML rendered of Static module.
	 */
	public static function render_callback($attrs, $content, $block, $elements): string
	{
		// --- Get Attributes ---
		$before_text_val = $attrs['beforetext']['innerContent']['desktop']['value'] ?? '';
		$after_text_val  = $attrs['aftertext']['innerContent']['desktop']['value'] ?? '';
		$shuffle_text_val = $attrs['shuffletext']['innerContent']['desktop']['value'] ?? 'Hello World!'; // Default if empty
		$shuffle_text_newline_val = ($attrs['shuffletextnewline']['innerContent']['desktop']['value'] ?? 'off') === 'on';
		$after_text_newline_val  = ($attrs['aftertextnewline']['innerContent']['desktop']['value'] ?? 'off') === 'on';
		$heading_level_val = $attrs['doptm_shuffle_letter']['decoration']['font']['font']['desktop']['value']['headingLevel'] ?? 'h1';
		$shuffle_speed_val = $attrs['shuffletextspeed']['innerContent']['desktop']['value'] ?? '50';
		$shuffle_duration_val = $attrs['shuffletextdelay']['innerContent']['desktop']['value'] ?? '1.5';
		$shuffle_chars_val = $attrs['overwrite']['innerContent']['desktop']['value'] ?? ''; // Empty means use JS default

		$heading_tag = esc_html($heading_level_val ?: 'h1');

		// --- Prepare Styles (inline for simplicity, consider CSS classes) ---
		$before_text_styles_arr = [
			'display'        => $shuffle_text_newline_val ? 'block' : 'inline-block',
			'padding-right'  => $shuffle_text_newline_val ? '0px' : '10px',
		];
		$before_text_style_attr = '';
		foreach ($before_text_styles_arr as $prop => $value) {
			$before_text_style_attr .= esc_attr($prop) . ':' . esc_attr($value) . ';';
		}

		$after_text_styles_arr = [
			'display'       => $after_text_newline_val ? 'block' : 'inline-block',
			'padding-left'  => $after_text_newline_val ? '0px' : '10px',
		];
		$after_text_style_attr = '';
		foreach ($after_text_styles_arr as $prop => $value) {
			$after_text_style_attr .= esc_attr($prop) . ':' . esc_attr($value) . ';';
		}

		// --- Prepare attributes for the shuffle span ---
		$shuffle_span_attrs = [
			'class'             => 'dotm_shuffle_text', // This class is CRUCIAL for the IntersectionObserver
			'data-target-text'  => esc_attr($shuffle_text_val),
			'data-shuffle-speed'=> esc_attr($shuffle_speed_val),
			'data-duration'     => esc_attr($shuffle_duration_val),
		];
		if (!empty($shuffle_chars_val)) {
			$shuffle_span_attrs['data-chars'] = esc_attr($shuffle_chars_val);
		}
		// You might want a unique ID if you have multiple instances and need to target them specifically,
		// but the IntersectionObserver can work off the class name for all instances.
		// If you do need an ID for other JS:
		// $unique_id = 'shuffle_text_' . wp_unique_id();
		// $shuffle_span_attrs['id'] = $unique_id;


		// --- Render HTML components ---
		$shuffle_letter_span = HTMLUtility::render(
			[
				'tag'               => 'span',
				'attributes'        => $shuffle_span_attrs,
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => esc_html($shuffle_text_val), // Initial text before JS kicks in
			]
		);

		$before_text_span = '';
		if (!empty($before_text_val)) {
			$before_text_span = HTMLUtility::render(
				[
					'tag'               => 'span',
					'attributes'        => [
						'class' => 'dotm_shuffle_before_text',
						'style' => $before_text_style_attr,
					],
					'childrenSanitizer' => 'et_core_esc_previously',
					'children'          => esc_html($before_text_val),
				]
			);
		}

		$after_text_span = '';
		if (!empty($after_text_val)) {
			$after_text_span = HTMLUtility::render(
				[
					'tag'               => 'span',
					'attributes'        => [
						'class' => 'dotm_shuffle_after_text',
						'style' => $after_text_style_attr,
					],
					'childrenSanitizer' => 'et_core_esc_previously',
					'children'          => esc_html($after_text_val),
				]
			);
		}

		$parent_block_data = BlockParserStore::get_parent($block->parsed_block['id'], $block->parsed_block['storeInstance']);
		$parent_attrs = $parent_block_data->attrs ?? [];
		$parent_id = $parent_block_data->id ?? '';
		$parent_name = $parent_block_data->blockName ?? '';


		// --- Enqueue the main shuffle script if not already done ---
		// This should ideally be handled in your module's main class setup (e.g., init or constructor)
		// to ensure it's only enqueued once per page load, not per module instance.
		// Example:
		// if (!wp_script_is('my-shuffle-script', 'enqueued')) {
		//     wp_enqueue_script(
		//         'my-shuffle-script',
		//         // Adjust path to your compiled JS file containing IntersectionObserver setup and shuffleText
		//         get_stylesheet_directory_uri() . '/js/your-compiled-shuffle-script.js',
		//         [], // Dependencies
		//         '1.0.0', // Version
		//         true // In footer
		//     );
		// }
		// For now, we assume the script is globally available.


		// --- Assemble Final Module Output ---
		return Module::render(
			[
				// FE only.
				'orderIndex'          => $block->parsed_block['orderIndex'],
				'storeInstance'       => $block->parsed_block['storeInstance'],

				// VB equivalent.
				'attrs'               => $attrs,
				'elements'            => $elements,
				'id'                  => $block->parsed_block['id'],
				'name'                => $block->block_type->name,
				'moduleCategory'      => $block->block_type->category,
				'classnamesFunction'  => [ShuffleLetter::class, 'module_classnames'], // Make sure ShuffleLetter class is correct
				'stylesComponent'     => [ShuffleLetter::class, 'module_styles'],
				'scriptDataComponent' => [ShuffleLetter::class, 'module_script_data'], // This might not be needed if JS is self-contained
				'parentAttrs'         => $parent_attrs,
				'parentId'            => $parent_id,
				'parentName'          => $parent_name,
				'children'            => [
					ElementComponents::component(
						[
							'attrs'         => $attrs['module']['decoration'] ?? [],
							'id'            => $block->parsed_block['id'],
							'orderIndex'    => $block->parsed_block['orderIndex'],
							'storeInstance' => $block->parsed_block['storeInstance'],
						]
					),
					HTMLUtility::render(
						[
							'tag'               => $heading_tag,
							'attributes'        => [
								// Add any classes needed for the heading tag itself from module settings
								'class' => 'doptm_shuffle_letter_heading_wrapper', // Example class
							],
							'childrenSanitizer' => 'et_core_esc_previously',
							'children'          => $before_text_span . $shuffle_letter_span . $after_text_span,
						]
					),
					// NO inline script to directly call shuffleText here.
					// The global IntersectionObserver script will handle it.
				],
			]
		);
	}
}