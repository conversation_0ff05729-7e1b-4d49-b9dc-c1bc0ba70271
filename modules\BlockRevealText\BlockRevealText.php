<?php

/**
 * Module: BlockRevealText class.
 *
 * @package MEE\Modules\BlockRevealText
 * @since ??
 */

namespace MEE\Modules\BlockRevealText;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

use ET\Builder\Framework\DependencyManagement\Interfaces\DependencyInterface;
use ET\Builder\Packages\ModuleLibrary\ModuleRegistration;


/**
 * `BlockRevealText` is consisted of functions used for Static Module such as Front-End rendering, REST API Endpoints etc.
 *
 * This is a dependency class and can be used as a dependency for `DependencyTree`.
 *
 * @since ??
 */
class BlockRevealText implements DependencyInterface
{
	use BlockRevealTextTrait\RenderCallbackTrait;
	use BlockRevealTextTrait\ModuleClassnamesTrait;
	use BlockRevealTextTrait\ModuleStylesTrait;
	use BlockRevealTextTrait\ModuleScriptDataTrait;

	/**
	 * Loads `BlockRevealText` and registers Front-End render callback and REST API Endpoints.
	 *
	 * @since ??
	 *
	 * @return void
	 */
	public function load()
	{
		$module_json_folder_path = DIVIOPTIMIZER_MODULES_JSON_PATH . 'BlockRevealText/';

		add_action(
			'init',
			function () use ($module_json_folder_path) {
				ModuleRegistration::register_module(
					$module_json_folder_path,
					[
						'render_callback' => [BlockRevealText::class, 'render_callback'],
					]
				);
			}
		);
	}
}
