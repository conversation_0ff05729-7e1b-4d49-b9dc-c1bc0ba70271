<?php

/**
 * Module: Static Module class.
 *
 * @package MEE\Modules\YoutubeVideo
 * @since ??
 */

namespace MEE\Modules\YoutubeVideo;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

use ET\Builder\Framework\DependencyManagement\Interfaces\DependencyInterface;
use ET\Builder\Packages\ModuleLibrary\ModuleRegistration;


/**
 * `YoutubeVideo` is consisted of functions used for Static Module such as Front-End rendering, REST API Endpoints etc.
 *
 * This is a dependency class and can be used as a dependency for `DependencyTree`.
 *
 * @since ??
 */
class YoutubeVideo implements DependencyInterface
{
	use YoutubeVideoTrait\RenderCallbackTrait;
	use YoutubeVideoTrait\ModuleClassnamesTrait;
	use YoutubeVideoTrait\ModuleStylesTrait;
	use YoutubeVideoTrait\ModuleScriptDataTrait;

	/**
	 * Loads `YoutubeVideo` and registers Front-End render callback and REST API Endpoints.
	 *
	 * @since ??
	 *
	 * @return void
	 */
	public function load()
	{
		$module_json_folder_path = DIVIOPTIMIZER_MODULES_JSON_PATH . 'YoutubeVideo/';

		add_action(
			'init',
			function () use ($module_json_folder_path) {
				ModuleRegistration::register_module(
					$module_json_folder_path,
					[
						'render_callback' => [YoutubeVideo::class, 'render_callback'],
					]
				);
			}
		);
	}
}
