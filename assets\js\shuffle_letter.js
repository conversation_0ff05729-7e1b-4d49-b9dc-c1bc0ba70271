// This script should be enqueued globally by your WordPress theme/plugin
document.addEventListener('DOMContentLoaded', function () {

  // The shuffleText function (as defined previously, taking an element and options)
  function shuffleText({ /* ... as defined before ... */ element, targetText, shuffleSpeed, duration, chars }) {
    // ... (implementation from previous examples: clearing old intervals, setting new one, updating element.textContent)
    if (!element) return;

    if (element._shuffleIntervalId) {
      clearInterval(element._shuffleIntervalId);
    }

    const startTime = performance.now();
    const durationMs = duration * 1000;
    const initialDisplay = element.textContent; // Or generate random

    element._shuffleIntervalId = setInterval(() => {
      const elapsed = performance.now() - startTime;
      if (elapsed >= durationMs) {
        clearInterval(element._shuffleIntervalId);
        element._shuffleIntervalId = null;
        element.textContent = targetText;
        return;
      }
      let displayText = '';
      for (let i = 0; i < targetText.length; i++) {
        if (Math.random() < elapsed / durationMs) {
          displayText += targetText[i];
        } else {
          displayText += chars[Math.floor(Math.random() * chars.length)];
        }
      }
      element.textContent = displayText;
    }, shuffleSpeed);
  }

  const observerCallback = (entries, observerInstance) => {
      entries.forEach(entry => {
          const el = entry.target;
          if (entry.isIntersecting) {
              const textToShuffle = el.dataset.targetText || "Default Text!";
              const speed = parseInt(el.dataset.shuffleSpeed, 10) || 50;
              const animDuration = parseFloat(el.dataset.duration) || 1.5;
              const customChars = el.dataset.chars || 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';

              // console.log(`Element ${el.className} intersecting. Shuffling to: ${textToShuffle}`);
              shuffleText({
                  element: el,
                  targetText: textToShuffle,
                  shuffleSpeed: speed,
                  duration: animDuration,
                  chars: customChars,
              });
          } else {
              // Optional: Stop animation and reset text if it scrolls out
              if (el._shuffleIntervalId) {
                  clearInterval(el._shuffleIntervalId);
                  el._shuffleIntervalId = null;
                  el.textContent = el.dataset.targetText || "Default Text!"; // Reset to target
                  // console.log(`Element ${el.className} scrolled out. Animation stopped.`);
              }
          }
      });
  };

  const observerOptions = {
      threshold: 0.5, // 50% visible
  };

  const observer = new IntersectionObserver(observerCallback, observerOptions);
  const targets = document.querySelectorAll('.dotm_shuffle_text');

  if (targets.length > 0) {
      targets.forEach(target => observer.observe(target));
  } else {
      // console.log("No .dotm_shuffle_text elements found to observe.");
  }
});