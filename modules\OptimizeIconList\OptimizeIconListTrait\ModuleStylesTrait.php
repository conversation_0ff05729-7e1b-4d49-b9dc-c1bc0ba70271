<?php

/**
 * OptimizeIconList::module_styles().
 *
 * @package MEE\Modules\OptimizeIconList
 * @since ??
 */

namespace MEE\Modules\OptimizeIconList\OptimizeIconListTrait;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

use ET\Builder\FrontEnd\Module\Style;
use ET\Builder\Packages\Module\Layout\Components\StyleCommon\CommonStyle;
use ET\Builder\Packages\Module\Options\Css\CssStyle;
use MEE\Modules\ChildModule\ChildModule;

trait ModuleStylesTrait
{

	use CustomCssTrait;

	/**
	 * Child Module's style components.
	 *
	 * This function is equivalent of JS function ModuleStyles located in
	 * src/components/OptimizeIconList/styles.tsx.
	 *
	 * @param array $args {
	 *     An array of arguments.
	 *
	 * @type string $id Module ID. In VB, the ID of module is UUIDV4. In FE, the ID is order index.
	 * @type string $name Module name.
	 * @type string $attrs Module attributes.
	 * @type string $parentAttrs Parent attrs.
	 * @type string $orderClass Selector class name.
	 * @type string $parentOrderClass Parent selector class name.
	 * @type string $wrapperOrderClass Wrapper selector class name.
	 * @type string $settings Custom settings.
	 * @type string $state Attributes state.
	 * @type string $mode Style mode.
	 * @type ModuleElements $elements ModuleElements instance.
	 * }
	 * @since ??
	 */
	public static function module_styles($args)
	{
		$attrs        = $args['attrs'] ?? [];
		$parent_attrs = $args['parentAttrs'] ?? [];
		$order_class  = $args['orderClass'];
		$elements     = $args['elements'];
		$settings     = $args['settings'] ?? [];

		$wraperSelector = "{$order_class} .dotm_optimize_icon_container_wraper";
		$itemContainerSelector = "{$order_class} .dotm_optimize_icon_item_container";
		$tooltipTextAfterSelector = "{$order_class} .dotm_optimize_icon_item_container .dotm_optimize_icon_item_tooltip_text::after";
		$tooltipTextSelector = "{$order_class} .dotm_optimize_icon_item_container .dotm_optimize_icon_item_tooltip_text";

		$reverse_content = $attrs['wrapper']['advanced']['reverse_content']['desktop']['value'] ?? '';
		$layoutType = $attrs['wrapper']['advanced']['layoutType']['desktop']['value'] ?? '';
		$toolTipBgColor = $attrs['tooltip']['decoration']['bgColor']['desktop']['value'] ?? '';

		Style::add(
			[
				'id'            => $args['id'],
				'name'          => $args['name'],
				'orderIndex'    => $args['orderIndex'],
				'storeInstance' => $args['storeInstance'],
				'styles'        => [
					// Module.
					$elements->style(
						[
							'attrName'   => 'module',
							'styleProps' => [
								'disabledOn' => [
									'disabledModuleVisibility' => $settings['disabledModuleVisibility'] ?? null,
								],
								'advancedStyles' => [
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $wraperSelector,
											'attr'     => $attrs['wrapper']['advanced']['layoutType'],
											'property' => "flex-direction"
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $wraperSelector,
											'attr'     => $attrs['wrapper']['advanced']['gap'] ?? [],
											'property' => "gap"
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $itemContainerSelector,
											'attr'     => $attrs['wrapper']['advanced']['vr_alignment'],
											'property' => "align-items"
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $itemContainerSelector,
											'attr'     => $attrs['wrapper']['advanced']['reverse_content'] ?? [],
											'property' => $reverse_content === 'on' ? "flex-direction: row-reverse;" : ''
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $wraperSelector,
											'attr'     => $attrs['wrapper']['advanced']['alignment'],
											'property' => $layoutType === "column" ? "align-items" : "justify-content"
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $tooltipTextSelector,
											'attr'     => $attrs['tooltip']['decoration']['bgColor'],
											'property' => "background-color"
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $tooltipTextAfterSelector,
											'attr'     => $attrs['tooltip']['decoration']['bgColor'],
											'property' => "border-color: transparent {$toolTipBgColor} transparent transparent;"
										]
									],
								]
							],
						]
					),

					// Title.
					$elements->style(
						[
							'attrName' => 'title',
						]
					),

					// tooltip.
					$elements->style(
						[
							'attrName' => 'tooltip',
						]
					),

					// image_container.
					$elements->style(
						[
							'attrName' => 'image_container',
						]
					),

					// Icon.
					$elements->style(
						[
							'attrName'   => 'icon',
							'styleProps' => [
								'advancedStyles' => [
									[
										'componentName' => 'divi/common',
										'props'         => [
											'attr'                => $attrs['icon']['innerContent'] ?? [],
											'declarationFunction' => [ChildModule::class, 'icon_font_declaration'],
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'attr'     => $attrs['icon']['advanced']['color'] ?? [],
											'property' => 'color',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'attr'     => $attrs['icon']['advanced']['size'] ?? [],
											'property' => 'font-size',
										]
									],
								]
							]
						]
					),

					/*
					 * We need to add CssStyle at the very bottom of other
					 * components so that custom css can override module styles
					 * till we find a more elegant solution.
					 */
					CssStyle::style(
						[
							'selector'  => $args['orderClass'],
							'attr'      => $attrs['css'] ?? [],
							'cssFields' => self::custom_css(),
						]
					),

					// ATTENTION: The code is intentionally added and commented in FE only as an example of expected value format.
					// If you have custom style processing, the style output should be passed as an `array` of style declarations
					// to the `styles` property of the `Style::add` method. For example:
					// [
					// 	[
					// 		'atRules'     => false,
					// 		'selector'    => $icon_selector,
					// 		'declaration' => 'color: red;'
					// 	],
					// 	[
					// 		'atRules'     => '@media only screen and (max-width: 767px)',
					// 		'selector'    => $icon_selector,
					// 		'declaration' => 'color: green;'
					// 	],
					// ],

					// The code below is an example of how to use the `CssStyle::style` method to generate CSS style.
					CssStyle::style(
						[
							'selector'  => $args['orderClass'],
							'attr'      => $attrs['css'] ?? [],
							'cssFields' => self::custom_css(),
						]
					),
				],
			]
		);
	}
}
