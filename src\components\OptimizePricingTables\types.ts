import { ModuleEditProps } from '@divi/module-library';
import {
  FormatBreakpointStateAttr,
  InternalAttrs,
  type Element,
  type Icon,
  type Module,
} from '@divi/types';

export interface OptimizePricingTablesCssAttr extends Module.Css.AttributeValue {
  contentContainer?: string;
  title?: string;
  content?: string;
  icon?: string;
}

export type OptimizePricingTablesCssGroupAttr = FormatBreakpointStateAttr<OptimizePricingTablesCssAttr>;

export interface OptimizePricingTablesAttrs extends InternalAttrs {

  // CSS options is used across multiple elements inside the module thus it deserves its own top property.
  css?: OptimizePricingTablesCssGroupAttr;

  // Module
  module?: {
    meta?: Element.Meta.Attributes;
    advanced?: {
      link?: Element.Advanced.Link.Attributes;
      htmlAttributes?: Element.Advanced.IdClasses.Attributes;
      text?: Element.Advanced.Text.Attributes;
    };
    decoration?: Element.Decoration.PickedAttributes<
      'animation' |
      'background' |
      'border' |
      'boxShadow' |
      'disabledOn' |
      'filters' |
      'overflow' |
      'position' |
      'scroll' |
      'sizing' |
      'spacing' |
      'sticky' |
      'transform' |
      'transition' |
      'zIndex'
    >;
  };

  
  title?: string | any;
  subTitle?: string | any;
  currency?: string | any;
  priceWrapper?: string | any;
  frequency?: string | any;
  priceContainer?: string | any;
  icon?: string | any;
  content?: string | any;
  image?: string | any;
  button?: string | any;
  headingWrapper?: string | any;

}

export type OptimizePricingTablesEditProps = ModuleEditProps<OptimizePricingTablesAttrs>;
