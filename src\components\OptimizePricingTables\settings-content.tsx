// External dependencies.
import React, { ReactElement } from 'react';

// WordPress dependencies
import { __ } from '@wordpress/i18n';

// Divi dependencies.
import {
  AdminLabelGroup,
  BackgroundGroup,
  DraggableChildModuleListContainer,
  FieldContainer,
  LinkGroup,
} from '@divi/module';
import {
  DraggableListContainer,
  IconPickerContainer,
} from '@divi/field-library';
import {
  type Module,
} from '@divi/types';
import { GroupContainer } from '@divi/modal';
import {OptimizePricingTablesAttrs} from "./types";


export const SettingsContent = ({
    defaultSettingsAttrs,
  }: Module.Settings.Panel.Props<OptimizePricingTablesAttrs>): ReactElement => (
  <React.Fragment>
    <DraggableChildModuleListContainer
      childModuleName="dotm/optimize-pricing-tables-item"
      addTitle={__('Add New Item', 'et_builder')}
    >
      <DraggableListContainer />
    </DraggableChildModuleListContainer>
    <GroupContainer
      id="icon"
      title={__('Icon', 'divi-optimaizer-modules')}
    >
      <FieldContainer
        attrName="icon.innerContent"
        label={__('Icon', 'divi-optimaizer-modules')}
        description={__('Upload an Icon', 'divi-optimaizer-modules')}
        defaultAttr={defaultSettingsAttrs?.icon}
        features={{
          sticky: false,
        }}
      >
        <IconPickerContainer />
      </FieldContainer>
    </GroupContainer>
    <LinkGroup />
    <BackgroundGroup />
    <AdminLabelGroup />
  </React.Fragment>
);
