{"name": "dotm/block-reveal-text", "d4Shortcode": "", "title": "Block Reveal Text", "titles": "Block Reveal Texts", "moduleIcon": "dotm/block-reveal-text", "category": "module", "attributes": {"module": {"type": "object", "selector": "{{selector}}", "default": {"meta": {"adminLabel": {"desktop": {"value": "Block Reveal Text"}}}, "advanced": {"text": {"text": {"desktop": {"value": {"color": "light"}}}}}}, "defaultPrintedStyle": {"decoration": {"background": {"desktop": {"value": {"color": "#ecf4f7"}}}}}, "styleProps": {"border": {"important": true}}}, "revealText": {"type": "object", "selector": "{{selector}} .dotm_block_text_reveal_text", "default": {"decoration": {"font": {"font": {"desktop": {"value": {"headingLevel": "h2"}}}}}}, "defaultPrintedStyle": {"decoration": {"font": {"font": {"desktop": {"value": {"size": "26px", "lineHeight": "1em", "weight": "500"}}}}}}, "styleProps": {"font": {"important": {"font": {"desktop": {"value": {"color": true}}}}}}, "tagName": "h2", "attributes": {"class": "dotm_block_text_reveal_text"}, "inlineEditor": "plainText", "elementType": "heading", "childrenSanitizer": "et_core_esc_previously"}, "revealAnimation": {"type": "object", "selector": "{{selector}} .dotm_block_text_reveal_wrapper", "default": {"advanced": {"type": {"desktop": {"value": "left_to_right"}}, "revealColor": {"desktop": {"value": "#7cdb24"}}, "display_type": {"desktop": {"value": "block"}}}}}, "content": {"type": "object", "selector": "{{selector}} .example_static_module__content", "tagName": "div", "attributes": {"class": "example_static_module__content"}, "inlineEditor": "richText", "childrenSanitizer": "et_core_esc_previously", "styleProps": {"bodyFont": {"selectors": {"desktop": {"value": "{{selector}} .example_static_module__content"}}}}}}, "customCssFields": {"contentContainer": {"subName": "contentContainer", "selectorSuffix": " .example_static_module__content-container"}, "title": {"subName": "title", "selector": "div{{selector}}", "selectorSuffix": " .example_static_module__title"}, "content": {"subName": "content", "selectorSuffix": " .example_static_module__content"}, "image": {"subName": "image", "selectorSuffix": " .example_static_module__image img"}}}