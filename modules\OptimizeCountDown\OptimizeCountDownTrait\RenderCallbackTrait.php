<?php

/**
 * ChildModule::render_callback()
 *
 * @package MEE\Modules\ChildModule
 * @since ??
 */

namespace MEE\Modules\OptimizeCountDown\OptimizeCountDownTrait;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

// phpcs:disable ET.Sniffs.ValidVariableName.UsedPropertyNotSnakeCase -- WP use snakeCase in \WP_Block_Parser_Block

use ET\Builder\FrontEnd\BlockParser\BlockParserStore;
use ET\Builder\Packages\Module\Module;
use ET\Builder\Packages\Module\Options\Element\ElementComponents;
use MEE\Modules\OptimizeCountDown\OptimizeCountDown;
use ET\Builder\Framework\Utility\HTMLUtility;

trait RenderCallbackTrait
{
	use ModuleClassnamesTrait;
	use ModuleStylesTrait;
	use ModuleScriptDataTrait;
	/**
	 * OptimizeCountDown render callback which outputs server side rendered HTML on the Front-End.
	 *
	 * @since ??
	 *
	 * @param array          $attrs Block attributes that were saved by VB.
	 * @param string         $content          Block content.
	 * @param \WP_Block      $block            Parsed block object that being rendered.
	 * @param ModuleElements $elements         ModuleElements instance.
	 *
	 * @return string HTML rendered of Parent module.
	 */
	public static function render_callback($attrs, $content, $block, $elements)
	{
		$children_ids = $block->parsed_block['innerBlocks'] ? array_map(
			function ($inner_block) {
				return $inner_block['id'];
			},
			$block->parsed_block['innerBlocks']
		) : [];

		$parent       = BlockParserStore::get_parent($block->parsed_block['id'], $block->parsed_block['storeInstance']);
		$parent_attrs = $parent->attrs ?? [];

		$uuid = substr(str_shuffle(str_repeat($x = '0123456789abcdefghijklmnopqrstuvwxyz', ceil(10 / strlen($x)))), 1, 10) . (new \DateTime())->format('U');

		$date_time = $attrs['date_time']['innerContent']['desktop']['value'] ?? '2025-07-01T00:00:00';
		$useBlink = $attrs['timer']['decoration']['useBlink']['desktop']['value'] ?? "on";
		$useLabel = $attrs['timer']['decoration']['useLabel']['desktop']['value'] ?? "off";
		$hideDay = $attrs['timer']['decoration']['hideDay']['desktop']['value'] ?? "off";
		$hideSecond = $attrs['timer']['decoration']['hideSecond']['desktop']['value'] ?? "off";
		// warning.
		$warning = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'dotm_coming_soon_warning',
					'style' => "display: none; color: #ff6666; font-weight: bold; margin-bottom: 20px;"
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => "The date you have entered has already passed. Please choose a future
            date to activate the countdown timer and see the launch schedule",
			]
		);

		// Title.
		$title = $elements->render(
			[
				'attrName' => 'title',
			]
		);

		// days label.
		$days_label = HTMLUtility::render(
			[
				'tag'               => 'span',
				'attributes'        => [
					'class' => 'label dotm_coming_soon_timer_label',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => "DAYS",
			]
		);

		// days.
		$days = HTMLUtility::render(
			[
				'tag'               => 'span',
				'attributes'        => [
					'class' => 'dotm_coming_soon_days dotm_coming_soon_time',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => "00",
			]
		);

		

		// timer_item.
		$days_timer_item = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'dotm_coming_soon_days_wrapper dotm_coming_soon_timer_item',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $days . ($useLabel === "off" ? $days_label : ""),
			]
		);

		// timer_inner.
		$days_timer_item_inner = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'dotm_coming_soon_timer_inner',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $days_timer_item . ($useLabel === "on" ? $days_label : "") ,
			]
		);

		// hours label.
		$hours_label = HTMLUtility::render(
			[
				'tag'               => 'span',
				'attributes'        => [
					'class' => 'label dotm_coming_soon_timer_label',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => "HOURS",
			]
		);

		// hours.
		$hours = HTMLUtility::render(
			[
				'tag'               => 'span',
				'attributes'        => [
					'class' => 'dotm_coming_soon_hours dotm_coming_soon_time',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => "00",
			]
		);

		// hours timer_item.
		$hours_timer_item = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'dotm_coming_soon_hours_wrapper dotm_coming_soon_timer_item',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $hours . ($useLabel === "off" ? $hours_label : ""),
			]
		);

		// timer_inner.
		$hours_timer_item_inner = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'dotm_coming_soon_timer_inner',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $hours_timer_item . ($useLabel === "on" ? $hours_label : ""),
			]
		);

		// hours label.
		$minutes_label = HTMLUtility::render(
			[
				'tag'               => 'span',
				'attributes'        => [
					'class' => 'label dotm_coming_soon_timer_label',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => "MINUTES",
			]
		);

		// minutes.
		$minutes = HTMLUtility::render(
			[
				'tag'               => 'span',
				'attributes'        => [
					'class' => 'dotm_coming_soon_minutes dotm_coming_soon_time',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => "00",
			]
		);

		// minutes_timer_item.
		$minutes_timer_item = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'dotm_coming_soon_minutes_wrapper dotm_coming_soon_timer_item',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $minutes . ($useLabel === "off" ? $minutes_label : ""),
			]
		);

		// timer_inner.
		$minutes_timer_item_inner = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'dotm_coming_soon_timer_inner',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $minutes_timer_item . ($useLabel === "on" ? $minutes_label : ""),
			]
		);

		// seconds_label label.
		$seconds_label = HTMLUtility::render(
			[
				'tag'               => 'span',
				'attributes'        => [
					'class' => 'label dotm_coming_soon_timer_label',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => "SECONDS",
			]
		);

		// seconds.
		$seconds = HTMLUtility::render(
			[
				'tag'               => 'span',
				'attributes'        => [
					'class' => 'dotm_coming_soon_seconds dotm_coming_soon_time',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => "00",
			]
		);

		// seconds_timer_item.
		$seconds_timer_item = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'dotm_coming_soon_seconds_wrapper dotm_coming_soon_timer_item',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $seconds . ($useLabel === "off" ? $seconds_label : ""),
			]
		);

		// timer_inner.
		$seconds_timer_item_inner = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'dotm_coming_soon_timer_inner',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $seconds_timer_item . ($useLabel === "on" ? $seconds_label : ""),
			]
		);

		// timer_blink_container.
		$timer_blink = HTMLUtility::render(
			[
				'tag'               => 'span',
				'attributes'        => [
					'class' => 'dotm_coming_soon_timer_tik',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => "",
			]
		);

		// timer_blink_container.
		$timer_blink_container = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'dotm_coming_soon_timer_blink',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $timer_blink . $timer_blink,
			]
		);

		// timer.
		$timer = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'dotm_coming_soon_timer',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => ($hideDay === "off" ? $days_timer_item_inner : "") . ($hideDay === "off" && $useBlink === "on" ? $timer_blink_container : "") . $hours_timer_item_inner . ($useBlink === "on" ? $timer_blink_container : "") . $minutes_timer_item_inner. ($hideSecond === "off" && $useBlink === "on" ? $timer_blink_container : "") . ($hideSecond === "off" ? $seconds_timer_item_inner : ""),
			]
		);

		// container.
		$container = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'dotm_coming_soon_container',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $title . $warning . $timer,
			]
		);

		// section.
		$section = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'dotm_coming_soon_section',
					'id' => $uuid
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $container,
			]
		);

		return Module::render(
			[
				// FE only.
				'orderIndex'          => $block->parsed_block['orderIndex'],
				'storeInstance'       => $block->parsed_block['storeInstance'],

				// VB equivalent.
				'id'                  => $block->parsed_block['id'],
				'name'                => $block->block_type->name,
				'moduleCategory'      => $block->block_type->category,
				'attrs'               => $attrs,
				'elements'            => $elements,
				'classnamesFunction'  => [OptimizeCountDown::class, 'module_classnames'],
				'scriptDataComponent' => [OptimizeCountDown::class, 'module_script_data'],
				'stylesComponent'     => [OptimizeCountDown::class, 'module_styles'],
				'parentAttrs'         => $parent_attrs,
				'parentId'            => $parent->id ?? '',
				'parentName'          => $parent->blockName ?? '',
				'children'            => [
					ElementComponents::component(
						[
							'attrs'         => $attrs['module']['decoration'] ?? [],
							'id'            => $block->parsed_block['id'],

							// FE only.
							'orderIndex'    => $block->parsed_block['orderIndex'],
							'storeInstance' => $block->parsed_block['storeInstance'],
						]
					) . $section,
					'childrenIds'         => $children_ids,
					HTMLUtility::render(
						[
							'tag' => 'script',
							'attributes' => [],
							'childrenSanitizer' => 'et_core_esc_previously',
							'children' => "document.addEventListener('DOMContentLoaded', function() {
                                const id = " . json_encode($uuid) . ";
                                const launchDate = " . json_encode($date_time) . ";
                                const hideDay = " . json_encode($hideDay) . ";
                                const hideSecond = " . json_encode($hideSecond) . ";
                                initComingSoonTimer(id, launchDate, hideDay, hideSecond);
                            });",
						]
					),
				]

			]
		);
	}
}
