<?php
/**
 * Module: OptimizePricingTables class.
 *
 * @package MEE\Modules\OptimizePricingTables
 * @since ??
 */

namespace MEE\Modules\OptimizePricingTables;

if ( ! defined( 'ABSPATH' ) ) {
	die( 'Direct access forbidden.' );
}


use ET\Builder\Framework\DependencyManagement\Interfaces\DependencyInterface;
use ET\Builder\Packages\ModuleLibrary\ModuleRegistration;

/**
 * `OptimizePricingTables` is consisted of functions used for OptimizePricingTables such as Front-End rendering, REST API Endpoints etc.
 *
 * This is a dependency class and can be used as a dependency for `DependencyTree`.
 *
 * @since ??
 */
class OptimizePricingTables implements DependencyInterface {
	use OptimizePricingTablesTrait\RenderCallbackTrait;

	/**
	 * Loads `OptimizePricingTables` and registers Front-End render callback and REST API Endpoints.
	 *
	 * @since ??
	 *
	 * @return void
	 */
	public function load() {
		$module_json_folder_path = DIVIOPTIMIZER_MODULES_JSON_PATH . 'OptimizePricingTables/';

		add_action(
			'init',
			function() use ( $module_json_folder_path ) {
				ModuleRegistration::register_module(
					$module_json_folder_path,
					[
						'render_callback' => [ OptimizePricingTables::class, 'render_callback' ],
					]
				);
			}
		);
	}
}
