<?php
/**
 * YoutubeVideo::render_callback()
 *
 * @package MEE\Modules\YoutubeVideo
 * @since ??
 */

namespace MEE\Modules\YoutubeVideo\YoutubeVideoTrait;

if ( ! defined( 'ABSPATH' ) ) {
	die( 'Direct access forbidden.' );
}

// phpcs:disable ET.Sniffs.ValidVariableName.UsedPropertyNotSnakeCase -- WP use snakeCase in \WP_Block_Parser_Block

use ET\Builder\Packages\Module\Module;
use ET\Builder\Framework\Utility\HTMLUtility;
use ET\Builder\FrontEnd\BlockParser\BlockParserStore;
use ET\Builder\Packages\Module\Options\Element\ElementComponents;
use MEE\Modules\YoutubeVideo\YoutubeVideo;

trait RenderCallbackTrait {

	/**
	 * Static module render callback which outputs server side rendered HTML on the Front-End.
	 *
	 * @since ??
	 * @param array          $attrs    Block attributes that were saved by VB.
	 * @param string         $content  Block content.
	 * @param WP_Block       $block    Parsed block object that being rendered.
	 * @param ModuleElements $elements ModuleElements instance.
	 *
	 * @return string HTML rendered of Static module.
	 */
	public static function render_callback( $attrs, $content, $block, $elements ) {

		$height = $attrs['youtubeStyle']['advanced']['youtubeHeight']['desktop']['value'] ?? '';
		$width = $attrs['youtubeStyle']['advanced']['youtubeWidth']['desktop']['value'] ?? '';
		$videoLink = $attrs['youtubeLink']['innerContent']['desktop']['value'] ?? '';
		$validVideoLink = '';
		
		// Check if the video link is already in embed format
		if (strpos($videoLink, 'www.youtube.com/embed/') !== false) {
		    $validVideoLink = $videoLink;
		} 
		// If it's a shortened YouTube URL format (youtu.be)
		else if (strpos($videoLink, 'https://youtu.be/') !== false) {
		    $videoId = explode('/', $videoLink)[3]; // Extracting video ID
		    $validVideoLink = "https://www.youtube.com/embed/{$videoId}";
		} 
		// If it's a standard YouTube URL format
		else if (strpos($videoLink, 'https://www.youtube.com/watch?v=') !== false) {
		    $urlParts = parse_url($videoLink);
		    parse_str($urlParts['query'], $queryParams);
		    $videoId = $queryParams['v'] ?? ''; // Get video ID from query parameter
		    $validVideoLink = "https://www.youtube.com/embed/{$videoId}";
		}
		
		$parent       = BlockParserStore::get_parent( $block->parsed_block['id'], $block->parsed_block['storeInstance'] );
		$parent_attrs = $parent->attrs ?? [];

		$iframe = HTMLUtility::render(
			[
				'tag'               => 'iframe',
				'attributes'        => [
					'class' => 'dotm-youtube-video',
					'src' => $validVideoLink,
					'frameborder' => 0,
					'allowfullscreen' => 'allowfullscreen',
					'title'=>"Optimize YouTube Video",
					'style'=> "width: $width; height: $height;"
				],
				'childrenSanitizer' => 'et_core_esc_previously',
			]
			);

		return Module::render(
			[
				// FE only.
				'orderIndex'          => $block->parsed_block['orderIndex'],
				'storeInstance'       => $block->parsed_block['storeInstance'],

				// VB equivalent.
				'attrs'               => $attrs,
				'elements'            => $elements,
				'id'                  => $block->parsed_block['id'],
				'name'                => $block->block_type->name,
				'moduleCategory'      => $block->block_type->category,
				'classnamesFunction'  => [ YoutubeVideo::class, 'module_classnames' ],
				'stylesComponent'     => [ YoutubeVideo::class, 'module_styles' ],
				'scriptDataComponent' => [ YoutubeVideo::class, 'module_script_data' ],
				'parentAttrs'         => $parent_attrs,
				'parentId'            => $parent->id ?? '',
				'parentName'          => $parent->blockName ?? '',
				'children'            => [
					ElementComponents::component(
						[
							'attrs'         => $attrs['module']['decoration'] ?? [],
							'id'            => $block->parsed_block['id'],

							// FE only.
							'orderIndex'    => $block->parsed_block['orderIndex'],
							'storeInstance' => $block->parsed_block['storeInstance'],
						]
					),
					HTMLUtility::render(
						[
							'tag'               => 'iframe',
							'attributes'        => [
								'class' => 'dotm-youtube-video',
								'src' => $validVideoLink,
								'frameborder' => 0,
								'allowfullscreen' => 'allowfullscreen',
								'title'=>"Optimize YouTube Video",
								'style'=> "width: $width; height: $height;"
							],
							'childrenSanitizer' => 'et_core_esc_previously',
						]
						)
				],
			]
		);
	}
}
