// External dependencies.
import React, { ReactElement } from 'react';

// Divi dependencies.
import {
  StyleContainer,
  StylesProps,
  CssStyle,
  CommonStyle,
} from '@divi/module';

// Local dependencies.
import { BreadcrumbsAttrs } from './types';
import { cssFields } from './custom-css';
import { iconFontDeclaration } from '../BreadcrumbsItem/style-declarations';

/**
 * Parent Module's style components.
 *
 * @since ??
 */
export const ModuleStyles = ({
  attrs,
  elements,
  settings,
  orderClass,
  mode,
  state,
  noStyleTag,
}: StylesProps<BreadcrumbsAttrs>): ReactElement => {

  const home_btn_selector = `${orderClass} .dotm_breadcrumbs_btn`;
  const icon_placemant = attrs?.home_btn?.decoration?.icon_placement?.desktop?.value;
  const icon_gap = attrs?.home_btn?.decoration?.icon_gap?.desktop?.value;
  
  console.log(attrs);
  return (
    <StyleContainer mode={mode} state={state} noStyleTag={noStyleTag}>
      {/* Module */}
      {elements.style({
        attrName: 'module',
        styleProps: {
          disabledOn: {
            disabledModuleVisibility: settings?.disabledModuleVisibility,
          },
        },
      })}

      {
        elements.style({
          attrName: 'home_btn',
          styleProps: {
            advancedStyles: [
              {
                componentName: 'divi/common',
                props: {
                  attr: attrs?.home_btn?.decoration?.icon_placement,
                  property: `flex-direction: ${icon_placemant} !important; gap: ${icon_gap} !important;`,
                },
              },
            ],
          },
        })
      }

      {/* Icon */}
      {elements.style({
        attrName: 'home_icon',
        styleProps: {
          advancedStyles: [
            {
              componentName: "divi/common",
              props: {
                attr: attrs?.home_icon?.innerContent ?? {},
                declarationFunction: iconFontDeclaration,
              }
            },
            {
              componentName: "divi/common",
              props: {
                attr: attrs?.home_icon?.advanced?.color,
                property: "color"
              }
            },
            {
              componentName: "divi/common",
              props: {

                attr: attrs?.home_icon?.advanced?.size,
                property: "font-size"
              }
            },
          ]
        }
      })}

      {/* Icon */}
      {elements.style({
        attrName: 'icon',
        styleProps: {
          advancedStyles: [
            {
              componentName: "divi/common",
              props: {
                attr:attrs?.icon?.innerContent ?? {},
                declarationFunction:iconFontDeclaration,
              }
            },
            {
              componentName: "divi/common",
              props: {
                attr: attrs?.icon?.advanced?.color,
                property:"color"
              }
            },
            {
              componentName: "divi/common",
              props: {
                attr:attrs?.icon?.advanced?.size,
                property:"font-size"
              }
            },
            {
              componentName: "divi/common",
              props: {
                attr:attrs?.icon?.advanced?.icon_gap,
                property:"gap"
              }
            } 
          ]
        }
      })}

      {/*
       * We need to add CssStyle at the very bottom of other components
       * so that custom css can override module styles till we find a
       * more elegant solution.
       */}
      <CssStyle
        selector={orderClass}
        attr={attrs.css}
        cssFields={cssFields}
      />
      <CssStyle
        selector={orderClass}
        attr={attrs.css}
        cssFields={cssFields}
      />
    </StyleContainer>
  )
};

