<?php
/**
 * FacebookLikeButton::render_callback()
 *
 * @package MEE\Modules\FacebookLikeButton
 * @since ??
 */

namespace MEE\Modules\FacebookLikeButton\FacebookLikeButtonTrait;

if ( ! defined( 'ABSPATH' ) ) {
	die( 'Direct access forbidden.' );
}

// phpcs:disable ET.Sniffs.ValidVariableName.UsedPropertyNotSnakeCase -- WP use snakeCase in \WP_Block_Parser_Block

use ET\Builder\Packages\Module\Module;
use ET\Builder\Framework\Utility\HTMLUtility;
use ET\Builder\FrontEnd\BlockParser\BlockParserStore;
use ET\Builder\Packages\Module\Options\Element\ElementComponents;
use MEE\Modules\FacebookLikeButton\FacebookLikeButton;

trait RenderCallbackTrait {

	/**
	 * Static module render callback which outputs server side rendered HTML on the Front-End.
	 *
	 * @since ??
	 * @param array          $attrs    Block attributes that were saved by VB.
	 * @param string         $content  Block content.
	 * @param WP_Block       $block    Parsed block object that being rendered.
	 * @param ModuleElements $elements ModuleElements instance.
	 *
	 * @return string HTML rendered of Static module.
	 */
	public static function render_callback( $attrs, $content, $block, $elements ) {
		
		$likeButtonURL = $attrs['fblikebutton']['advanced']['url']['desktop']['value'] ?? '';
		$layout        = $attrs['fblikebutton']['advanced']['layout']['desktop']['value'] ?? '';
		$colorscheme   = $attrs['fblikebutton']['advanced']['color_scheme']['desktop']['value'] ?? '';
		$action_type   = $attrs['fblikebutton']['advanced']['action_type']['desktop']['value'] ?? '';
		$show_faces    = $attrs['fblikebutton']['advanced']['show_faces']['desktop']['value'] ?? '';
		$size          = $attrs['fblikebutton']['advanced']['size']['desktop']['value'] ?? '';
		$show_share    = $attrs['fblikebutton']['advanced']['show_share']['desktop']['value'] ?? '';

		$content = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'fb-like',
					'data-href'     => $likeButtonURL,
					'data-width'    => '',
					'data-layout'   => $layout,
					'data-action'   => $action_type,
					'data-colorscheme' => $colorscheme,
					'data-show-faces' => $show_faces,
					'data-size' => $size,
					'data-share' => $show_share,
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => '',
			]
		);

		$root = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'id' => 'fb-root',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => '',
			]
			);

		$parent       = BlockParserStore::get_parent( $block->parsed_block['id'], $block->parsed_block['storeInstance'] );
		$parent_attrs = $parent->attrs ?? [];


		return Module::render(
			[
				// FE only.
				'orderIndex'          => $block->parsed_block['orderIndex'],
				'storeInstance'       => $block->parsed_block['storeInstance'],

				// VB equivalent.
				'attrs'               => $attrs,
				'elements'            => $elements,
				'id'                  => $block->parsed_block['id'],
				'name'                => $block->block_type->name,
				'moduleCategory'      => $block->block_type->category,
				'classnamesFunction'  => [ FacebookLikeButton::class, 'module_classnames' ],
				'stylesComponent'     => [ FacebookLikeButton::class, 'module_styles' ],
				'scriptDataComponent' => [ FacebookLikeButton::class, 'module_script_data' ],
				'parentAttrs'         => $parent_attrs,
				'parentId'            => $parent->id ?? '',
				'parentName'          => $parent->blockName ?? '',
				'children'            => [
					ElementComponents::component(
						[
							'attrs'         => $attrs['module']['decoration'] ?? [],
							'id'            => $block->parsed_block['id'],

							// FE only.
							'orderIndex'    => $block->parsed_block['orderIndex'],
							'storeInstance' => $block->parsed_block['storeInstance'],
						]
					),
					HTMLUtility::render(
						[
							'tag'               => 'div',
							'attributes'        => [
								'class' => 'dotm_fb_like',
							],
							'childrenSanitizer' => 'et_core_esc_previously',
							'children'          => $root . $content,
						]
					),
				],
			]
		);
	}
}
