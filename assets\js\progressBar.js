function initProgressBar(progress_props = {}) {
  const {
    progress_type,
    progress_bg_style,
    progress_bg_color,
    progress_strip_color1,
    progress_strip_color2,
    use_count_for_linear
  } = progress_props

  console.log(progress_props, 'progress_props');

  // Variables to store elements
  const progressType = progress_type;
  const bgStyle = progress_bg_style;
  const normalProgress = document.getElementById("normal-progress");
  const circleProgress = document.getElementById("circle-progress");
  const progressBar = document.getElementById("progress-bar");
  const progressCircle = document.querySelector(".progress");
  const progressText = document.getElementById("progress-percentage");
  const progressCount = document.getElementById("progress-count");
  const countText = document.querySelector(".progress-linear-percentage");

  const selectedType = progressType;
  if (selectedType === "normal") {
    normalProgress.style.display = "block";
    circleProgress.style.display = "none";
  } else if (selectedType === "circle") {
    normalProgress.style.display = "none";
    circleProgress.style.display = "block";
  }


  const selectedStyle = bgStyle;

  // Reset all styles first
  progressBar.className = "progress-bar";
  progressCircle.style.stroke = "";

  // Apply selected style
 if (progress_type === "normal" && selectedStyle === "solid") {
    progressBar.style.background = progress_bg_color;
  } else if (selectedStyle === "striped") {
    progressBar.style.background = `repeating-linear-gradient(45deg, ${progress_strip_color1}, ${progress_strip_color1} 10px, ${progress_strip_color2} 10px, ${progress_strip_color2} 20px)`;
  }

  if(progress_type === "circle" && selectedStyle === "solid") {
    progressCircle.style.stroke = progress_bg_color;
  } else if (progress_type === "circle" && selectedStyle === "gradient-circle") {
    progressCircle.style.stroke = `url(#circle-gradient)`; // Matching animated gradient color
  } else if (progress_type === "circle" && selectedStyle === "striped") {
    progressCircle.style.stroke = `url(#striped-pattern)`; // Matching animated gradient color
  }

  // Update progress bar on scroll
  window.addEventListener("scroll", () => {
    const scrollTop = window.scrollY;
    const documentHeight = document.documentElement.scrollHeight - document.documentElement.clientHeight;
    const scrollPercent = (scrollTop / documentHeight) * 100;

    // Update left-to-right progress bar
    progressBar.style.width = scrollPercent + "%";
    countText.style.left = scrollPercent + "%";

    // Update circular progress bar
    const circleCircumference = 2 * Math.PI * 45; // r = 45
    const strokeDashOffset = circleCircumference - (scrollPercent / 100) * circleCircumference;
    progressCircle.style.strokeDashoffset = strokeDashOffset;

    // Update percentage text
    progressText.textContent = `${Math.round(scrollPercent)}%`;
    progressCount.textContent = `${Math.round(scrollPercent)}%`;
  });

}
