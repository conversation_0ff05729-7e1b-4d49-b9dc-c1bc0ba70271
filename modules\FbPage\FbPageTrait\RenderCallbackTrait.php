<?php
/**
 * FbPage::render_callback()
 *
 * @package MEE\Modules\FbPage
 * @since ??
 */

namespace MEE\Modules\FbPage\FbPageTrait;

if ( ! defined( 'ABSPATH' ) ) {
	die( 'Direct access forbidden.' );
}

// phpcs:disable ET.Sniffs.ValidVariableName.UsedPropertyNotSnakeCase -- WP use snakeCase in \WP_Block_Parser_Block

use ET\Builder\Packages\Module\Module;
use ET\Builder\Framework\Utility\HTMLUtility;
use ET\Builder\FrontEnd\BlockParser\BlockParserStore;
use ET\Builder\Packages\Module\Options\Element\ElementComponents;
use MEE\Modules\FbPage\FbPage;

trait RenderCallbackTrait {

	/**
	 * Static module render callback which outputs server side rendered HTML on the Front-End.
	 *
	 * @since ??
	 * @param array          $attrs    Block attributes that were saved by VB.
	 * @param string         $content  Block content.
	 * @param WP_Block       $block    Parsed block object that being rendered.
	 * @param ModuleElements $elements ModuleElements instance.
	 *
	 * @return string HTML rendered of Static module.
	 */
	public static function render_callback( $attrs, $content, $block, $elements ) {
		$pageurl = $attrs['pageurl']['innerContent']['desktop']['value'] ?? '';
		$tab_selections = $attrs['pageurl']['advanced']['tab_selections']['desktop']['value'] ?? '';
		$pagewidth = $attrs['pageurl']['advanced']['pagewidth']['desktop']['value'] ?? '';
		$pageheight = $attrs['pageurl']['advanced']['pageheight']['desktop']['value'] ?? '';
		$smallheader = $attrs['pageurl']['advanced']['smallheader']['desktop']['value'] ?? 'false';
		$hidecover = $attrs['pageurl']['advanced']['hidecover']['desktop']['value'] ?? 'false';
		$facepile = $attrs['pageurl']['advanced']['facepile']['desktop']['value'] ?? 'true';


		// Image.
		$image_src = $attrs['image']['innerContent']['desktop']['value']['src'] ?? '';
		$image_alt = $attrs['image']['innerContent']['desktop']['value']['alt'] ?? '';
		$image     = HTMLUtility::render(
			[
				'tag'                  => 'img',
				'attributes'           => [
					'src' => $image_src,
					'alt' => $image_alt,
				],
				'attributesSanitizers' => [
					'src' => function ( $value ) {
						$protocols = array_merge( wp_allowed_protocols(), [ 'data' ] ); // Need to add `data` protocol for default image.
						return esc_url( $value, $protocols );
					},
				],
			]
		);

		// Image container.
		$image_container = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'example_static_module__image',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $image,
			]
		);

		// Title.
		$title = $elements->render(
			[
				'attrName' => 'title',
			]
		);

		// Content.
		$content = $elements->render(
			[
				'attrName' => 'content',
			]
		);

		// Content container.
		$content_container = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'example_static_module__content-container',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $title . $content,
			]
		);

		$anchor = HTMLUtility::render(
			[
				'tag'                  => 'a',
				'attributes'           => [
					'href' => 'https://www.facebook.com/facebook',

				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => 'Facebook',
			],
		);

		$blockquote = HTMLUtility::render(
			[
				'tag'               => 'blockquote',
				'attributes'        => [
					'cite' => 'https://www.facebook.com/facebook',
					'class'=>'fb-xfbml-parse-ignore',
				],
				'children'          => $anchor,
				'childrenSanitizer' => 'et_core_esc_previously'
			],
		);


		$parent       = BlockParserStore::get_parent( $block->parsed_block['id'], $block->parsed_block['storeInstance'] );
		$parent_attrs = $parent->attrs ?? [];

		return Module::render(
			[
				// FE only.
				'orderIndex'          => $block->parsed_block['orderIndex'],
				'storeInstance'       => $block->parsed_block['storeInstance'],

				// VB equivalent.
				'attrs'               => $attrs,
				'elements'            => $elements,
				'id'                  => $block->parsed_block['id'],
				'name'                => $block->block_type->name,
				'moduleCategory'      => $block->block_type->category,
				'classnamesFunction'  => [ FbPage::class, 'module_classnames' ],
				'stylesComponent'     => [ FbPage::class, 'module_styles' ],
				'scriptDataComponent' => [ FbPage::class, 'module_script_data' ],
				'parentAttrs'         => $parent_attrs,
				'parentId'            => $parent->id ?? '',
				'parentName'          => $parent->blockName ?? '',
				'children'            => [
					ElementComponents::component(
						[
							'attrs'         => $attrs['module']['decoration'] ?? [],
							'id'            => $block->parsed_block['id'],

							// FE only.
							'orderIndex'    => $block->parsed_block['orderIndex'],
							'storeInstance' => $block->parsed_block['storeInstance'],
						]
					),
					HTMLUtility::render(
						[
							'tag'               => 'div',
							'attributes'        => [
								'class' => 'fb-page',
								'data-href' => $pageurl,
								'data-tabs' => $tab_selections,
								'data-width' => $pagewidth,
								'data-height' => $pageheight,
								'data-small-header' => $smallheader,
								'data-adapt-container-width' => 'true',
								'data-hide-cover' => $hidecover,
								'data-show-facepile' => $facepile,
							],
							'children'          => $blockquote,
							'childrenSanitizer' => 'et_core_esc_previously'
						],
					),
				],
			]
		);
	}
}
