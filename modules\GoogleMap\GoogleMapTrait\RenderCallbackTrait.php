<?php
/**
 * GoogleMap::render_callback()
 *
 * @package MEE\Modules\GoogleMap
 * @since ??
 */

namespace MEE\Modules\GoogleMap\GoogleMapTrait;

if ( ! defined( 'ABSPATH' ) ) {
	die( 'Direct access forbidden.' );
}

// phpcs:disable ET.Sniffs.ValidVariableName.UsedPropertyNotSnakeCase -- WP use snakeCase in \WP_Block_Parser_Block

use ET\Builder\Packages\Module\Module;
use ET\Builder\Framework\Utility\HTMLUtility;
use ET\Builder\FrontEnd\BlockParser\BlockParserStore;
use ET\Builder\Packages\Module\Options\Element\ElementComponents;
use MEE\Modules\GoogleMap\GoogleMap;

trait RenderCallbackTrait {

	/**
	 * Static module render callback which outputs server side rendered HTML on the Front-End.
	 *
	 * @since ??
	 * @param array          $attrs    Block attributes that were saved by VB.
	 * @param string         $content  Block content.
	 * @param WP_Block       $block    Parsed block object that being rendered.
	 * @param ModuleElements $elements ModuleElements instance.
	 *
	 * @return string HTML rendered of Static module.
	 */
	public static function render_callback( $attrs, $content, $block, $elements ) {
		// Image.

		$address = isset( $attrs['address']['innerContent']['desktop']['value'] ) ? $attrs['address']['innerContent']['desktop']['value'] : '';
		$mapType = isset( $attrs['map']['decoration']['type']['desktop']['value'] ) ? $attrs['map']['decoration']['type']['desktop']['value'] : '';
		$zoom    = isset( $attrs['map']['decoration']['zoom']['desktop']['value'] ) ? $attrs['map']['decoration']['zoom']['desktop']['value'] : '';

		$finalAddress = str_replace( ' ', '+', $address );
		$finalMap     = str_replace( ' ', '+', $mapType );

		$iframe = HTMLUtility::render(
			[
				'tag'               => 'iframe',
				'attributes'        => [
					'class'	=> 'dotm_map_inner',
					'frameBorder' => '0',
					'scrolling'   => 'no',
					'src' 	=> "https://maps.google.com/maps?width=600&height=400&hl=en&q=$finalAddress&t=$finalMap&z=$zoom&ie=UTF8&iwloc=B&output=embed",
					'title'       => $finalAddress,
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => '',
			]
		);

		$parent       = BlockParserStore::get_parent( $block->parsed_block['id'], $block->parsed_block['storeInstance'] );
		$parent_attrs = $parent->attrs ?? [];

		return Module::render(
			[
				// FE only.
				'orderIndex'          => $block->parsed_block['orderIndex'],
				'storeInstance'       => $block->parsed_block['storeInstance'],

				// VB equivalent.
				'attrs'               => $attrs,
				'elements'            => $elements,
				'id'                  => $block->parsed_block['id'],
				'name'                => $block->block_type->name,
				'moduleCategory'      => $block->block_type->category,
				'classnamesFunction'  => [ GoogleMap::class, 'module_classnames' ],
				'stylesComponent'     => [ GoogleMap::class, 'module_styles' ],
				'scriptDataComponent' => [ GoogleMap::class, 'module_script_data' ],
				'parentAttrs'         => $parent_attrs,
				'parentId'            => $parent->id ?? '',
				'parentName'          => $parent->blockName ?? '',
				'children'            => [
					ElementComponents::component(
						[
							'attrs'         => $attrs['module']['decoration'] ?? [],
							'id'            => $block->parsed_block['id'],

							// FE only.
							'orderIndex'    => $block->parsed_block['orderIndex'],
							'storeInstance' => $block->parsed_block['storeInstance'],
						]
					),
					HTMLUtility::render(
						[
							'tag'               => 'div',
							'attributes'        => [
								'class' => 'dotm_map_wrapper',
							],
							'childrenSanitizer' => 'et_core_esc_previously',
							'children'          => $iframe,
						]
					),
				],
			]
		);
	}
}
