<?php

/**
 * Module: ProfileOptimazerItem class.
 *
 * @package MEE\Modules\ProfileOptimazerItem
 * @since ??
 */

namespace MEE\Modules\ProfileOptimazerItem;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

use ET\Builder\Framework\DependencyManagement\Interfaces\DependencyInterface;
use ET\Builder\Packages\ModuleLibrary\ModuleRegistration;

/**
 * `ProfileOptimazerItem` is consisted of functions used for ProfileOptimazerItem such as Front-End rendering, REST API Endpoints etc.
 *
 * This is a dependency class and can be used as a dependency for `DependencyTree`.
 *
 * @since ??
 */
class ProfileOptimazerItem implements DependencyInterface
{
	use ProfileOptimazerItemTrait\RenderCallbackTrait;

	/**
	 * Loads `ProfileOptimazerItem` and registers Front-End render callback and REST API Endpoints.
	 *
	 * @since ??
	 *
	 * @return void
	 */
	public function load()
	{
		$module_json_folder_path = DIVIOPTIMIZER_MODULES_JSON_PATH . 'ProfileOptimazerItem/';

		add_action(
			'init',
			function () use ($module_json_folder_path) {
				ModuleRegistration::register_module(
					$module_json_folder_path,
					[
						'render_callback' => [ProfileOptimazerItem::class, 'render_callback'],
					]
				);
			}
		);
	}
}
