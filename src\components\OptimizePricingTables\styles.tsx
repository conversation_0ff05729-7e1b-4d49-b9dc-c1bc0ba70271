// External dependencies.
import React, { ReactElement } from 'react';

// Divi dependencies.
import {
  StyleContainer,
  StylesProps,
  CssStyle,
  CommonStyle,
} from '@divi/module';

// Local dependencies.
import { OptimizePricingTablesAttrs } from './types';
import { cssFields } from './custom-css';
import { iconFontDeclaration } from '../OptimizePricingTablesItem/style-declarations';

/**
 * OptimizePricingTables's style components.
 *
 * @since ??
 */
 export const ModuleStyles = ({
  attrs,
  elements,
  settings,
  orderClass,
  mode,
  state,
  noStyleTag,
}: StylesProps<OptimizePricingTablesAttrs>): ReactElement => {
  const headerContainerSelector = `${orderClass} .dotm_optimize_pricing_tables_item_header`;
  return (
    <StyleContainer mode={mode} state={state} noStyleTag={noStyleTag}>
      {/* Module */}
      {elements.style({
        attrName: 'module',
        styleProps: {
          disabledOn: {
            disabledModuleVisibility: settings?.disabledModuleVisibility,
          },
          advancedStyles: [
            {
              componentName: 'divi/text',
              props: {
                selector: headerContainerSelector,
                attr: attrs?.headingWrapper?.advanced?.text,
              }
            },
            {
              componentName: 'divi/common',
              props: {
                attr: attrs?.image?.decoration?.gap,
                property: "gap"
              }
            },
          ]
        },
      })}

      {/* Title */}
      {elements.style({
        attrName: 'title',
      })}

      {/* subTitle */}
      {elements.style({
        attrName: 'subTitle',
      })}

      {/* currency */}
      {elements.style({
        attrName: 'currency',
      })}

      {/* priceWrapper */}
      {elements.style({
        attrName: 'priceWrapper',
      })}

      {/* frequency */}
      {elements.style({
        attrName: 'frequency',
      })}

      {/* Content */}
      {elements.style({
        attrName: 'content',
      })}

      {/* Icon */}
      {elements.style({
        attrName: 'icon',
        styleProps: {
          advancedStyles: [
            {
              componentName: "divi/common",
              props: {
                attr:attrs?.icon?.innerContent ?? {},
                declarationFunction:iconFontDeclaration,
              }
            },
            {
              componentName: "divi/common",
              props: {
                attr: attrs?.icon?.advanced?.color,
                property:"color"
              }
            },
            {
              componentName: "divi/common",
              props: {
                attr:attrs?.icon?.advanced?.size,
                property:"font-size"
              }
            }
          ]
        }
      })}

      {/* image */}
      {elements.style({
        attrName: 'image',
        styleProps: {
          advancedStyles: [
            {
              componentName: "divi/common",
              props: {
                attr: attrs?.image?.decoration?.img_width,
                property:"width",
              }
            },
            {
              componentName: "divi/common",
              props: {
                attr: attrs?.image?.decoration?.img_height,
                property:"height",
              }
            }
          ]
        }
      })}

      {/* headingWrapper */}
      {elements.style({
        attrName: 'headingWrapper',
      })}


      {/*
       * We need to add CssStyle at the very bottom of other components
       * so that custom css can override module styles till we find a
       * more elegant solution.
       */}
      <CssStyle
        selector={orderClass}
        attr={attrs.css}
        cssFields={cssFields}
      />
      <CssStyle
        selector={orderClass}
        attr={attrs.css}
        cssFields={cssFields}
      />
    </StyleContainer>
  )
};

