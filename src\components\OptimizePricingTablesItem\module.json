{"name": "dotm/optimize-pricing-tables-item", "d4Shortcode": "", "title": "Optimize Pricing Tables Item", "titles": "Optimize Pricing Tables Items", "category": "child-module", "attributes": {"module": {"type": "object", "selector": "{{selector}}", "default": {"meta": {"adminLabel": {"desktop": {"value": "Optimize Pricing Tables Item"}}}, "decoration": {"sizing": {"desktop": {"value": {"width": "320px"}}}, "border": {"desktop": {"value": {"styles": {"all": {"width": "1px", "style": "solid", "color": "#e0e0e0"}}}}}}}, "styleProps": {"border": {"important": true}, "spacing": {"important": true}}}, "featureText": {"type": "object", "selector": ".dotm_optimize_pricing_tables {{selector}} .dotm_optimize_pricing_tables_item_featureText", "default": {"decoration": {"font": {"font": {"desktop": {"value": {"weight": "600", "size": "14px", "color": "#fff", "lineHeight": "1.7em"}}}}, "background": {"desktop": {"value": {"color": "#000"}}}, "spacing": {"desktop": {"value": {"padding": {"top": "7px", "bottom": "7px", "left": "15px", "right": "15px"}}}}}, "advanced": {"highlight": {"desktop": {"value": "1"}}, "zIndex": {"desktop": {"value": "3"}}, "badgePlacement": {"desktop": {"value": "-20px"}}}}, "tagName": "div", "attributes": {"class": "dotm_optimize_pricing_tables_item_featureText"}, "inlineEditor": "plainText", "elementType": "text", "childrenSanitizer": "et_core_esc_previously"}, "headingWrapper": {"type": "object", "selector": ".dotm_optimize_pricing_tables {{selector}} .dotm_optimize_pricing_tables_item_header", "defaultPrintedStyle": {"decoration": {"spacing": {"desktop": {"value": {"padding": {"top": "30px", "bottom": "30px", "left": "20px", "right": "20px"}}}}, "background": {"desktop": {"value": {"color": "#d1d5db"}}}}, "advanced": {"text": {"text": {"desktop": {"value": {"orientation": "center"}}}}}}}, "title": {"type": "object", "selector": ".dotm_optimize_pricing_tables {{selector}} .dotm_optimize_pricing_tables_item_title", "defaultPrintedStyle": {"decoration": {"font": {"font": {"desktop": {"value": {"headingLevel": "h2", "weight": "600"}}}}}}, "styleProps": {"font": {"important": {"font": {"desktop": {"value": {"color": true}}}}}}, "tagName": "h2", "attributes": {"class": "dotm_optimize_pricing_tables_item_title"}, "inlineEditor": "plainText", "elementType": "heading", "childrenSanitizer": "et_core_esc_previously"}, "subTitle": {"type": "object", "selector": ".dotm_optimize_pricing_tables {{selector}} .dotm_optimize_pricing_tables_item_subTitle", "defaultPrintedStyle": {"decoration": {"font": {"font": {"desktop": {"value": {"headingLevel": "h4"}}}}}}, "tagName": "h4", "attributes": {"class": "dotm_optimize_pricing_tables_item_subTitle"}, "inlineEditor": "plainText", "elementType": "heading", "childrenSanitizer": "et_core_esc_previously"}, "priceContainer": {"type": "object", "selector": ".dotm_optimize_pricing_tables {{selector}} .dotm_optimize_pricing_tables_item_price_section", "default": {"decoration": {"spacing": {"desktop": {"value": {"padding": {"top": "40px", "bottom": "40px", "left": "20px", "right": "20px"}}}}, "alignment": {"desktop": {"value": "center"}}, "border": {"desktop": {"value": {"styles": {"bottom": {"width": "1px", "style": "solid", "color": "#e0e0e0"}}}}}}}}, "priceWrapper": {"type": "object", "selector": ".dotm_optimize_pricing_tables {{selector}} .dotm_optimize_pricing_tables_item_price_wrapper", "defaultPrintedStyle": {"decoration": {"font": {"font": {"desktop": {"value": {"weight": "700", "size": "72px", "lineHeight": "1em"}}}}}}, "styleProps": {"font": {"important": {"font": {"desktop": {"value": {"color": true}}}}}}}, "currency": {"type": "object", "selector": ".dotm_optimize_pricing_tables {{selector}} .dotm_optimize_pricing_tables_item_currency", "defaultPrintedStyle": {"decoration": {"font": {"font": {"desktop": {"value": {"weight": "400"}}}}}, "styleProps": {"font": {"important": {"font": {"desktop": {"value": {"color": true}}}}}}}, "tagName": "span", "attributes": {"class": "dotm_optimize_pricing_tables_item_currency"}, "inlineEditor": "plainText", "elementType": "text", "childrenSanitizer": "et_core_esc_previously"}, "frequency": {"type": "object", "selector": ".dotm_optimize_pricing_tables {{selector}} .dotm_optimize_pricing_tables_item_frequency", "defaultPrintedStyle": {"decoration": {"font": {"font": {"desktop": {"value": {"color": "#6b7280", "size": "16px"}}}}}}, "styleProps": {"font": {"important": {"font": {"desktop": {"value": {"color": true}}}}}}, "tagName": "div", "attributes": {"class": "dotm_optimize_pricing_tables_item_frequency"}, "inlineEditor": "plainText", "elementType": "heading", "childrenSanitizer": "et_core_esc_previously"}, "price": {"type": "object", "selector": ".dotm_optimize_pricing_tables {{selector}} .dotm_optimize_pricing_tables_item_price", "tagName": "span", "attributes": {"class": "dotm_optimize_pricing_tables_item_price"}, "inlineEditor": "plainText", "elementType": "text", "childrenSanitizer": "et_core_esc_previously"}, "button": {"type": "object", "selector": ".dotm_optimize_pricing_tables {{selector}} .dotm_optimize_pricing_tables_item_button", "defaultPrintedStyle": {"advanced": {"type": {"desktop": {"value": "inline-block"}}, "buttonAlignment": {"desktop": {"value": "center"}}}}, "tagName": "a", "attributes": {"class": "dotm_optimize_pricing_tables_item_button"}, "inlineEditor": "plainText", "childrenSanitizer": "et_core_esc_previously"}, "content": {"type": "object", "selector": ".dotm_optimize_pricing_tables {{selector}} .dotm_optimize_pricing_tables_item_description", "tagName": "div", "attributes": {"class": "dotm_optimize_pricing_tables_item_description"}, "inlineEditor": "richText", "childrenSanitizer": "et_core_esc_previously"}, "icon": {"type": "object", "selector": ".dotm_optimize_pricing_tables {{selector}} .dotm_optimize_pricing_tables_item_icon", "defaultPrintedStyle": {"innerContent": {"desktop": {"value": {"unicode": "&#x39;", "type": "divi", "weight": "400"}}}, "advanced": {"color": {"desktop": {"value": "#000000"}}, "size": {"desktop": {"value": "28px"}}}}, "styleProps": {"background": {"important": true}, "spacing": {"important": true}, "border": {"important": true}}}, "image": {"type": "object", "selector": ".dotm_optimize_pricing_tables {{selector}} .dotm_optimize_pricing_tables_item_image", "defaultPrintedStyle": {"decoration": {"img_width": {"desktop": {"value": "100px"}}, "img_height": {"desktop": {"value": "auto"}}}}, "elementType": "image", "tagName": "img", "inlineEditor": "image", "childrenSanitizer": "et_core_esc_previously", "allowHtml": true, "attributes": {"class": "dotm_optimize_pricing_tables_item_image"}}}, "customCssFields": {"contentContainer": {"subName": "contentContainer", "selectorSuffix": " .example_child_module__content-container"}, "title": {"subName": "title", "selectorSuffix": " .example_child_module__title"}, "content": {"subName": "content", "selectorSuffix": " .example_child_module__content"}, "icon": {"subName": "icon", "selectorSuffix": " .example_child_module__icon.et-pb-icon"}}}