// External dependencies.
import React, { ReactElement, useState } from 'react';

// WordPress dependencies.
import { __ } from '@wordpress/i18n';

// Divi dependencies.
import {
  AnimationGroup,
  BorderGroup,
  BoxShadowGroup,
  FiltersGroup,
  FontGroup,
  FontBodyGroup,
  SizingGroup,
  SpacingGroup,
  TextGroup,
  TransformGroup,
  BackgroundGroup,
} from '@divi/module';
import {
  type Module,
} from '@divi/types';
import { GroupContainer, GroupTabs } from '@divi/modal';

// Local dependencies.
import { BeforeAfterSliderAttrs } from "./types";

export const SettingsDesign = ({
  defaultSettingsAttrs,
}: Module.Settings.Panel.Props<BeforeAfterSliderAttrs>): ReactElement => {
  const [activeTab, setAcivteTab] = useState('before');
  return (
    <React.Fragment>
      <GroupContainer
        id="imageStyle"
        title={__('Image Style', 'divi-optimaizer-modules')}>
        <GroupTabs
          showLabel={true}
          activeTab={activeTab}
          showIcon={false}
          tabs={{
            'before': {
              "label": "Before Image",
              "icon": "iconNameFor2D" // Replace "iconNameFor2D" with the actual icon identifier for the 2D tab
            },
            'after': {
              "label": "After Image",
              "icon": "iconNameForBG" // Replace "iconNameForBG" with the actual icon identifier for the BG tab
            }

          }}
          onClick={(e: React.MouseEvent<HTMLButtonElement>) => {
            let target = e.target as HTMLButtonElement;
            let parent = target.parentElement;
            setAcivteTab(
              target.value ? target.value : (parent as HTMLButtonElement).value
            );
          }}
        />

        <BorderGroup
          attrName="before_image.decoration.border"
          grouped={false}
          visible={activeTab === 'before'}
        />
        {/* <SpacingGroup
          attrName="before_image.decoration.spacing"
          grouped={false}
          visible={activeTab === 'before'}
        /> */}

        <BorderGroup
          attrName="after_image.decoration.border"
          grouped={false}
          visible={activeTab === 'after'}
        />
        {/* <SpacingGroup
          attrName="after_image.decoration.spacing"
          grouped={false}
          visible={activeTab === 'after'}
        /> */}

      </GroupContainer>

      <GroupContainer
        id='label_styles'
        title={__('Label Styles', 'divi-optimaizer-modules')}
      >
        <GroupTabs
          showLabel={true}
          activeTab={activeTab}
          showIcon={false}
          tabs={{
            'before': {
              "label": "Before Label",
              "icon": "iconNameFor2D" // Replace "iconNameFor2D" with the actual icon identifier for the 2D tab
            },
            'after': {
              "label": "After Label",
              "icon": "iconNameForBG" // Replace "iconNameForBG" with the actual icon identifier for the BG tab
            }

          }}
          onClick={(e: React.MouseEvent<HTMLButtonElement>) => {
            let target = e.target as HTMLButtonElement;
            let parent = target.parentElement;
            setAcivteTab(
              target.value ? target.value : (parent as HTMLButtonElement).value
            );
          }}
        />
        <FontGroup
          attrName='before_label.decoration.font'
          grouped={false}
          visible={activeTab === 'before'}
          fields={{
            textAlign: {
              render: false
            }
          }}
        />
        <FontGroup
          attrName='after_label.decoration.font'
          grouped={false}
          visible={activeTab === 'after'}
          fields={{
            textAlign: {
              render: false
            }
          }}
        />
        <BackgroundGroup
          attrName='before_label.decoration.background'
          defaultGroupAttr={defaultSettingsAttrs?.before_label?.decoration?.background}
          grouped={false}
          visible={activeTab === 'before'}
        />
        <BackgroundGroup
          attrName='after_label.decoration.background'
          defaultGroupAttr={defaultSettingsAttrs?.after_label?.decoration?.background}
          grouped={false}
          visible={activeTab === 'after'}
        />
        <BorderGroup
          attrName='before_label.decoration.border'
          grouped={false}
          visible={activeTab === 'before'}
        />
        <BorderGroup
          attrName='after_label.decoration.border'
          grouped={false}
          visible={activeTab === 'after'}
        />
        <SpacingGroup
          attrName='before_label.decoration.spacing'
          defaultGroupAttr={defaultSettingsAttrs?.before_label?.decoration?.spacing}
          grouped={false}
          visible={activeTab === 'before'}
        />
        <SpacingGroup
          attrName='after_label.decoration.spacing'
          defaultGroupAttr={defaultSettingsAttrs?.after_label?.decoration?.spacing}
          grouped={false}
          visible={activeTab === 'after'}
        />
      </GroupContainer>

      <GroupContainer
        id='beforeImageFilter'
        title={__('Image Filters', 'divi-optimaizer-modules')}
      >
        <GroupTabs
          showLabel={true}
          activeTab={activeTab}
          showIcon={false}
          tabs={{
            'before': {
              "label": "Before Image",
              "icon": "iconNameFor2D" // Replace "iconNameFor2D" with the actual icon identifier for the 2D tab
            },
            'after': {
              "label": "After Image",
              "icon": "iconNameForBG" // Replace "iconNameForBG" with the actual icon identifier for the BG tab
            }

          }}
          onClick={(e: React.MouseEvent<HTMLButtonElement>) => {
            let target = e.target as HTMLButtonElement;
            let parent = target.parentElement;
            setAcivteTab(
              target.value ? target.value : (parent as HTMLButtonElement).value
            );
          }}
        />

        <FiltersGroup
          attrName="before_image.decoration.filters"
          grouped={false}
          visible={activeTab === 'before'}
        />
        <FiltersGroup
          attrName="after_image.decoration.filters"
          grouped={false}
          visible={activeTab === 'after'}
        />
      </GroupContainer>
      <SizingGroup defaultGroupAttr={defaultSettingsAttrs?.module?.decoration?.sizing} />
      <SpacingGroup />
      <BorderGroup />
      <BoxShadowGroup />
      <FiltersGroup />
      <TransformGroup />
      <AnimationGroup />
    </React.Fragment>
  )
}
