<?php

/**
 * OptimizeIconItem::module_styles().
 *
 * @package Builder\Packages\ModuleLibrary
 * @since ??
 */

namespace MEE\Modules\OptimizeIconItem\OptimizeIconItemTrait;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

use ET\Builder\FrontEnd\Module\Style;
use ET\Builder\Packages\Module\Options\Text\TextStyle;
use ET\Builder\Packages\Module\Options\Css\CssStyle;
use ET\Builder\Packages\Module\Layout\Components\StyleCommon\CommonStyle;
use ET\Builder\Packages\ModuleLibrary\ModuleRegistration;
use MEE\Modules\OptimizeIconItem\OptimizeIconItem;

trait ModuleStylesTrait
{

	use CustomCssTrait;
	use StyleDeclarationTrait;

	/**
	 *OptimizeIconItem's style components.
	 *
	 * This function is equivalent of JS function ModuleStyles located in
	 * src/components/OptimizeIconItem/styles.tsx.
	 *
	 * @param array $args {
	 *     An array of arguments.
	 *
	 * @type string $id Module ID. In VB, the ID of module is UUIDV4. In FE, the ID is order index.
	 * @type string $name Module name.
	 * @type string $attrs Module attributes.
	 * @type string $parentAttrs Parent attrs.
	 * @type string $orderClass Selector class name.
	 * @type string $parentOrderClass Parent selector class name.
	 * @type string $wrapperOrderClass Wrapper selector class name.
	 * @type string $settings Custom settings.
	 * @type string $state Attributes state.
	 * @type string $mode Style mode.
	 * @type ModuleElements $elements ModuleElements instance.
	 * }
	 * @since ??
	 */
	public static function module_styles($args)
	{
		$attrs        = $args['attrs'] ?? [];
		$order_class  = $args['orderClass'];
		$elements     = $args['elements'];
		$settings     = $args['settings'] ?? [];
		$parent_attrs = $args['parentAttrs'] ?? [];

		$parent_default_attributes = ModuleRegistration::get_default_attrs('dotm/optimize-icon-list');
		$parent_attrs_with_default = array_replace_recursive($parent_default_attributes, $parent_attrs);

		$imgContainerSelector = "{$order_class} .dotm_optimize_icon_item_image_container";
		$tooltipTextAfterSelector = "{$order_class} .dotm_optimize_icon_item_container .dotm_optimize_icon_item_tooltip_text::after";
		$tooltipTextSelector = "{$order_class} .dotm_optimize_icon_item_container .dotm_optimize_icon_item_tooltip_text";
		$itemContainerSelector = "{$order_class} .dotm_optimize_icon_item_container";

		$tooltipPosition = $attrs['tooltip']['decoration']['position']['desktop']['value'] ?? '';
		$toolTipBgColor = $attrs['tooltip']['decoration']['bgColor']['desktop']['value'] ?? 'na';


		Style::add(
			[
				'id'            => $args['id'],
				'name'          => $args['name'],
				'orderIndex'    => $args['orderIndex'],
				'storeInstance' => $args['storeInstance'],
				'styles'        => [
					// Module.
					$elements->style(
						[
							'attrName'   => 'module',
							'styleProps' => [
								'disabledOn'     => [
									'disabledModuleVisibility' => $settings['disabledModuleVisibility'] ?? null,
								],
								'advancedStyles' => [
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $tooltipTextSelector,
											'attr'     => $attrs['tooltip']['decoration']['position'],
											'property' => ($tooltipPosition === "right" ? "left: 110%; top: 50%; transform: translateY(-50%);" : ($tooltipPosition === "left" ? "right: 110%; top: 50%; transform: translateY(-50%);" : ($tooltipPosition === "top" ? "bottom: 110%; left: 50%; transform: translateX(-50%);" : ($tooltipPosition === "bottom" ? "top: 110%; left: 50%; transform: translateX(-50%);" : ''))))
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $tooltipTextAfterSelector,
											'attr'     => $attrs['tooltip']['decoration']['position'],
											'property' => ($tooltipPosition === "right" ? "right: 100%; top: 50%;  transform: translateY(-50%);" : ($tooltipPosition === "left" ? "left: 100%; top: 50%; transform: translateY(-50%) rotateY(180deg);" : ($tooltipPosition === "top" ? "top: 88%; left: 50%; transform: translateX(-50%); rotate: -90deg;" : ($tooltipPosition === "bottom" ? "bottom: 88%; left: 50%; transform: translateX(-50%); rotate: 90deg;" : ''))))
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $tooltipTextSelector,
											'attr'     => $attrs['tooltip']['decoration']['bgColor'] ?? $parent_attrs_with_default['tooltip']['decoration']['bgColor'],
											'property' => "background-color: {$toolTipBgColor} !important;"
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $tooltipTextAfterSelector,
											'attr'     => $attrs['tooltip']['decoration']['bgColor'] ?? $parent_attrs_with_default['tooltip']['decoration']['bgColor'],
											'property' => "border-color: transparent {$toolTipBgColor} transparent transparent !important;"
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $itemContainerSelector,
											'attr'     => $attrs['title']['advanced']['gap'] ?? $parent_attrs_with_default['title']['advanced']['gap'] ?? [],
											'property' => "gap"
										]
									],

								]
							],
						]
					),

					// Title.
					$elements->style(
						[
							'attrName' => 'title',
						]
					),

					// tooltip.
					$elements->style(
						[
							'attrName' => 'tooltip',
						]
					),

					// image_container.
					$elements->style(
						[
							'attrName' => 'image_container',
						]
					),

					// image.
					$elements->style(
						[
							'attrName' => 'image',
							'styleProps' => [
								'advancedStyles' => [
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $imgContainerSelector,
											'attr'                => $attrs['image']['advanced']['size'] ?? $parent_attrs_with_default['image']['advanced']['size'] ?? [],
											'property'              => "width",
										]
									],
								]
							]

						]
					),

					// Icon.
					$elements->style(
						[
							'attrName'   => 'icon',
							'styleProps' => [
								'advancedStyles' => [
									[
										'componentName' => 'divi/common',
										'props'         => [
											'attr'                => $attrs['icon']['innerContent'] ?? $parent_attrs_with_default['icon']['innerContent'] ?? [],
											'declarationFunction' => [OptimizeIconItem::class, 'icon_font_declaration'],
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'attr'     => $attrs['icon']['advanced']['color'] ?? $parent_attrs_with_default['icon']['advanced']['color'] ?? [],
											'property' => 'color',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'attr'     => $attrs['icon']['advanced']['size'] ?? $parent_attrs_with_default['icon']['advanced']['size'] ?? [],
											'property' => 'font-size',
										]
									],
								]
							]
						]
					),

					/*
					 * We need to add CssStyle at the very bottom of other
					 * components so that custom css can override module styles
					 * till we find a more elegant solution.
					 */
					CssStyle::style(
						[
							'selector'  => $args['orderClass'],
							'attr'      => $attrs['css'] ?? [],
							'cssFields' => self::custom_css(),
						]
					),

					// ATTENTION: The code is intentionally added and commented in FE only as an example of expected value format.
					// If you have custom style processing, the style output should be passed as an `array` of style declarations
					// to the `styles` property of the `Style::add` method. For example:
					// [
					// 	[
					// 		'atRules'     => false,
					// 		'selector'    => $icon_selector,
					// 		'declaration' => 'color: red;'
					// 	],
					// 	[
					// 		'atRules'     => '@media only screen and (max-width: 767px)',
					// 		'selector'    => $icon_selector,
					// 		'declaration' => 'color: green;'
					// 	],
					// ],

					// The code below is an example of how to use the `CssStyle::style` method to generate CSS style.
					CssStyle::style(
						[
							'selector'  => $args['orderClass'],
							'attr'      => $attrs['css'] ?? [],
							'cssFields' => self::custom_css(),
						]
					),
				],
			]
		);
	}
}
