import React, {
  Fragment,
  ReactElement,
} from 'react';

import {
  ModuleScriptDataProps,
} from '@divi/module';
import { BeforeAfterSliderAttrs } from './types';


/**
 * BeforeAfterSlider's script data component.
 *
 * @since ??
 *
 * @param {ModuleScriptDataProps<BeforeAfterSliderAttrs>} props React component props.
 *
 * @returns {ReactElement}
 */
export const ModuleScriptData = ({
  elements,
}: ModuleScriptDataProps<BeforeAfterSliderAttrs>): ReactElement => (
  <Fragment>
    {elements.scriptData({
      attrName: 'module',
    })}
  </Fragment>
);

