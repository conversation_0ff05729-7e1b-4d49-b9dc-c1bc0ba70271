<?php

/**
 * Module: Static Module class.
 *
 * @package MEE\Modules\TwitterTimeline
 * @since ??
 */

namespace MEE\Modules\TwitterTimeline;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

use ET\Builder\Framework\DependencyManagement\Interfaces\DependencyInterface;
use ET\Builder\Packages\ModuleLibrary\ModuleRegistration;


/**
 * `TwitterTimeline` is consisted of functions used for Static Module such as Front-End rendering, REST API Endpoints etc.
 *
 * This is a dependency class and can be used as a dependency for `DependencyTree`.
 *
 * @since ??
 */
class TwitterTimeline implements DependencyInterface
{
	use TwitterTimelineTrait\RenderCallbackTrait;
	use TwitterTimelineTrait\ModuleClassnamesTrait;
	use TwitterTimelineTrait\ModuleStylesTrait;
	use TwitterTimelineTrait\ModuleScriptDataTrait;

	/**
	 * Loads `TwitterTimeline` and registers Front-End render callback and REST API Endpoints.
	 *
	 * @since ??
	 *
	 * @return void
	 */
	public function load()
	{
		$module_json_folder_path = DIVIOPTIMIZER_MODULES_JSON_PATH . 'TwitterTimeline/';

		add_action(
			'init',
			function () use ($module_json_folder_path) {
				ModuleRegistration::register_module(
					$module_json_folder_path,
					[
						'render_callback' => [TwitterTimeline::class, 'render_callback'],
					]
				);
			}
		);
	}
}
