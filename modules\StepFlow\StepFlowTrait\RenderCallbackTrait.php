<?php
/**
 * StepFlow::render_callback()
 *
 * @package MEE\Modules\StepFlow
 * @since ??
 */

namespace MEE\Modules\StepFlow\StepFlowTrait;

if ( ! defined( 'ABSPATH' ) ) {
	die( 'Direct access forbidden.' );
}

// phpcs:disable ET.Sniffs.ValidVariableName.UsedPropertyNotSnakeCase -- WP use snakeCase in \WP_Block_Parser_Block

use ET\Builder\Packages\Module\Module;
use ET\Builder\Framework\Utility\HTMLUtility;
use ET\Builder\FrontEnd\BlockParser\BlockParserStore;
use ET\Builder\Packages\IconLibrary\IconFont\Utils;
use ET\Builder\Packages\Module\Options\Element\ElementComponents;
use MEE\Modules\StepFlow\StepFlow;

trait RenderCallbackTrait {

	/**
	 * StepFlow render callback which outputs server side rendered HTML on the Front-End.
	 *
	 * @since ??
	 * @param array          $attrs    Block attributes that were saved by VB.
	 * @param string         $content  Block content.
	 * @param WP_Block       $block    Parsed block object that being rendered.
	 * @param ModuleElements $elements ModuleElements instance.
	 *
	 * @return string HTML rendered of StepFlow.
	 */
	public static function render_callback( $attrs, $content, $block, $elements ) {

		$use_image = $attrs['image']['advanced']['use']['desktop']['value'] ?? '';
		$use_icon = $attrs['icon']['advanced']['use']['desktop']['value'] ?? '';
		$use_button = $attrs['button']['advanced']['use']['desktop']['value'] ?? '';
		$step_align_class = $attrs['step']['decoration']['alignment']['desktop']['value'] ?? '';
		$use_direction = $attrs['direction']['advanced']['use']['desktop']['value'] ?? '';
		$icon_value = $attrs['icon']['innerContent']['desktop']['value'] ?? [];
		$button_link = $attrs['button']['desktop']['value']['url'] ?? '';
		$button_target = $attrs['button']['desktop']['value']['target'] ?? '';
		$use_image_mask = $attrs['image']['advanced']['use_mask']['desktop']['value'] ?? 'off';
		$shape_name = $attrs['image']['advanced']['shape_name']['desktop']['value'] ?? '';
		$use_hover_effect = $attrs['image']['advanced']['use_hover_effect']['desktop']['value'] ?? '';

		$hover_effect_name = $use_hover_effect === 'on' ? ($attrs['image']['advanced']['hover_effect_name']['desktop']['value'] ?? '') : '';

		// Image.
		$image = $elements->render(
			[
				'attrName' => 'image',
			]
		);

		$mask_container = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => "dotm_step_flow_image_mask {$shape_name}",
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $image,
			]
		);

		// Image container.
		$image_container = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => "dotm_step_flow_image_container {$hover_effect_name}",
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => ($use_image_mask === 'on' ? $mask_container : $image) ,
			]
		);

		

		// Icon.
		$icon       = HTMLUtility::render(
			[
				'tag'               => 'span',
				'attributes'        => [
					'class' => 'dotm_step_flow_icon',
				],
				'childrenSanitizer' => 'esc_html',
				'children'          => Utils::process_font_icon($icon_value),
			]
		);

		$icon_container       = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'dotm_step_flow_icon_container',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $icon,
			]
		);

		// Title.
		$title = $elements->render(
			[
				'attrName' => 'title',
			]
		);

		// Content.
		$content = $elements->render(
			[
				'attrName' => 'content',
			]
		);

		$contents       = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'dotm_step_flow_contents',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $title . $content,
			]
		);

		// Button.
		$button = $elements->render(
			[
				'attrName' => 'button',
				'attributes' => [
					'class' => 'dotm_step_flow_button',
					'href' => $button_link,
					'target' => $button_target === 'on' ? '_blank' : '_self',

				]
			]
		);

		$button_container       = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'dotm_step_flow_button_container',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $button,
			]
		);

		

		// badge.
		$badge = $elements->render(
			[
				'attrName' => 'step',
				'attributes' => [
					'class' => "dotm_step_flow_step domt_step_align_{$step_align_class}",
				],
			]
		);

		// Content container.
		$inner_container = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'dotm_step_flow_inner',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $badge . ($use_image === 'on' ? $image_container : '') . ($use_icon === 'on' ? $icon_container : '') . $contents . ($use_button === 'on' ? $button_container : ''),
			]
		);

		$direction_container       = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'dotm_step_flow_direction',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => '',
			]
		);

		$parent       = BlockParserStore::get_parent( $block->parsed_block['id'], $block->parsed_block['storeInstance'] );
		$parent_attrs = $parent->attrs ?? [];

		return Module::render(
			[
				// FE only.
				'orderIndex'          => $block->parsed_block['orderIndex'],
				'storeInstance'       => $block->parsed_block['storeInstance'],

				// VB equivalent.
				'attrs'               => $attrs,
				'elements'            => $elements,
				'id'                  => $block->parsed_block['id'],
				'name'                => $block->block_type->name,
				'moduleCategory'      => $block->block_type->category,
				'classnamesFunction'  => [ StepFlow::class, 'module_classnames' ],
				'stylesComponent'     => [ StepFlow::class, 'module_styles' ],
				'scriptDataComponent' => [ StepFlow::class, 'module_script_data' ],
				'parentAttrs'         => $parent_attrs,
				'parentId'            => $parent->id ?? '',
				'parentName'          => $parent->blockName ?? '',
				'children'            => [
					ElementComponents::component(
						[
							'attrs'         => $attrs['module']['decoration'] ?? [],
							'id'            => $block->parsed_block['id'],

							// FE only.
							'orderIndex'    => $block->parsed_block['orderIndex'],
							'storeInstance' => $block->parsed_block['storeInstance'],
						]
					),
					HTMLUtility::render(
						[
							'tag'               => 'div',
							'attributes'        => [
								'class' => 'dotm_step_flow_wrapper_container',
							],
							'childrenSanitizer' => 'et_core_esc_previously',
							'children'          => $inner_container . ($use_direction === 'on' ? $direction_container : ''),
						]
					),
				],
			]
		);
	}
}
