<?php

/**
 * BeforeAfterSlider::module_styles().
 *
 * @package MEE\Modules\BeforeAfterSlider
 * @since ??
 */

namespace MEE\Modules\BeforeAfterSlider\BeforeAfterSliderTrait;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

use ET\Builder\FrontEnd\Module\Style;
use ET\Builder\Packages\Module\Options\Text\TextStyle;
use ET\Builder\Packages\Module\Options\Css\CssStyle;
use MEE\Modules\BeforeAfterSlider\BeforeAfterSlider;

trait ModuleStylesTrait
{

	use CustomCssTrait;

	/**
	 * BeforeAfterSlider's style components.
	 *
	 * This function is equivalent of JS function ModuleStyles located in
	 * src/components/BeforeAfterSlider/styles.tsx.
	 *
	 * @since ??
	 *
	 * @param array $args {
	 *     An array of arguments.
	 *
	 *      @type string $id                Module ID. In VB, the ID of module is UUIDV4. In FE, the ID is order index.
	 *      @type string $name              Module name.
	 *      @type string $attrs             Module attributes.
	 *      @type string $parentAttrs       Parent attrs.
	 *      @type string $orderClass        Selector class name.
	 *      @type string $parentOrderClass  Parent selector class name.
	 *      @type string $wrapperOrderClass Wrapper selector class name.
	 *      @type string $settings          Custom settings.
	 *      @type string $state             Attributes state.
	 *      @type string $mode              Style mode.
	 *      @type ModuleElements $elements  ModuleElements instance.
	 * }
	 */
	public static function module_styles($args)
	{
		$attrs    = $args['attrs'] ?? [];
		$elements = $args['elements'];
		$settings = $args['settings'] ?? [];
		$order_class = $args['orderClass'];

		$arrow_selector = "$order_class .dotm_before_after_slider_left_arrow, $order_class .dotm_before_after_slider_right_arrow";
		$slider_selector = "$order_class .dotm_before_after_slider_slide_line";
		$circle_selector = "$order_class .dotm_before_after_slider_slide_line::after";
		$label_before_selector = "$order_class .dotm_before_after_slider_before_label";
		$label_after_selector = "$order_class .dotm_before_after_slider_after_label";
		$vr_before_selector = "$order_class .vertical .dotm_before_after_slider_before_label";
		$vr_after_selector = "$order_class .vertical .dotm_before_after_slider_after_label";

		$slider_color = $attrs['slider_container']['advanced']['slider_color']['desktop']['value'] ?? '';
		$hide_circle = $attrs['slider_container']['advanced']['hide_circle']['desktop']['value'] ?? '';
		$circle_round = $attrs['slider_container']['advanced']['circle_round']['desktop']['value'] ?? '';
		$container_mode = $attrs['slider_container']['advanced']['vertical_mode']['desktop']['value'] ?? 'off';
		$label_hr_position = $attrs['slider_container']['advanced']['label_hr_position']['desktop']['value'] ?? '';
		$label_vr_position = $attrs['slider_container']['advanced']['label_vr_position']['desktop']['value'] ?? '';



		Style::add(
			[
				'id'            => $args['id'],
				'name'          => $args['name'],
				'orderIndex'    => $args['orderIndex'],
				'storeInstance' => $args['storeInstance'],
				'styles'        => [
					// Module.
					$elements->style(
						[
							'attrName'   => 'module',
							'styleProps' => [
								'disabledOn'     => [
									'disabledModuleVisibility' => $settings['disabledModuleVisibility'] ?? null,
								],
								'advancedStyles' => [
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $slider_selector,
											'attr'     => $attrs['slider_container']['advanced']['slider_color'] ?? [],
											'property' => "background: {$slider_color};",
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $arrow_selector,
											'attr'     => $attrs['slider_container']['advanced']['slider_color'] ?? [],
											'property' => "color: {$slider_color};",
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $circle_selector,
											'attr'     => $attrs['slider_container']['advanced']['slider_color'] ?? [],
											'property' => $hide_circle === 'on' ? "border: 3px solid {$slider_color};  box-shadow: 0 0 5px rgba(0, 0, 0, 0.2); border-radius: {$circle_round};" : '',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $label_before_selector,
											'attr'     => $attrs['slider_container']['advanced']['vertical_mode'] ?? [],
											'property' => $container_mode === 'on' ? '' : "top: {$label_hr_position}; left: 10px; transform: translateY(-50%);",
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $label_after_selector,
											'attr'     => $attrs['slider_container']['advanced']['vertical_mode'] ?? [],
											'property' => $container_mode === 'on' ? '' : "top: {$label_hr_position}; right: 10px; transform: translateY(-50%);",
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $vr_before_selector,
											'attr'     => $attrs['slider_container']['advanced']['vertical_mode'] ?? [],
											'property' => $container_mode === 'on' ? "top: 10px; left: {$label_vr_position}; transform: translateX(-50%);" : "",
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $vr_after_selector,
											'attr'     => $attrs['slider_container']['advanced']['vertical_mode'] ?? [],
											'property' => $container_mode === 'on' ? "bottom: 10px; left: {$label_vr_position}; transform: translateX(-50%);" : "",
										]
									],

								]
							],
						]
					),

					// before_label.
					$elements->style(
						[
							'attrName' => 'before_label',
						]
					),

					// after_label.
					$elements->style(
						[
							'attrName' => 'after_label',
						]
					),

					// before_image.
					$elements->style(
						[
							'attrName' => 'before_image',
						]
					),

					// after_image.
					$elements->style(
						[
							'attrName' => 'after_image',
						]
					),

					/*
					 * We need to add CssStyle at the very bottom of other
					 * components so that custom css can override module styles
					 * till we find a more elegant solution.
					 */
					CssStyle::style(
						[
							'selector'  => $args['orderClass'],
							'attr'      => $attrs['css'] ?? [],
							'cssFields' => BeforeAfterSlider::custom_css(),
						]
					),

					// ATTENTION: The code is intentionally added and commented in FE only as an example of expected value format.
					// If you have custom style processing, the style output should be passed as an `array` of style declarations
					// to the `styles` property of the `Style::add` method. For example:
					// [
					// 	[
					// 		'atRules'     => false,
					// 		'selector'    => "{$args['orderClass']} .example_static_module__content-container",
					// 		'declaration' => 'color: red;'
					// 	],
					// 	[
					// 		'atRules'     => '@media only screen and (max-width: 767px)',
					// 		'selector'    => "{$args['orderClass']} .example_static_module__content-container",
					// 		'declaration' => 'color: green;'
					// 	],
					// ],

					// The code below is an example of how to use the `CssStyle::style` method to generate CSS style.
					CssStyle::style(
						[
							'selector'  => $args['orderClass'],
							'attr'      => $attrs['css'] ?? [],
							'cssFields' => BeforeAfterSlider::custom_css(),
						]
					),
				],
			]
		);
	}
}
