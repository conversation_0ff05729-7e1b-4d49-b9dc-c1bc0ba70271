// Divi dependencies.
import {
  type Metadata,
  type ModuleLibrary,
} from '@divi/types';

// Local dependencies.
import metadata from './module.json';
import { OptimizePricingTablesItemEdit } from './edit';
import { SettingsContent } from './settings-content';
import { SettingsDesign } from './settings-design';
import { SettingsAdvanced } from './settings-advanced';
import { OptimizePricingTablesItemAttrs } from './types';
import { placeholderContent } from './placeholder-content';

// Styles.
import './module.scss';
import { ModuleMetadata } from '@divi/types';

/**
 * OptimizePricingTablesItem.
 *
 * @since ??
 */
export const OptimizePricingTablesItem: ModuleLibrary.Module.RegisterDefinition<OptimizePricingTablesItemAttrs> = {
  // Imported json has no inferred type hence type-cast is necessary.
  metadata: metadata as Metadata.Values<OptimizePricingTablesItemAttrs>,
  placeholderContent,
  settings:   {
    content:  SettingsContent,
    design:   SettingsDesign,
    advanced: SettingsAdvanced,
  },
  renderers: {
    edit: OptimizePricingTablesItemEdit,
  },
  parentsName: ['dotm/optimize-pricing-tables'],
};
