import { ModuleEditProps } from '@divi/module-library';
import {
  FormatBreakpointStateAttr,
  InternalAttrs,
  type Element,
  type Icon,
  type Module,
} from '@divi/types';
import {
  OptimizePricingTablesAttrs,
} from "../OptimizePricingTables/types";

export interface OptimizePricingTablesItemCssAttr extends Module.Css.AttributeValue {
  contentContainer?: string;
  title?: string;
  content?: string;
  icon?: string;
}

export type OptimizePricingTablesItemCssGroupAttr = FormatBreakpointStateAttr<OptimizePricingTablesItemCssAttr>;

export interface OptimizePricingTablesItemAttrs extends InternalAttrs {
  // CSS options is used across multiple elements inside the module thus it deserves its own top property.
  css?: OptimizePricingTablesItemCssGroupAttr;

  // Module
  module?: {
    meta?: Element.Meta.Attributes;
    advanced?: {
      link?: Element.Advanced.Link.Attributes;
      htmlAttributes?: Element.Advanced.IdClasses.Attributes;
      text?: Element.Advanced.Text.Attributes;
    };
    decoration?: Element.Decoration.PickedAttributes<
      'background' |
      'border' |
      'boxShadow' |
      'disabledOn' |
      'filters' |
      'overflow' |
      'position' |
      'scroll' |
      'sizing' |
      'spacing' |
      'sticky' |
      'transform' |
      'transition' |
      'zIndex'
    >;
  };

  headingWrapper?: string | any;
  title?: string | any;
  subTitle?: string | any;
  price?: string | any;
  frequency?: string | any;
  content?: string | any;
  currency?: string | any;
  priceWrapper?: string | any;
  priceContainer?: string | any;
  button?: string | any;
  image?: string | any;
  icon?: string | any;
  featureText?: string | any;

}

export type OptimizePricingTablesItemEditProps = ModuleEditProps<OptimizePricingTablesItemAttrs, OptimizePricingTablesAttrs>;
