<?php

/**
 * Module: OptimizeImageReveal class.
 *
 * @package MEE\Modules\OptimizeImageReveal
 * @since ??
 */

namespace MEE\Modules\OptimizeImageReveal;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

use ET\Builder\Framework\DependencyManagement\Interfaces\DependencyInterface;
use ET\Builder\Packages\ModuleLibrary\ModuleRegistration;


/**
 * `OptimizeImageReveal` is consisted of functions used for Static Module such as Front-End rendering, REST API Endpoints etc.
 *
 * This is a dependency class and can be used as a dependency for `DependencyTree`.
 *
 * @since ??
 */
class OptimizeImageReveal implements DependencyInterface
{
	use OptimizeImageRevealTrait\RenderCallbackTrait;
	use OptimizeImageRevealTrait\ModuleClassnamesTrait;
	use OptimizeImageRevealTrait\ModuleStylesTrait;
	use OptimizeImageRevealTrait\ModuleScriptDataTrait;

	/**
	 * Loads `OptimizeImageReveal` and registers Front-End render callback and REST API Endpoints.
	 *
	 * @since ??
	 *
	 * @return void
	 */
	public function load()
	{
		$module_json_folder_path = DIVIOPTIMIZER_MODULES_JSON_PATH . 'OptimizeImageReveal/';

		add_action(
			'init',
			function () use ($module_json_folder_path) {
				ModuleRegistration::register_module(
					$module_json_folder_path,
					[
						'render_callback' => [OptimizeImageReveal::class, 'render_callback'],
					]
				);
			}
		);
	}
}
