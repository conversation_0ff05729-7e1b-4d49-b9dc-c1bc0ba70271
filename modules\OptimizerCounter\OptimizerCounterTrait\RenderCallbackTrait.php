<?php

/**
 * OptimizerCounter::render_callback()
 *
 * @package MEE\Modules\OptimizerCounter
 * @since ??
 */

namespace MEE\Modules\OptimizerCounter\OptimizerCounterTrait;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

// phpcs:disable ET.Sniffs.ValidVariableName.UsedPropertyNotSnakeCase -- WP use snakeCase in \WP_Block_Parser_Block


use ET\Builder\Framework\Utility\HTMLUtility;
use ET\Builder\FrontEnd\BlockParser\BlockParserStore;
use ET\Builder\Packages\Module\Module;
use MEE\Modules\OptimizerCounter\OptimizerCounter;

trait RenderCallbackTrait
{

	/**
	 * OptimizerCounter render callback which outputs server side rendered HTML on the Front-End.
	 *
	 * @since ??
	 * @param array          $attrs    Block attributes that were saved by VB.
	 * @param string         $content  Block content.
	 * @param WP_Block       $block    Parsed block object that being rendered.
	 * @param ModuleElements $elements ModuleElements instance.
	 *
	 * @return string HTML rendered of Static module.
	 */
	public static function render_callback($attrs, $content, $block, $elements)
	{
		// Get counter settings from attrs
		$counter_type = $attrs['counterType']['innerContent']['desktop']['value'] ?? 'bar';
		$counter_value = intval($attrs['counterValue']['innerContent']['desktop']['value'] ?? '75');
		$counter_duration = intval($attrs['counterDuration']['innerContent']['desktop']['value'] ?? '2000');
		$progress_duration = $attrs['counterDuration']['innerContent']['progressDuration']['desktop']['value'] ?? '2s';
		$use_icon = $attrs['counterType']['advanced']['useIcon']['desktop']['value'] ?? 'off';
		$icon_type = $attrs['counterType']['advanced']['iconType']['desktop']['value'] ?? '';

		// Get styling attributes
		$circle_size = $attrs['counterCircle']['decoration']['circleSize']['desktop']['value'] ?? '150';
		$circle_stroke_width = $attrs['counterCircle']['decoration']['strokeWidth']['desktop']['value'] ?? '8';
		// Half circle attributes not used in this implementation
		$animation_easing = $attrs['counterAnimation']['advanced']['easing']['desktop']['value'] ?? 'ease-out';
		$progress_animation_type = $attrs['counterAnimation']['advanced']['progressAnimationType']['desktop']['value'] ?? 'normal';

		$progress_bg_color2 = $attrs['counterBar']['decoration']['progressBgColor2']['desktop']['value'] ?? '';

		// Get color attributes
		$stroke_progress_color = $attrs['counterCircle']['decoration']['strokeProgressColor']['desktop']['value'] ?? '';
		// Half circle colors not used in this implementation

		// Calculate values for different counter types
		$radius = $circle_size / 2 - $circle_stroke_width;
		$circumference = 2 * M_PI * $radius;
		// Calculate the stroke dashoffset for the circle progress
		$final_stroke_dashoffset = $circumference - ($circumference * $counter_value / 100);
		$final_value_percentage = "{$counter_value}%";

		// Generate a unique ID for this counter instance
				$counter_id = 'dotm-counter-' . $block->parsed_block['id'];
				$counter_text = HTMLUtility::render(
					[
						'tag'               => 'span',
						'attributes'        => [
							'id' => $counter_id,
						],
						'childrenSanitizer' => 'et_core_esc_previously',
						'children'          => "0",
					]
				);

				$counter_icon = \ET\Builder\Framework\Utility\HTMLUtility::render(
					[
						'tag'               => 'span',
						'childrenSanitizer' => 'et_core_esc_previously',
						'children'          => $use_icon === 'on' ? ($icon_type === 'plus' ? '+' : '%') : '',
					]
				);

		$option = [
			'elementId' => 'dotm-counter-' . $block->parsed_block['id'],
			'target' => $counter_value,
			'duration' => $counter_duration,
		];


		// labelTop.
		$labelTop = $elements->render(
			[
				'attrName' => 'labelTop',
			]
		);

		// labelBottom.
		$labelBottom = $elements->render(
			[
				'attrName' => 'labelBottom',
			]
		);

		// Create counter HTML based on counter type
		$counter_html = '';

		switch ($counter_type) {
			case 'bar':

				// Define the style for the progress bar
				$style = "--final-value-percentage: {$final_value_percentage}; --animation-easing: {$animation_easing}; --progress-duration: {$progress_duration};";

				// Add striped animation styles if needed
				if ($progress_animation_type === "striped") {
					$style .= " background-size: 20px 20px;";
					$style .= " animation: bar-fill var(--progress-duration, 2s) var(--animation-easing, ease-out) forwards, moveStripes 1.5s linear infinite;";
					if (!empty($progress_bg_color2)) {
						$style .= " background-image: linear-gradient(45deg, {$progress_bg_color2} 25%, transparent 25%, transparent 50%, {$progress_bg_color2} 50%, {$progress_bg_color2} 75%, transparent 75%, transparent);";
					}
				}

				$counter_bar = HTMLUtility::render(
					[
						'tag'               => 'div',
						'attributes'        => [
							'class' => "dotm_optimizer_counter_bar_progress " . ($progress_animation_type === "striped" ? "striped" : ""),
							'style' => $style,
						],
						'childrenSanitizer' => 'et_core_esc_previously',
						'children'          => $counter_text . $counter_icon,
					]
				);
				$counter_bar_wrapper = HTMLUtility::render(
					[
						'tag'               => 'div',
						'attributes'        => [
							'class' => 'dotm_optimizer_counter_bar',
						],
						'childrenSanitizer' => 'et_core_esc_previously',
						'children'          => $counter_bar,
					]
				);
				$counter_html = HTMLUtility::render(
					[
						'tag'               => 'div',
						'attributes'        => [
							'class' => 'dotm_optimizer_counter_wrapper',
						],
						'childrenSanitizer' => 'et_core_esc_previously',
						'children'          => $labelTop . $counter_bar_wrapper . $labelBottom,
					]
				);
				break;

			case 'circle':
				// Create a unique pattern ID for this instance
				$pattern_id = 'striped-pattern-' . $block->parsed_block['id'];

				// Set up the circle properties
				$circle_props = [
					'cx' => $circle_size / 2,
					'cy' => $circle_size / 2,
					'r' => $radius,
					'stroke-width' => $circle_stroke_width,
				];

				// Set up the circle styles
				$circle_style = "--circumference: {$circumference}; --final-stroke-dashoffset: {$final_stroke_dashoffset};";

				// Determine if we should use the striped pattern
				$use_striped_pattern = $progress_animation_type === "striped";

				// Create the SVG content using the same approach as ProgressBar module
				$pattern_content = '';
				if ($use_striped_pattern) {
					$pattern_content = '<animateTransform
						attributeName="patternTransform"
						type="translate"
						from="10 0"
						to="0 0"
						dur="0.5s"
						repeatCount="indefinite"
					/>';
				}

				$svg_content = '<svg
					width="' . $circle_size . '"
					height="' . $circle_size . '"
					viewBox="0 0 ' . $circle_size . ' ' . $circle_size . '"
					class="dotm_optimizer_counter_circle_svg"
				>
					<defs>
						<pattern
							id="' . $pattern_id . '"
							width="10"
							height="10"
							patternUnits="userSpaceOnUse"
							patternTransform="rotate(45)"
						>
							<rect width="5" height="10" fill="' . $stroke_progress_color . '" />
							<rect x="5" width="5" height="10" fill="' . $progress_bg_color2 . '" />
							' . $pattern_content . '
						</pattern>
					</defs>

					<!-- Background circle -->
					<circle
						class="dotm_optimizer_counter_circle_bg"
						cx="' . $circle_props['cx'] . '"
						cy="' . $circle_props['cy'] . '"
						r="' . $circle_props['r'] . '"
						stroke-width="' . $circle_props['stroke-width'] . '"
					/>

					<!-- Progress circle -->
					<circle
						class="dotm_optimizer_counter_circle_progress"
						cx="' . $circle_props['cx'] . '"
						cy="' . $circle_props['cy'] . '"
						r="' . $circle_props['r'] . '"
						stroke-width="' . $circle_props['stroke-width'] . '"
						style="' . ($use_striped_pattern ? 'stroke: url(#' . $pattern_id . ');' : '') . ' transition: stroke-dashoffset ' . $progress_duration . ' ' . $animation_easing . ';"
					/>
				</svg>';

				$circle_text_wrapper = HTMLUtility::render(
					[
						'tag'               => 'div',
						'attributes'        => [
							'class' => 'dotm_optimizer_counter_circle_text',
						],
						'childrenSanitizer' => 'et_core_esc_previously',
						'children'          => $counter_text . $counter_icon,
					]
				);



				// Create the circle container
				$counter_circle = HTMLUtility::render(
					[
						'tag'               => 'div',
						'attributes'        => [
							'class' => 'dotm_optimizer_counter_circle',
							'style' => $circle_style,
						],
						'childrenSanitizer' => 'et_core_esc_previously',
						'children'          => $svg_content . $circle_text_wrapper,
					]
				);

				// Create the wrapper
				$counter_html = HTMLUtility::render(
					[
						'tag'               => 'div',
						'attributes'        => [
							'class' => 'dotm_optimizer_counter_wrapper',
						],
						'childrenSanitizer' => 'et_core_esc_previously',
						'children'          => $labelTop . $counter_circle . $labelBottom,
					]
				);
				break;

			case 'blocks':
				// Get blocks styling attributes
				$block_size = $attrs['counterBlocks']['decoration']['blockSize']['desktop']['value'] ?? '30';
				$block_spacing = $attrs['counterBlocks']['decoration']['blockSpacing']['desktop']['value'] ?? '5';
				$block_color = $attrs['counterBlocks']['decoration']['blockColor']['desktop']['value'] ?? '#6200ee';
				$block_bg_color = $attrs['counterBlocks']['decoration']['blockBgColor']['desktop']['value'] ?? '#e0e0e0';

				// Calculate the number of blocks (10 blocks for 100%)
				$total_blocks = 10;
				$active_blocks = ceil(($counter_value / 100) * $total_blocks);

				// Create blocks HTML
				$blocks_html = '';
				for ($i = 0; $i < $total_blocks; $i++) {
					$is_active = $i < $active_blocks ? 'active' : '';
					$animation_delay = $i * 0.1;

					$blocks_html .= HTMLUtility::render(
						[
							'tag'               => 'div',
							'attributes'        => [
								'class' => "dotm_optimizer_counter_blocks_item {$is_active}",
								'style' => "animation-delay: {$animation_delay}s;",
							],
							'childrenSanitizer' => 'et_core_esc_previously',
							'children'          => "",
						]
					);
				}

				// Create the counter value display
				$counter_value_display = HTMLUtility::render(
					[
						'tag'               => 'div',
						'attributes'        => [
							'class' => 'dotm_optimizer_counter_blocks_value',
						],
						'childrenSanitizer' => 'et_core_esc_previously',
						'children'          => $counter_text . $counter_icon,
					]
				);

				// Create the blocks container
				$blocks_container = HTMLUtility::render(
					[
						'tag'               => 'div',
						'attributes'        => [
							'class' => 'dotm_optimizer_counter_blocks',
							'style' => "--block-size: {$block_size}px; --block-spacing: {$block_spacing}px; --block-color: {$block_color}; --block-bg-color: {$block_bg_color}; --animation-easing: {$animation_easing}; --progress-duration: {$progress_duration};",
						],
						'childrenSanitizer' => 'et_core_esc_previously',
						'children'          => $blocks_html . $counter_value_display,
					]
				);

				// Create the wrapper
				$counter_html = HTMLUtility::render(
					[
						'tag'               => 'div',
						'attributes'        => [
							'class' => 'dotm_optimizer_counter_wrapper',
						],
						'childrenSanitizer' => 'et_core_esc_previously',
						'children'          => $labelTop . $blocks_container . $labelBottom,
					]
				);
				break;

			case 'simple':
			default:
				// Create the counter text
				$counter_text = HTMLUtility::render(
					[
						'tag'               => 'span',
						'attributes'        => [
							'id' => 'dotm-counter-' . $block->parsed_block['id'],
						],
						'childrenSanitizer' => 'et_core_esc_previously',
						'children'          => "0",
					]
				);

				// Create the counter icon if needed
				$counter_icon = '';
				if ($use_icon === 'on') {
					$counter_icon = HTMLUtility::render(
						[
							'tag'               => 'span',
							'childrenSanitizer' => 'et_core_esc_previously',
							'children'          => $icon_type === 'plus' ? '+' : '%',
						]
					);
				}

				// Create the simple counter
				$simple_counter = HTMLUtility::render(
					[
						'tag'               => 'div',
						'attributes'        => [
							'class' => 'dotm_optimizer_counter_simple',
							'style' => "animation-timing-function: {$animation_easing}; animation-duration: {$progress_duration};",
						],
						'childrenSanitizer' => 'et_core_esc_previously',
						'children'          => $counter_text . $counter_icon,
					]
				);

				// Create the wrapper
				$counter_html = \ET\Builder\Framework\Utility\HTMLUtility::render(
					[
						'tag'               => 'div',
						'attributes'        => [
							'class' => 'dotm_optimizer_counter_wrapper',
						],
						'childrenSanitizer' => 'et_core_esc_previously',
						'children'          => $labelTop . $simple_counter . $labelBottom,
					]
				);
				break;
		}


		$parent       = \ET\Builder\FrontEnd\BlockParser\BlockParserStore::get_parent($block->parsed_block['id'], $block->parsed_block['storeInstance']);
		$parent_attrs = $parent->attrs ?? [];

		return \ET\Builder\Packages\Module\Module::render(
			[
				// FE only.
				'orderIndex'          => $block->parsed_block['orderIndex'],
				'storeInstance'       => $block->parsed_block['storeInstance'],

				// VB equivalent.
				'attrs'               => $attrs,
				'elements'            => $elements,
				'id'                  => $block->parsed_block['id'],
				'name'                => $block->block_type->name,
				'moduleCategory'      => $block->block_type->category,
				'classnamesFunction'  => [OptimizerCounter::class, 'module_classnames'],
				'stylesComponent'     => [OptimizerCounter::class, 'module_styles'],
				'scriptDataComponent' => [OptimizerCounter::class, 'module_script_data'],
				'parentAttrs'         => $parent_attrs,
				'parentId'            => $parent->id ?? '',
				'parentName'          => $parent->blockName ?? '',
				'children'            => [
					$counter_html,
					\ET\Builder\Framework\Utility\HTMLUtility::render(
						[
							'tag' => 'script',
							'attributes' => [],
							'childrenSanitizer' => 'et_core_esc_previously',
							'children' => "document.addEventListener('DOMContentLoaded', function() {
                                const options = " . json_encode($option) . ";
                                animateCounter(options);
                            });",
						]
					),
				],
			]
		);
	}
}
