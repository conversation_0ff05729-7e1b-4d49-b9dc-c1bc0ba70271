<?php

/**
 * OptimizeImageReveal::module_styles().
 *
 * @package MEE\Modules\OptimizeImageReveal
 * @since ??
 */

namespace MEE\Modules\OptimizeImageReveal\OptimizeImageRevealTrait;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

use ET\Builder\FrontEnd\Module\Style;
use ET\Builder\Packages\Module\Options\Text\TextStyle;
use ET\Builder\Packages\Module\Options\Css\CssStyle;
use MEE\Modules\OptimizeImageReveal\OptimizeImageReveal;

trait ModuleStylesTrait
{

	use CustomCssTrait;

	/**
	 * Static Module's style components.
	 *
	 * This function is equivalent of JS function ModuleStyles located in
	 * src/components/OptimizeImageReveal/styles.tsx.
	 *
	 * @since ??
	 *
	 * @param array $args {
	 *     An array of arguments.
	 *
	 *      @type string $id                Module ID. In VB, the ID of module is UUIDV4. In FE, the ID is order index.
	 *      @type string $name              Module name.
	 *      @type string $attrs             Module attributes.
	 *      @type string $parentAttrs       Parent attrs.
	 *      @type string $orderClass        Selector class name.
	 *      @type string $parentOrderClass  Parent selector class name.
	 *      @type string $wrapperOrderClass Wrapper selector class name.
	 *      @type string $settings          Custom settings.
	 *      @type string $state             Attributes state.
	 *      @type string $mode              Style mode.
	 *      @type ModuleElements $elements  ModuleElements instance.
	 * }
	 */
	public static function module_styles($args)
	{
		$attrs    = $args['attrs'] ?? [];
		$elements = $args['elements'];
		$settings = $args['settings'] ?? [];
		$orderClass = $args['orderClass'];

		$reveal_selector =  "$orderClass .dotm_optimize_image_reveal_img_reveal";
		$main_selector =  "$orderClass .dotm_optimize_image_reveal_main";

		$alignment = $attrs['image']['advanced']['alignment']['desktop']['value'] ?? '';
		$reveal_color = $attrs['image']['advanced']['revealColor']['desktop']['value'] ?? '';

		Style::add(
			[
				'id'            => $args['id'],
				'name'          => $args['name'],
				'orderIndex'    => $args['orderIndex'],
				'storeInstance' => $args['storeInstance'],
				'styles'        => [
					// Module.
					$elements->style(
						[
							'attrName'   => 'module',
							'styleProps' => [
								'disabledOn'     => [
									'disabledModuleVisibility' => $settings['disabledModuleVisibility'] ?? null,
								],
								'advancedStyles' => [

									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $reveal_selector,
											'attr'     => $attrs['image']['advanced']['revealDirection'] ?? [],
											'property' => "background-color: {$reveal_color};"
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $main_selector,
											'attr'     => $attrs['image']['advanced']['alignment'] ?? [],
											'property' => "display: flex; justify-content: {$alignment};"
										]
									]
								]
							],
						]
					),

					// container.
					$elements->style(
						[
							'attrName' => 'container',
						]
					),

					// image.
					$elements->style(
						[
							'attrName' => 'image',
						]
					),

					// overlay.
					$elements->style(
						[
							'attrName' => 'overlay',
						]
					),

					/*
					 * We need to add CssStyle at the very bottom of other
					 * components so that custom css can override module styles
					 * till we find a more elegant solution.
					 */
					CssStyle::style(
						[
							'selector'  => $args['orderClass'],
							'attr'      => $attrs['css'] ?? [],
							'cssFields' => OptimizeImageReveal::custom_css(),
						]
					),

					// ATTENTION: The code is intentionally added and commented in FE only as an example of expected value format.
					// If you have custom style processing, the style output should be passed as an `array` of style declarations
					// to the `styles` property of the `Style::add` method. For example:
					// [
					// 	[
					// 		'atRules'     => false,
					// 		'selector'    => "{$args['orderClass']} .example_static_module__content-container",
					// 		'declaration' => 'color: red;'
					// 	],
					// 	[
					// 		'atRules'     => '@media only screen and (max-width: 767px)',
					// 		'selector'    => "{$args['orderClass']} .example_static_module__content-container",
					// 		'declaration' => 'color: green;'
					// 	],
					// ],

					// The code below is an example of how to use the `CssStyle::style` method to generate CSS style.
					CssStyle::style(
						[
							'selector'  => $args['orderClass'],
							'attr'      => $attrs['css'] ?? [],
							'cssFields' => OptimizeImageReveal::custom_css(),
						]
					),
				],
			]
		);
	}
}
