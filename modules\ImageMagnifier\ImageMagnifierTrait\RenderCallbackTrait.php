<?php
/**
 * ImageMagnifier::render_callback()
 *
 * @package MEE\Modules\ImageMagnifier
 * @since ??
 */

namespace MEE\Modules\ImageMagnifier\ImageMagnifierTrait;

if ( ! defined( 'ABSPATH' ) ) {
	die( 'Direct access forbidden.' );
}

// phpcs:disable ET.Sniffs.ValidVariableName.UsedPropertyNotSnakeCase -- WP use snakeCase in \WP_Block_Parser_Block

use ET\Builder\Packages\Module\Module;
use ET\Builder\Framework\Utility\HTMLUtility;
use ET\Builder\FrontEnd\BlockParser\BlockParserStore;
use ET\Builder\Packages\Module\Options\Element\ElementComponents;
use MEE\Modules\ImageMagnifier\ImageMagnifier;

trait RenderCallbackTrait {

	/**
	 * ImageMagnifier render callback which outputs server side rendered HTML on the Front-End.
	 *
	 * @since ??
	 * @param array          $attrs    Block attributes that were saved by VB.
	 * @param string         $content  Block content.
	 * @param WP_Block       $block    Parsed block object that being rendered.
	 * @param ModuleElements $elements ModuleElements instance.
	 *
	 * @return string HTML rendered of Static module.
	 */
	public static function render_callback( $attrs, $content, $block, $elements ) {

		$uuid = substr(str_shuffle(str_repeat($x='0123456789abcdefghijklmnopqrstuvwxyz', ceil(10/strlen($x)) )), 1, 10) . (new \DateTime())->format('U');

		// Image.
		$zoom_lavel = $attrs['lens']['advanced']['zoom_lavel']['desktop']['value'] ?? '';

		$option = [
			'containerId' => $uuid,
			'zoom_lavel' => $zoom_lavel,
		];
		

		// image.
		$image = $elements->render(
			[
				'attrName' => 'image',
			]
		);

		// magnifier_glass.
		$magnifier_glass = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'dotm_image_magnifier_glass',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => "",
			]
		);

		$parent       = BlockParserStore::get_parent( $block->parsed_block['id'], $block->parsed_block['storeInstance'] );
		$parent_attrs = $parent->attrs ?? [];

		return Module::render(
			[
				// FE only.
				'orderIndex'          => $block->parsed_block['orderIndex'],
				'storeInstance'       => $block->parsed_block['storeInstance'],

				// VB equivalent.
				'attrs'               => $attrs,
				'elements'            => $elements,
				'id'                  => $block->parsed_block['id'],
				'name'                => $block->block_type->name,
				'moduleCategory'      => $block->block_type->category,
				'classnamesFunction'  => [ ImageMagnifier::class, 'module_classnames' ],
				'stylesComponent'     => [ ImageMagnifier::class, 'module_styles' ],
				'scriptDataComponent' => [ ImageMagnifier::class, 'module_script_data' ],
				'parentAttrs'         => $parent_attrs,
				'parentId'            => $parent->id ?? '',
				'parentName'          => $parent->blockName ?? '',
				'children'            => [
					ElementComponents::component(
						[
							'attrs'         => $attrs['module']['decoration'] ?? [],
							'id'            => $block->parsed_block['id'],

							// FE only.
							'orderIndex'    => $block->parsed_block['orderIndex'],
							'storeInstance' => $block->parsed_block['storeInstance'],
						]
					),
					HTMLUtility::render(
						[
							'tag'               => 'div',
							'attributes'        => [
								'class' => 'dotm_image_magnifier_container',
								'id' => $uuid
							],
							'childrenSanitizer' => 'et_core_esc_previously',
							'children'          => $magnifier_glass . $image,
						]
					),
					HTMLUtility::render(
						[
							'tag' => 'script',
							'attributes' => [],
							'childrenSanitizer' => 'et_core_esc_previously',
							'children' => "document.addEventListener('DOMContentLoaded', function() {
                                const options = " . json_encode($option) . ";
                                ImageMagnifier(options);
                            });",
						]
					),
				],
			]
		);
	}
}
