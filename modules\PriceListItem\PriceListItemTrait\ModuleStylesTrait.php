<?php

/**
 * PriceListItem::module_styles().
 *
 * @package Builder\Packages\ModuleLibrary
 * @since ??
 */

namespace MEE\Modules\PriceListItem\PriceListItemTrait;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

use ET\Builder\FrontEnd\Module\Style;
use ET\Builder\Packages\Module\Options\Text\TextStyle;
use ET\Builder\Packages\Module\Options\Css\CssStyle;
use ET\Builder\Packages\Module\Layout\Components\StyleCommon\CommonStyle;
use ET\Builder\Packages\ModuleLibrary\ModuleRegistration;
use MEE\Modules\PriceListItem\PriceListItem;

trait ModuleStylesTrait
{

	use CustomCssTrait;
	use StyleDeclarationTrait;

	/**
	 * PriceListItem's style components.
	 *
	 * This function is equivalent of JS function ModuleStyles located in
	 * src/components/PriceListItem/styles.tsx.
	 *
	 * @param array $args {
	 *     An array of arguments.
	 *
	 * @type string $id Module ID. In VB, the ID of module is UUIDV4. In FE, the ID is order index.
	 * @type string $name Module name.
	 * @type string $attrs Module attributes.
	 * @type string $parentAttrs Parent attrs.
	 * @type string $orderClass Selector class name.
	 * @type string $parentOrderClass Parent selector class name.
	 * @type string $wrapperOrderClass Wrapper selector class name.
	 * @type string $settings Custom settings.
	 * @type string $state Attributes state.
	 * @type string $mode Style mode.
	 * @type ModuleElements $elements ModuleElements instance.
	 * }
	 * @since ??
	 */
	public static function module_styles($args)
	{
		$attrs        = $args['attrs'] ?? [];
		$order_class  = $args['orderClass'];
		$elements     = $args['elements'];
		$settings     = $args['settings'] ?? [];
		$parent_attrs = $args['parentAttrs'] ?? [];

		$parent_default_attributes = ModuleRegistration::get_default_attrs('optimizer/dotm-price-list');
		$parent_attrs_with_default = array_replace_recursive($parent_default_attributes, $parent_attrs);

		$separator_selector              = "{$order_class} .dotm_price_list_item_separator";
		$image_selector = "{$order_class} .dotm_price_list_item_image_container";
		$container_selector = "{$order_class} .dotm_price_list_item_container";
		$iconSelector = "{$order_class} .dotm_price_list_item_icon";

		$separator_width =
			$attrs['separator']['decoration']['width']['desktop']['value'] ?? "";
		$separator_color =
			$attrs['separator']['decoration']['color']['desktop']['value'] ?? "";
		$separator_style =
			$attrs['separator']['decoration']['style']['desktop']['value'] ?? "";
		$separator_gap =
			$attrs['separator']['decoration']['gap_spacing']['desktop']['value'] ?? "";



		Style::add(
			[
				'id'            => $args['id'],
				'name'          => $args['name'],
				'orderIndex'    => $args['orderIndex'],
				'storeInstance' => $args['storeInstance'],
				'styles'        => [
					// Module.
					$elements->style(
						[
							'attrName'   => 'module',
							'styleProps' => [
								'disabledOn' => [
									'disabledModuleVisibility' => $settings['disabledModuleVisibility'] ?? null,
								],
							],
						]
					),

					CssStyle::style(
						[
							'selector'  => $args['orderClass'],
							'attr'      => $attrs['css'] ?? [],
							'cssFields' => self::custom_css(),
						]
					),

					// contentContainer.
					$elements->style(
						[
							'attrName' => 'contentContainer',
						]
					),

					// image_container.
					$elements->style(
						[
							'attrName' => 'image_container',
						]
					),

					// price_tag.
					$elements->style(
						[
							'attrName' => 'price_tag',
						]
					),

					// Title.
					$elements->style(
						[
							'attrName' => 'title',
						]
					),

					// Content.
					$elements->style(
						[
							'attrName' => 'content',
						]
					),

					// image.
					$elements->style(
						[
							'attrName' => 'image',
						]
					),

					// icon.
					$elements->style(
						[
							'attrName' => 'icon',
						]
					),

					// Icon.
					CommonStyle::style(
						[
							'selector'            => $iconSelector,
							'attr'                => $attrs['icon']['innerContent'] ?? [],
							'declarationFunction' => [ PriceListItem::class, 'icon_font_declaration' ],
						]
					),
					CommonStyle::style(
						[
							'selector' => $iconSelector ,
							'attr'     => $attrs['icon']['advanced']['color'] ?? [],
							'property' => 'color',
						]
					),
					CommonStyle::style(
						[
							'selector' => $iconSelector ,
							'attr'     => $attrs['icon']['advanced']['size'] 
							 ?? [],
							'property' => 'font-size',
						]
					),


					CommonStyle::style(
						[
							'selector' => $container_selector,
							'attr'     => $attrs['image']['advanced']['alignment'] ?? [],
							'property' => 'align-items',
						]
					),
					CommonStyle::style(
						[
							'selector' => $image_selector,
							'attr'     => $attrs['image']['advanced']['img_width'] ?? [],
							'property' => 'width',
						]
					),

					CommonStyle::style(
						[
							'selector' => $separator_selector,
							'attr'     => $attrs['separator']['decoration']['style'] ?? [],
							'property' => "border-bottom-width: {$separator_width}; border-bottom-color: {$separator_color}; border-bottom-style: {$separator_style}; margin-left: {$separator_gap}; margin-right: {$separator_gap};",
						]
					),
					
				],
			]
		);
	}
}
