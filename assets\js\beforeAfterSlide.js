function beforeAfterImageSlider(options = {}) {

    const { interactionMode, mode, slideStartingPoint } = options;

    function updateInteractionMode() {
        document.querySelectorAll('.dotm_before_after_slider_image_container').forEach((slider) => {
            const sliderLine = slider.querySelector('.dotm_before_after_slider_slide_line');
            const afterImage = slider.querySelector('.dotm_before_after_slider_after_image');

            // Remove all existing event listeners
            slider.onmousedown = null;
            slider.onmousemove = null;
            slider.onclick = null;

            document.onmouseup = null;
            document.onmousemove = null;

            if (interactionMode === 'drag') {
                let isDragging = false;

                slider.onmousedown = (e) => {
                    isDragging = true;
                    handleMove(e, slider, afterImage, sliderLine);
                };

                document.onmouseup = () => {
                    isDragging = false;
                };

                document.onmousemove = (e) => {
                    if (isDragging) {
                        handleMove(e, slider, afterImage, sliderLine);
                    }
                };
            } else if (interactionMode === 'click') {
                slider.onclick = (e) => {
                    handleMove(e, slider, afterImage, sliderLine);
                };
            } else if (interactionMode === 'hover') {
                slider.onmousemove = (e) => {
                    handleMove(e, slider, afterImage, sliderLine);
                };
            }
        });
    }

    function handleMove(e, slider, afterImage, sliderLine) {
        const rect = slider.getBoundingClientRect();
        let offset, percentage;

        if (slider.classList.contains('vertical')) {
            offset = e.clientY - rect.top;
            percentage = Math.max(0, Math.min(offset / rect.height, 1));
            afterImage.style.clipPath = `inset(${percentage * 100}% 0 0 0)`;
            sliderLine.style.top = `${percentage * 100}%`;
            sliderLine.style.left = '0';
        } else {
            offset = e.clientX - rect.left;
            percentage = Math.max(0, Math.min(offset / rect.width, 1));
            afterImage.style.clipPath = `inset(0 0 0 ${percentage * 100}%)`;
            sliderLine.style.left = `${percentage * 100}%`;
            sliderLine.style.top = '0';
        }
    }

    function setOrientation(orientation) {
        document.querySelectorAll('.dotm_before_after_slider_image_container').forEach((slider) => {
            if (orientation === 'vertical') {
                slider.classList.add('vertical');
            } else {
                slider.classList.remove('vertical');
            }

            const afterImage = slider.querySelector('.dotm_before_after_slider_after_image');
            const sliderLine = slider.querySelector('.dotm_before_after_slider_slide_line');

            afterImage.style.clipPath = slider.classList.contains('vertical') ? `inset(${slideStartingPoint} 0 0 0)` : `inset(0 0 0 ${slideStartingPoint})`;
            sliderLine.style.left = slider.classList.contains('vertical') ? '0' : slideStartingPoint;
            sliderLine.style.top = slider.classList.contains('vertical') ? slideStartingPoint : '0';
        });
    }

    // Initialize default orientation and interaction mode
    setOrientation(mode);
    updateInteractionMode();
}