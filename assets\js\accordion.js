function accordion(id, trigger, direction, animation) {
      const accordion = document.getElementById(id);

      if (!accordion) {
        console.warn(`Accordion with id "${id}" not found.`);
        return;
      }

      accordion.classList.remove('horizontal', 'vertical');
      accordion.classList.add(direction);

      const panels = accordion.querySelectorAll('.dotm_image_accordion_item');

      function expandPanel(panel) {
        panels.forEach(p => {
          p.classList.remove('dotm_image_accordion_item_expanded');
          const content = p.querySelector('.dotm_image_accordion_item_contents');
          if (content) content.classList.remove('zoom-in', 'push-up', 'push-down', 'push-left', 'push-right');
        });

        panel.classList.add('dotm_image_accordion_item_expanded');
        const content = panel.querySelector('.dotm_image_accordion_item_contents');
        if (content) content.classList.add(animation);
      }

      panels.forEach(panel => {
        if (trigger === 'hover') {
          panel.addEventListener('mouseenter', () => expandPanel(panel));
        } else {
          panel.addEventListener('click', () => expandPanel(panel));
        }
      });
}
