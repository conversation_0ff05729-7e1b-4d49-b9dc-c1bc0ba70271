// External dependencies.
import React, { ReactElement, useState } from 'react';

// WordPress dependencies
import { __ } from '@wordpress/i18n';

// Divi dependencies.
import {
  AdminLabelGroup,
  BackgroundGroup,
  FieldContainer,
  LinkGroup,
} from '@divi/module';
import {
  GroupContainer,
  GroupTabs
} from '@divi/modal';
import {
  ColorPickerContainer,
  Range,
  RangeContainer,
  RichTextContainer,
  SelectContainer,
  TextContainer,
  ToggleContainer,
  UploadContainer,
} from '@divi/field-library';
import {
  type Module,
} from '@divi/types';


import { BeforeAfterSliderAttrs } from "./types";

const handleRangeChange = (params: { inputValue: string }) => {
  // setRangeValue(value);
  console.log('Range Value:', params.inputValue);
};

export const SettingsContent = ({
  defaultSettingsAttrs,
  ...props
}: Module.Settings.Panel.Props<BeforeAfterSliderAttrs>): ReactElement => {
  const {attrs} = props;
  const [activeTab, setAcivteTab] = useState('before');
  const hide_circle = attrs?.slider_container?.advanced?.hide_circle?.desktop?.value ?? 'on';
  const use_labels = attrs?.slider_container?.advanced?.use_labels?.desktop?.value ?? '';
  const containerMode = attrs?.slider_container?.advanced?.vertical_mode?.desktop?.value ?? 'off';
  return (
    <React.Fragment>
      <GroupContainer
        id="images"
        title={__('Images', 'divi-optimaizer-modules')}
      >
        <GroupTabs
          showLabel={true}
          activeTab={activeTab}
          showIcon={false}
          tabs={{
            'before': {
              "label": "Before Image",
              "icon": "iconNameFor2D" // Replace "iconNameFor2D" with the actual icon identifier for the 2D tab
            },
            'after': {
              "label": "After Image",
              "icon": "iconNameForBG" // Replace "iconNameForBG" with the actual icon identifier for the BG tab
            }

          }}
          onClick={(e: React.MouseEvent<HTMLButtonElement>) => {
            let target = e.target as HTMLButtonElement;
            let parent = target.parentElement;
            setAcivteTab(
              target.value ? target.value : (parent as HTMLButtonElement).value
            );
          }}
        />
        <FieldContainer
          attrName="before_image.innerContent"
          subName="src"
          label={__('Before Image', 'divi-optimaizer-modules')}
          description={__('Upload an Image', 'divi-optimaizer-modules')}
          features={{
            sticky: false,
          }}
          visible={activeTab === 'before'}
        >
          <UploadContainer />
        </FieldContainer>

        <FieldContainer
          attrName="before_image.innerContent"
          subName="alt"
          label={__('Before Image Alt', 'divi-optimaizer-modules')}
          description={__('Input the alt text for the before image', 'divi-optimaizer-modules')}
          features={{
            sticky: false,
          }}
          visible={activeTab === 'before'}
        >
          <TextContainer />
        </FieldContainer>

        <FieldContainer
          attrName="after_image.innerContent"
          subName="src"
          label={__('After Image', 'divi-optimaizer-modules')}
          description={__('Upload an Image', 'divi-optimaizer-modules')}
          features={{
            sticky: false,
          }}
          visible={activeTab === 'after'}
        >
          <UploadContainer />
        </FieldContainer>

        <FieldContainer
          attrName="after_image.innerContent"
          subName="alt"
          label={__('After Image Alt', 'divi-optimaizer-modules')}
          description={__('Input the alt text for the after image', 'divi-optimaizer-modules')}
          features={{
            sticky: false,
          }}
          visible={activeTab === 'after'}
        >
          <TextContainer />
        </FieldContainer>

        
      </GroupContainer>

      <GroupContainer
        id='slide_settings'
        title={__('Slide Settings', 'divi-optimaizer-modules')}
      >
        <FieldContainer
          attrName='slider_container.advanced.slide_starting_point'
          defaultAttr={defaultSettingsAttrs?.slider_container?.advanced?.slide_starting_point}
          label={__('Slide Starting Point', 'divi-optimaizer-modules')}
          description={__('Input your value to action here.', 'divi-optimaizer-modules')}
          features={{ sticky: false }}
        >
          <RangeContainer defaultUnit="%"  />

        </FieldContainer>
        <FieldContainer
          attrName='slider_container.advanced.slide_trigger'
          defaultAttr={defaultSettingsAttrs?.slider_container?.advanced?.slide_trigger}
          label={__('Slide Trigger', 'divi-optimaizer-modules')}
          description={__('Input your value to action here.', 'divi-optimaizer-modules')}
          features={{ sticky: false }}
        >
          <SelectContainer 
            defaultValue="click"
            options= {{
              "click": { label: "Click" },
              "hover": { label: "Hover" },
              "drag": { label: "Drag" }
            }}
          />
        </FieldContainer>

        <FieldContainer
          attrName='slider_container.advanced.hide_circle'
          defaultAttr={defaultSettingsAttrs?.slider_container?.advanced?.hide_circle}
          label={__('Hide Circle', 'divi-optimaizer-modules')}
          description={__('Input your value to action here.', 'divi-optimaizer-modules')}
          features={{ sticky: false }}
        >
          <ToggleContainer />
        </FieldContainer>

        <FieldContainer
          attrName='slider_container.advanced.circle_round'
          defaultAttr={defaultSettingsAttrs?.slider_container?.advanced?.circle_round}
          label={__('Circle Round', 'divi-optimaizer-modules')}
          description={__('Input your value to action here.', 'divi-optimaizer-modules')}
          features={{ sticky: false }}
          visible={hide_circle === 'on'}
        >
          <RangeContainer defaultUnit="%" />
        </FieldContainer>

        <FieldContainer
          attrName='slider_container.advanced.vertical_mode'
          label={__('Vertical Mode', 'divi-optimaizer-modules')}
          description={__('Input your value to action here.', 'divi-optimaizer-modules')}
          features={{ sticky: false }}
        >
          <ToggleContainer />
        </FieldContainer>

        <FieldContainer
          attrName='slider_container.advanced.slider_color'
          defaultAttr={defaultSettingsAttrs?.slider_container?.advanced?.slider_color}
          label={__('Slider Color', 'divi-optimaizer-modules')}
          description={__('Input your value to action here.', 'divi-optimaizer-modules')}
          features={{ sticky: false }}
        >
          <ColorPickerContainer />
        </FieldContainer>
      </GroupContainer>
      
      <GroupContainer
        id='labels'
        title={__('Labels', 'divi-optimaizer-modules')}
      >
        <FieldContainer
          attrName='slider_container.advanced.use_labels'
          defaultAttr={defaultSettingsAttrs?.slider_container?.advanced?.use_labels}
          label={__('Use Label', 'divi-optimaizer-modules')}
          description={__('Input your value to action here.', 'divi-optimaizer-modules')}
          features={{ sticky: false }}
        >
          <ToggleContainer />
        </FieldContainer>

        <FieldContainer
          attrName="before_label.innerContent"
          label={__('Before Image Label', 'divi-optimaizer-modules')}
          description={__('Input the alt text for the before image label', 'divi-optimaizer-modules')}
          features={{
            sticky: false,
          }}
          visible={use_labels === 'on'}
        >
          <TextContainer />
        </FieldContainer>



        <FieldContainer
          attrName="after_label.innerContent"
          label={__('After Image Label', 'divi-optimaizer-modules')}
          description={__('Input the alt text for the after image label', 'divi-optimaizer-modules')}
          features={{
            sticky: false,
          }}
          visible={use_labels === 'on'}
        >
          <TextContainer />
        </FieldContainer>

        <FieldContainer
          attrName="slider_container.advanced.label_hr_position"
          defaultAttr={defaultSettingsAttrs?.slider_container?.advanced?.label_hr_position}
          label={__(' Label Horizontal Position', 'divi-optimaizer-modules')}
          description={__('Input your value to action here.', 'divi-optimaizer-modules')}
          features={{
            sticky: false,
          }}
          visible={containerMode === 'off' && use_labels === 'on'}
        >
          <RangeContainer defaultUnit="%" />
        </FieldContainer>

        <FieldContainer
          attrName="slider_container.advanced.label_vr_position"
          defaultAttr={defaultSettingsAttrs?.slider_container?.advanced?.label_vr_position}
          label={__(' Label Vertical Position', 'divi-optimaizer-modules')}
          description={__('Input your value to action here.', 'divi-optimaizer-modules')}
          features={{
            sticky: false,
          }}
          visible={containerMode === 'on' && use_labels === 'on'}
        >
          <RangeContainer defaultUnit="%" />
        </FieldContainer>

        <FieldContainer
          attrName='slider_container.advanced.use_label_on_hover'
          // defaultAttr={defaultSettingsAttrs?.label_container?.advanced?.use_label_on_hover}
          label={__('Use Label on Hover', 'divi-optimaizer-modules')}
          description={__('Input your value to action here.', 'divi-optimaizer-modules')}
          features={{ sticky: false }}
        >
          <ToggleContainer />
        </FieldContainer>
      </GroupContainer>

      <LinkGroup />
      <BackgroundGroup
        defaultGroupAttr={defaultSettingsAttrs?.module?.decoration?.background?.asMutable({ deep: true }) ?? {}}
      />
      <AdminLabelGroup />
    </React.Fragment>
  )
}
