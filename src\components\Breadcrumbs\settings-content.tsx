// External dependencies.
import React, { ReactElement } from 'react';

// WordPress dependencies
import { __ } from '@wordpress/i18n';

// Divi dependencies.
import {
  AdminLabelGroup,
  BackgroundGroup,
  DraggableChildModuleListContainer,
  FieldContainer,
  LinkGroup,
} from '@divi/module';
import {
  DraggableListContainer,
  IconPickerContainer,
  TextContainer,
} from '@divi/field-library';
import {
  type Module,
} from '@divi/types';
import { GroupContainer } from '@divi/modal';
import { BreadcrumbsAttrs } from "./types";


export const SettingsContent = ({
  defaultSettingsAttrs,
}: Module.Settings.Panel.Props<BreadcrumbsAttrs>): ReactElement => (
  <React.Fragment>
    <DraggableChildModuleListContainer
      childModuleName="dotm/breadcrumb-item"
      addTitle={__('Add New Child Module', 'et_builder')}
    >
      <DraggableListContainer />
    </DraggableChildModuleListContainer>
    <GroupContainer
      id="icon"
      title={__('Icon', 'divi-optimaizer-modules')}
    >
      <FieldContainer
        attrName="icon.innerContent"
        label={__('Icon', 'divi-optimaizer-modules')}
        description={__('Upload an Icon', 'divi-optimaizer-modules')}
        defaultAttr={defaultSettingsAttrs?.icon}
        features={{
          sticky: false,
        }}
      >
        <IconPickerContainer />
      </FieldContainer>
    </GroupContainer>
    <GroupContainer
      id="mainContent"
      title={__('Home Button Content', 'divi-optimaizer-modules')}
    >
      <FieldContainer
        attrName="home_btn.innerContent"
        label={__('Home', 'divi-optimaizer-modules')}
        description={__('Input your value to action title here.', 'divi-optimaizer-modules')}
        features={{
          sticky: false,
        }}
      >
        <TextContainer />
      </FieldContainer>
      <FieldContainer
        attrName="home_icon.innerContent"
        label={__('Home Iocn', 'divi-optimaizer-modules')}
        description={__('Input your value to action title here.', 'divi-optimaizer-modules')}
        features={{
          sticky: false,
        }}
      >
        <IconPickerContainer />
      </FieldContainer>
      <LinkGroup attrName='home_btn.innerContent.links' fieldLabel='Home'  grouped={false} />
    </GroupContainer>
    <LinkGroup />
    <BackgroundGroup />
    <AdminLabelGroup />
  </React.Fragment>
);
