<?php
/**
 * TextBadge::render_callback()
 *
 * @package MEE\Modules\TextBadge
 * @since ??
 */

namespace MEE\Modules\TextBadge\TextBadgeTrait;

if ( ! defined( 'ABSPATH' ) ) {
	die( 'Direct access forbidden.' );
}

// phpcs:disable ET.Sniffs.ValidVariableName.UsedPropertyNotSnakeCase -- WP use snakeCase in \WP_Block_Parser_Block

use ET\Builder\Packages\Module\Module;
use ET\Builder\Framework\Utility\HTMLUtility;
use ET\Builder\FrontEnd\BlockParser\BlockParserStore;
use ET\Builder\Packages\Module\Options\Element\ElementComponents;
use MEE\Modules\TextBadge\TextBadge;

trait RenderCallbackTrait {

	/**
	 * TextBadge render callback which outputs server side rendered HTML on the Front-End.
	 *
	 * @since ??
	 * @param array          $attrs    Block attributes that were saved by VB.
	 * @param string         $content  Block content.
	 * @param WP_Block       $block    Parsed block object that being rendered.
	 * @param ModuleElements $elements ModuleElements instance.
	 *
	 * @return string HTML rendered of Static module.
	 */
	public static function render_callback( $attrs, $content, $block, $elements ) {

		$badge_placement = $attrs['badgeText']['advanced']['placement']['desktop']['value'] ?? '';
		$use_text = $attrs['mainText']['advanced']['use']['desktop']['value'] ?? 'on';
		$use_image = $attrs['image']['advanced']['use']['desktop']['value'] ?? '';
  
		// image.
		$image = $elements->render(
			[
				'attrName' => 'image',
			]
		);

		// Image container.
		$image_container = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'dotm_image_badge_image_wrapper',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $image,
			]
		);

		// badgeText.
		$badgeText = $elements->render(
			[
				'attrName' => 'badgeText',
			]
		);

		// mainText.
		$mainText = $elements->render(
			[
				'attrName' => 'mainText',
			]
		);

		$parent       = BlockParserStore::get_parent( $block->parsed_block['id'], $block->parsed_block['storeInstance'] );
		$parent_attrs = $parent->attrs ?? [];

		return Module::render(
			[
				// FE only.
				'orderIndex'          => $block->parsed_block['orderIndex'],
				'storeInstance'       => $block->parsed_block['storeInstance'],

				// VB equivalent.
				'attrs'               => $attrs,
				'elements'            => $elements,
				'id'                  => $block->parsed_block['id'],
				'name'                => $block->block_type->name,
				'moduleCategory'      => $block->block_type->category,
				'classnamesFunction'  => [ TextBadge::class, 'module_classnames' ],
				'stylesComponent'     => [ TextBadge::class, 'module_styles' ],
				'scriptDataComponent' => [ TextBadge::class, 'module_script_data' ],
				'parentAttrs'         => $parent_attrs,
				'parentId'            => $parent->id ?? '',
				'parentName'          => $parent->blockName ?? '',
				'children'            => [
					ElementComponents::component(
						[
							'attrs'         => $attrs['module']['decoration'] ?? [],
							'id'            => $block->parsed_block['id'],

							// FE only.
							'orderIndex'    => $block->parsed_block['orderIndex'],
							'storeInstance' => $block->parsed_block['storeInstance'],
						]
					),
					HTMLUtility::render(
						[
							'tag'               => 'div',
							'attributes'        => [
								'class' => 'dotm_text_badge_container',
							],
							'childrenSanitizer' => 'et_core_esc_previously',
							'children'          => ($badge_placement === 'before' ? $badgeText : '')  . ($use_text === 'on' ? $mainText : '') . ($use_image === 'on' ? $image_container : '') . ($badge_placement === 'after' ? $badgeText : '') ,
						]
					),
				],
			]
		);
	}
}
