<?php

/**
 * BeforeAfterSlider::render_callback()
 *
 * @package MEE\Modules\BeforeAfterSlider
 * @since ??
 */

namespace MEE\Modules\BeforeAfterSlider\BeforeAfterSliderTrait;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

// phpcs:disable ET.Sniffs.ValidVariableName.UsedPropertyNotSnakeCase -- WP use snakeCase in \WP_Block_Parser_Block

use ET\Builder\Packages\Module\Module;
use ET\Builder\Framework\Utility\HTMLUtility;
use ET\Builder\FrontEnd\BlockParser\BlockParserStore;
use ET\Builder\Packages\Module\Options\Element\ElementComponents;
use MEE\Modules\BeforeAfterSlider\BeforeAfterSlider;

trait RenderCallbackTrait
{

	/**
	 * BeforeAfterSlider render callback which outputs server side rendered HTML on the Front-End.
	 *
	 * @since ??
	 * @param array          $attrs    Block attributes that were saved by VB.
	 * @param string         $content  Block content.
	 * @param WP_Block       $block    Parsed block object that being rendered.
	 * @param ModuleElements $elements ModuleElements instance.
	 *
	 * @return string HTML rendered of BeforeAfterSlider.
	 */
	public static function render_callback($attrs, $content, $block, $elements)
	{

		$interactionMode = $attrs['slider_container']['advanced']['slide_trigger']['desktop']['value'] ?? '';
		$containerMode = $attrs['slider_container']['advanced']['vertical_mode']['desktop']['value'] ?? '';
		$slideStartingPoint = $attrs['slider_container']['advanced']['slide_starting_point']['desktop']['value'] ?? '';
		$use_labels = $attrs['slider_container']['advanced']['use_labels']['desktop']['value'] ?? '';
		$use_label_on_hover = $attrs['slider_container']['advanced']['use_label_on_hover']['desktop']['value'] ?? '';

		$mode = ($containerMode === 'on') ? 'vertical' : 'horizontal';
		$show_label_on_hover = ($use_label_on_hover === 'on') ? 'show_label_on_hover' : '';

		$option = [
			'interactionMode' => $interactionMode,
			'mode' => $mode,
			'slideStartingPoint' => $slideStartingPoint,
		];


		// before_label.
		$before_label = $elements->render(
			[
				'attrName' => 'before_label',
				'attributes' => [
					'class' => "dotm_before_after_slider_before_label $show_label_on_hover",
				]
			]
		);

		// before_image.
		$before_image = $elements->render(
			[
				'attrName' => 'before_image',
			]
		);

		// after_image.
		$after_image = $elements->render(
			[
				'attrName' => 'after_image',
			]
		);

		// after_label.
		$after_label = $elements->render(
			[
				'attrName' => 'after_label',
				'attributes' => [
					'class' => "dotm_before_after_slider_after_label $show_label_on_hover",
				]
			]
		);

		$left_arrow = HTMLUtility::render(
			[
				'tag'               => 'span',
				'attributes'        => [
					'class' => 'dotm_before_after_slider_left_arrow',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => 'E',
			]
		);

		$right_arrow = HTMLUtility::render(
			[
				'tag'               => 'span',
				'attributes'        => [
					'class' => 'dotm_before_after_slider_right_arrow',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => 'D',
			]
		);

		$arrows = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'dotm_before_after_slider_arrows',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $left_arrow . $right_arrow,
			]
		);

		// slider_line.
		$slider_line = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'dotm_before_after_slider_slide_line',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $arrows,
			]
		);

		$parent       = BlockParserStore::get_parent($block->parsed_block['id'], $block->parsed_block['storeInstance']);
		$parent_attrs = $parent->attrs ?? [];

		return Module::render(
			[
				// FE only.
				'orderIndex'          => $block->parsed_block['orderIndex'],
				'storeInstance'       => $block->parsed_block['storeInstance'],

				// VB equivalent.
				'attrs'               => $attrs,
				'elements'            => $elements,
				'id'                  => $block->parsed_block['id'],
				'name'                => $block->block_type->name,
				'moduleCategory'      => $block->block_type->category,
				'classnamesFunction'  => [BeforeAfterSlider::class, 'module_classnames'],
				'stylesComponent'     => [BeforeAfterSlider::class, 'module_styles'],
				'scriptDataComponent' => [BeforeAfterSlider::class, 'module_script_data'],
				'parentAttrs'         => $parent_attrs,
				'parentId'            => $parent->id ?? '',
				'parentName'          => $parent->blockName ?? '',
				'children'            => [
					ElementComponents::component(
						[
							'attrs'         => $attrs['module']['decoration'] ?? [],
							'id'            => $block->parsed_block['id'],

							// FE only.
							'orderIndex'    => $block->parsed_block['orderIndex'],
							'storeInstance' => $block->parsed_block['storeInstance'],
						]
					),
					HTMLUtility::render(
						[
							'tag'               => 'div',
							'attributes'        => [
								'class' => 'dotm_before_after_slider_image_container',
							],
							'childrenSanitizer' => 'et_core_esc_previously',
							'children'          => ($use_labels === 'on' ? $before_label : '') . $before_image . $after_image . ($use_labels === 'on' ? $after_label : '') . $slider_line,
						]
					),
					HTMLUtility::render(
						[
							'tag' => 'script',
							'attributes' => [],
							'childrenSanitizer' => 'et_core_esc_previously',
							'children' => "document.addEventListener('DOMContentLoaded', function() {
                                const options = " . json_encode($option) . ";
                                beforeAfterImageSlider(options);
                            });",
						]
					),
				],
			]
		);
	}
}
