<?php

/**
 * OptimizeFlipBox::render_callback()
 *
 * @package MEE\Modules\OptimizeFlipBox
 * @since ??
 */

namespace MEE\Modules\OptimizeFlipBox\OptimizeFlipBoxTrait;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

// phpcs:disable ET.Sniffs.ValidVariableName.UsedPropertyNotSnakeCase -- WP use snakeCase in \WP_Block_Parser_Block

use ET\Builder\Packages\Module\Module;
use ET\Builder\Framework\Utility\HTMLUtility;
use ET\Builder\FrontEnd\BlockParser\BlockParserStore;
use ET\Builder\Packages\IconLibrary\IconFont\Utils;
use ET\Builder\Packages\Module\Options\Element\ElementComponents;
use MEE\Modules\OptimizeFlipBox\OptimizeFlipBox;

trait RenderCallbackTrait
{

	/**
	 * OptimizeFlipBox render callback which outputs server side rendered HTML on the Front-End.
	 *
	 * @since ??
	 * @param array          $attrs    Block attributes that were saved by VB.
	 * @param string         $content  Block content.
	 * @param WP_Block       $block    Parsed block object that being rendered.
	 * @param ModuleElements $elements ModuleElements instance.
	 *
	 * @return string HTML rendered of Static module.
	 */
	public static function render_callback($attrs, $content, $block, $elements)
	{
		// Get attributes.
		$flip_box_effect = $attrs['inner_wrapper']['advanced']['flipBoxEffect']['desktop']['value'] ?? '';
		$btn_link = $attrs['btn']['advanced']['link']['desktop']['value']['url'] ?? '';
		$btn_target = $attrs['btn']['advanced']['link']['desktop']['value']['target'] ?? '';
		$use_btn = $attrs['btn']['advanced']['use']['desktop']['value'] ?? '';

		// Get Front Attributes
		$use_front_image = $attrs['front_image']['advanced']['use_front_image']['desktop']['value'] ?? '';
		$use_front_icon = $attrs['front_icon']['advanced']['use_front_icon']['desktop']['value'] ?? '';
		$front_icon = $attrs['front_icon']['innerContent']['desktop']['value'] ?? [];
		$use_front_overlay = $attrs['front_container']['decoration']['background']['desktop']['value']['image']['url'] ?? '';
		$front_image_use_mask = $attrs['front_image']['advanced']['use_mask']['desktop']['value'] ?? '';
		$front_shape_name = $attrs['front_image']['advanced']['shape_name']['desktop']['value'] ?? '';

		// Get Back Attributes
		$use_back_image = $attrs['back_image']['advanced']['use_back_image']['desktop']['value'] ?? '';
		$use_back_icon = $attrs['back_icon']['advanced']['use_back_icon']['desktop']['value'] ?? '';
		$back_icon = $attrs['back_icon']['innerContent']['desktop']['value'] ?? [];
		$use_back_overlay = $attrs['back_container']['decoration']['background']['desktop']['value']['image']['url'] ?? '';
		$back_image_use_mask = $attrs['back_image']['advanced']['use_mask']['desktop']['value'] ?? '';
		$back_shape_name = $attrs['back_image']['advanced']['shape_name']['desktop']['value'] ?? '';


		// Front part.

		// front_image.
		$front_image = $elements->render(
			[
				'attrName' => 'front_image',
				'attributes' => [
					'class' => "dotm_optimize_flip_box_front_img " . ($front_image_use_mask === 'on' ? $front_shape_name : '')
				]
			]
		);

		$front_img_container = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => "dotm_optimize_flip_box_front_img_container",
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $front_image,
			]
		);

		$front_icon = HTMLUtility::render(
			[
				'tag'               => 'span',
				'attributes'        => [
					'class' => "dotm_optimize_flip_box_front_icon",
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => Utils::process_font_icon($front_icon),
			]
		);

		$front_icon_container = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => "dotm_optimize_flip_box_front_icon_container",
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $front_icon,
			]
		);

		// front_title.
		$front_title = $elements->render(
			[
				'attrName' => 'front_title',
			]
		);

		// front_content.
		$front_content = $elements->render(
			[
				'attrName' => 'front_content',
			]
		);

		$front_content_container = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => "dotm_optimize_flip_box_front_content_container",
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $front_title . $front_content,
			]
		);

		$front_threeD_container = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => "dotm_optimize_flip_box_3d_effect",
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => ($use_front_image === 'on' ? $front_img_container : '') . ($use_front_icon === 'on' ? $front_icon_container : '') . $front_content_container,
			]
		);

		$front_overlay = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => "dotm_optimize_flip_box_front_overlay",
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => '',
			]
		);

		$front_container = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => "dotm_optimize_flip_box_front",
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => ($use_front_overlay ? $front_overlay : '') . $front_threeD_container,
			]
		);

		// back part

		$back_image = $elements->render(
			[
				'attrName' => 'back_image',
				'attributes' => [
					'class' => "dotm_optimize_flip_box_back_img " . ($back_image_use_mask === 'on' ? $back_shape_name : '')
				]
			]
		);

		$back_img_container = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => "dotm_optimize_flip_box_back_img_container",
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $back_image,
			]
		);

		$back_icon = HTMLUtility::render(
			[
				'tag'               => 'span',
				'attributes'        => [
					'class' => "dotm_optimize_flip_box_back_icon",
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => Utils::process_font_icon($back_icon),
			]
		);

		$back_icon_container = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => "dotm_optimize_flip_box_back_icon_container",
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $back_icon,
			]
		);

		// back_title.
		$back_title = $elements->render(
			[
				'attrName' => 'back_title',
			]
		);

		// back_content.
		$back_content = $elements->render(
			[
				'attrName' => 'back_content',
			]
		);

		// btn.
		$btn = $elements->render(
			[
				'attrName' => 'btn',
				'attributes' => [
					'class' => "dotm_optimize_flip_box_btn",
					'href' => $btn_link,
					'target' => $btn_target,
				],
			]
		);

		$btn_container = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => "dotm_optimize_flip_box_btn_container",
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $btn,
			]
		);

		$back_content_container = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => "dotm_optimize_flip_box_back_content_container",
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $back_title . $back_content . ($use_btn === 'on' ? $btn_container : ''),
			]
		);

		$back_threeD_container = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => "dotm_optimize_flip_box_3d_effect",
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => ($use_back_image === 'on' ? $back_img_container : '') . ($use_back_icon === 'on' ? $back_icon_container : '') . $back_content_container,
			]
		);

		$back_overlay = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => "dotm_optimize_flip_box_back_overlay",
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => '',
			]
		);

		$back_container = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => "dotm_optimize_flip_box_back",
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => ($use_back_overlay ? $back_overlay : '') . $back_threeD_container,
			]
		);

		// Main container.
		$main_container = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => "dotm_optimize_flip_box_inner dotm_optimize_flip_box_flip_{$flip_box_effect}",
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $front_container . $back_container,
			]
		);

		$parent       = BlockParserStore::get_parent($block->parsed_block['id'], $block->parsed_block['storeInstance']);
		$parent_attrs = $parent->attrs ?? [];

		return Module::render(
			[
				// FE only.
				'orderIndex'          => $block->parsed_block['orderIndex'],
				'storeInstance'       => $block->parsed_block['storeInstance'],

				// VB equivalent.
				'attrs'               => $attrs,
				'elements'            => $elements,
				'id'                  => $block->parsed_block['id'],
				'name'                => $block->block_type->name,
				'moduleCategory'      => $block->block_type->category,
				'classnamesFunction'  => [OptimizeFlipBox::class, 'module_classnames'],
				'stylesComponent'     => [OptimizeFlipBox::class, 'module_styles'],
				'scriptDataComponent' => [OptimizeFlipBox::class, 'module_script_data'],
				'parentAttrs'         => $parent_attrs,
				'parentId'            => $parent->id ?? '',
				'parentName'          => $parent->blockName ?? '',
				'children'            => [
					ElementComponents::component(
						[
							'attrs'         => $attrs['module']['decoration'] ?? [],
							'id'            => $block->parsed_block['id'],

							// FE only.
							'orderIndex'    => $block->parsed_block['orderIndex'],
							'storeInstance' => $block->parsed_block['storeInstance'],
						]
					),
					HTMLUtility::render(
						[
							'tag'               => 'div',
							'attributes'        => [
								'class' => 'dotm_optimize_flip_box',
							],
							'childrenSanitizer' => 'et_core_esc_previously',
							'children'          => $main_container,
						]
					),
				],
			]
		);
	}
}
