<?php
/**
 * TextGradient::module_styles().
 *
 * @package MEE\Modules\TextGradient
 * @since ??
 */

namespace MEE\Modules\TextGradient\TextGradientTrait;

if ( ! defined( 'ABSPATH' ) ) {
	die( 'Direct access forbidden.' );
}

use ET\Builder\FrontEnd\Module\Style;
use ET\Builder\Packages\Module\Layout\Components\StyleCommon\CommonStyle;
use ET\Builder\Packages\Module\Options\Text\TextStyle;
use ET\Builder\Packages\Module\Options\Css\CssStyle;
use MEE\Modules\TextGradient\TextGradient;

trait ModuleStylesTrait {

	use CustomCssTrait;

	/**
	 * Static Module's style components.
	 *
	 * This function is equivalent of JS function ModuleStyles located in
	 * src/components/TextGradient/styles.tsx.
	 *
	 * @since ??
	 *
	 * @param array $args {
	 *     An array of arguments.
	 *
	 *      @type string $id                Module ID. In VB, the ID of module is UUIDV4. In FE, the ID is order index.
	 *      @type string $name              Module name.
	 *      @type string $attrs             Module attributes.
	 *      @type string $parentAttrs       Parent attrs.
	 *      @type string $orderClass        Selector class name.
	 *      @type string $parentOrderClass  Parent selector class name.
	 *      @type string $wrapperOrderClass Wrapper selector class name.
	 *      @type string $settings          Custom settings.
	 *      @type string $state             Attributes state.
	 *      @type string $mode              Style mode.
	 *      @type ModuleElements $elements  ModuleElements instance.
	 * }
	 */
	public static function module_styles( $args ) {
		$attrs    = $args['attrs'] ?? [];
		$elements = $args['elements'];
		$settings = $args['settings'] ?? [];
		$orderClass = $args['orderClass'];

		$use_animation = $attrs['gradient_text']['advanced']['animation_use']['desktop']['value'] ?? 'off';
		$animation_duration = $attrs['gradient_text']['decoration']['animation_duration']['desktop']['value'] ?? '';
		$animation_type = $attrs['gradient_text']['decoration']['animation_type']['desktop']['value'] ?? '';
		$animation_iteration = $attrs['gradient_text']['decoration']['animation_iteration']['desktop']['value'] ?? '';
		$use_reavel = $attrs['gradient_text']['advanced']['reavel_use']['desktop']['value'] ?? 'off';
		$border_width = $attrs['gradient_text']['advanced']['border_width']['desktop']['value'] ?? '';
		$border_color = $attrs['gradient_text']['advanced']['border_color']['desktop']['value'] ?? '';


		$animation_selector = "{$orderClass} .dotm_gradient_text_title";
		$reavel_selector = "{$orderClass} .dotm_gradient_text::before";
		$border_selector = "{$orderClass} .text_hover_effect_border::after";
		$text_selector = "{$orderClass} .dotm_gradient_text";

		Style::add(
			[
				'id'            => $args['id'],
				'name'          => $args['name'],
				'orderIndex'    => $args['orderIndex'],
				'storeInstance' => $args['storeInstance'],
				'styles'        => [
					// Module.
					$elements->style(
						[
							'attrName'   => 'module',
							'styleProps' => [
								'disabledOn'     => [
									'disabledModuleVisibility' => $settings['disabledModuleVisibility'] ?? null,
								],
								'advancedStyles' => [
									[
										'componentName' => 'divi/text',
										'props'         => [
											'selector' => $text_selector,
											'attr'     => $attrs['gradient_text']['advanced']['text'] ?? [],
										]
									]
								]
							],
						]
					),

					// text_container.
					$elements->style(
						[
							'attrName' => 'text_container',
						]
					),

					// gradient_text.
					$elements->style(
						[
							'attrName' => 'gradient_text',
						]
					),

					CommonStyle::style(
						[
							'selector' => $animation_selector ,
							'attr'     => $attrs['gradient_text']['advanced']['animation_use'] ?? [],
							'property' => $use_animation === "on" ? "animation: gradient-animation {$animation_duration} {$animation_type} {$animation_iteration};" : "",
						]
					),

					CommonStyle::style(
						[
							'selector' => $reavel_selector ,
							'attr'     => $attrs['reavel_container']['advanced']['bg_color'] ?? [],
							'property' => $use_reavel === "on" ? "background" : "",
						]
					),

					CommonStyle::style(
						[
							'selector' => $border_selector,
							'attr'     => $attrs['gradient_text']['advanced']['border_color'] ?? [],
							'property' => "background: {$border_color}; height: {$border_width};"
						]
					),

					/*
					 * We need to add CssStyle at the very bottom of other
					 * components so that custom css can override module styles
					 * till we find a more elegant solution.
					 */
					CssStyle::style(
						[
							'selector'  => $args['orderClass'],
							'attr'      => $attrs['css'] ?? [],
							'cssFields' => TextGradient::custom_css(),
						]
					),

					// ATTENTION: The code is intentionally added and commented in FE only as an example of expected value format.
					// If you have custom style processing, the style output should be passed as an `array` of style declarations
					// to the `styles` property of the `Style::add` method. For example:
					// [
					// 	[
					// 		'atRules'     => false,
					// 		'selector'    => "{$args['orderClass']} .example_static_module__content-container",
					// 		'declaration' => 'color: red;'
					// 	],
					// 	[
					// 		'atRules'     => '@media only screen and (max-width: 767px)',
					// 		'selector'    => "{$args['orderClass']} .example_static_module__content-container",
					// 		'declaration' => 'color: green;'
					// 	],
					// ],

					// The code below is an example of how to use the `CssStyle::style` method to generate CSS style.
					CssStyle::style(
						[
							'selector'  => $args['orderClass'],
							'attr'      => $attrs['css'] ?? [],
							'cssFields' => TextGradient::custom_css(),
						]
					),
				],
			]
		);
	}
}
