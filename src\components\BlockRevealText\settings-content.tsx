// External dependencies.
import React, { ReactElement } from "react";

// WordPress dependencies
import { __ } from "@wordpress/i18n";

// Divi dependencies.
import {
  AdminLabelGroup,
  BackgroundGroup,
  FieldContainer,
  LinkGroup,
} from "@divi/module";
import { GroupContainer } from "@divi/modal";
import {
  ColorPickerContainer,
  RangeContainer,
  SelectContainer,
  TextContainer,
} from "@divi/field-library";
import { type Module } from "@divi/types";
import { BlockRevealTextAttrs } from "./types";

export const SettingsContent = ({
  defaultSettingsAttrs,
}: Module.Settings.Panel.Props<BlockRevealTextAttrs>): ReactElement => (
  <React.Fragment>
    <GroupContainer
      id="mainContent"
      title={__("content", "divi-optimaizer-modules")}
    >
      <FieldContainer
        attrName="revealText.innerContent"
        label={__("Reveal Text", "divi-optimaizer-modules")}
        description={__(
          "Input your value to action title here.",
          "divi-optimaizer-modules"
        )}
        features={{
          sticky: false,
        }}
      >
        <TextContainer />
      </FieldContainer>
    </GroupContainer>

    <GroupContainer
      id="RevealAnimation"
      title={__("Reveal Animation", "divi-optimaizer-modules")}
    >
      <FieldContainer
        attrName="revealAnimation.advanced.display_type"
        label={__("Reveal Text Display Type", "divi-optimaizer-modules")}
        description={__(
          "Select your display type.",
          "divi-optimaizer-modules"
        )}
        features={{
          sticky: false,
        }}
        defaultAttr={defaultSettingsAttrs?.revealAnimation?.advanced?.display_type}
      >
        <SelectContainer
          options={{
            "block": { label: "Block" },
            "inline-block": { label: "Inline Block" },
          }}
        />
      </FieldContainer>

      <FieldContainer
        attrName="revealAnimation.advanced.type"
        label={__("Reveal Animation Type", "divi-optimaizer-modules")}
        description={__(
          "Select your animation type.",
          "divi-optimaizer-modules"
        )}
        features={{
          sticky: false,
        }}
        defaultAttr={defaultSettingsAttrs?.revealAnimation?.advanced?.type}
      >
        <SelectContainer
          options={{
            "left_to_right": { label: "Left To Right" },
            "right_to_left": { label: "Right To Left" },
            "top_to_bottom": { label: "Top To Bottom" },
            "bottom_to_top": { label: "Bottom to Top" },
          }}
        />
      </FieldContainer>

      <FieldContainer
        attrName="revealAnimation.advanced.revealColor"
        label={__("Reveal Animation Color", "divi-optimaizer-modules")}
        description={__(
          "Select your animation type.",
          "divi-optimaizer-modules"
        )}
        features={{
          sticky: false,
        }}

        defaultAttr={defaultSettingsAttrs?.revealAnimation?.advanced?.revealColor}
      >
        <ColorPickerContainer />
      </FieldContainer>

      <FieldContainer
        attrName="revealAnimation.advanced.revealDelay"
        label={__("Reveal Delay", "divi-optimaizer-modules")}
        description={__(
          "Select your reveal delay",
          "divi-optimaizer-modules"
        )}
        features={{
          sticky: false,
        }}

        defaultAttr={defaultSettingsAttrs?.revealAnimation?.advanced?.revealDelay}
      >
        <RangeContainer defaultUnit="" step={100} min={0} max={10000} />
      </FieldContainer>
    </GroupContainer>

    <LinkGroup />
    <BackgroundGroup
      defaultGroupAttr={
        defaultSettingsAttrs?.module?.decoration?.background?.asMutable({
          deep: true,
        }) ?? {}
      }
    />
    <AdminLabelGroup />
  </React.Fragment>
);
