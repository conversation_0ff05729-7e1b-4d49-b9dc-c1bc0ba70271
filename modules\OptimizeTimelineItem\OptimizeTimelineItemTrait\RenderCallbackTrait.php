<?php

/**
 * OptimizeTimelineItem::render_callback()
 *
 * @package MEE\Modules\OptimizeTimelineItem
 * @since ??
 */

namespace MEE\Modules\OptimizeTimelineItem\OptimizeTimelineItemTrait;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

// phpcs:disable ET.Sniffs.ValidVariableName.UsedPropertyNotSnakeCase -- WP use snakeCase in \WP_Block_Parser_Block

use ET\Builder\Packages\Module\Module;
use ET\Builder\Framework\Utility\HTMLUtility;
use ET\Builder\Packages\IconLibrary\IconFont\Utils;
use ET\Builder\FrontEnd\BlockParser\BlockParserStore;
use ET\Builder\Packages\Module\Options\Element\ElementComponents;
use ET\Builder\Packages\ModuleLibrary\ModuleRegistration;
use MEE\Modules\OptimizeTimelineItem\OptimizeTimelineItem;

trait RenderCallbackTrait
{
	use ModuleClassnamesTrait;
	use ModuleStylesTrait;

	/**
	 * OptimizeTimelineItem render callback which outputs server side rendered HTML on the Front-End.
	 *
	 * @since ??
	 *
	 * @param array          $attrs Block attributes that were saved by VB.
	 * @param string         $content          Block content.
	 * @param WP_Block       $block            Parsed block object that being rendered.
	 * @param ModuleElements $elements         ModuleElements instance.
	 *
	 * @return string HTML rendered of Child module.
	 */
	public static function render_callback($attrs, $content, $block, $elements)
	{
		$parent = BlockParserStore::get_parent($block->parsed_block['id'], $block->parsed_block['storeInstance']);

		$parent_attrs = $parent->attrs ?? [];

		$parent_default_attributes = ModuleRegistration::get_default_attrs('dotm/optimize-timeline');
		$parent_attrs_with_default = array_replace_recursive($parent_default_attributes, $parent_attrs);

		// Icon.
		$icon_value = $attrs['icon']['innerContent']['desktop']['value'] ?? $parent_attrs_with_default['icon']['innerContent']['desktop']['value'] ?? [];

		$pointerIcon = $attrs['pointerIcon']['innerContent']['desktop']['value'] ?? "";

		$use_icon = $attrs['icon']['advanced']['useIcon']['desktop']['value'] ?? 'off';
		$content_type = $attrs['container']['advanced']['contentType']['desktop']['value'] ?? 'custom-content';
		$icon_image = $attrs['pointer']['advanced']['iconImage']['desktop']['value'] ?? 'icon';

		$btn_link = $attrs['button']['advanced']['link']['desktop']['value']['url'] ?? '';
		$btn_target = $attrs['button']['advanced']['link']['desktop']['value']['target'] ?? '';

		

		

		// pointerImage.
		$pointerImage = $elements->render(
			[
				'attrName'      => 'pointerImage',
				'hoverSelector' => '{{parentSelector}}',
			]
		);

		// Content container.
		$pointer_container = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'dotm_optimize_timeline_item_pointer_icon',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => ($icon_image === "icon" ? Utils::process_font_icon($pointerIcon) : ($icon_image === "image" ? $pointerImage : '')),
			]
		);

		// image.
		$image = $elements->render(
			[
				'attrName'      => 'image',
				'hoverSelector' => '{{parentSelector}}',
			]
		);

		$icon       = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'dotm_optimize_timeline_item_icon',
				],
				'childrenSanitizer' => 'esc_html',
				'children'          => Utils::process_font_icon($icon_value),
			]
		);

		$icon_container      = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'dotm_optimize_timeline_item_icon_container',
				],
				'childrenSanitizer' => 'esc_html',
				'children'          => $icon,
			]
		);

		// Title.
		$title = $elements->render(
			[
				'attrName'      => 'title',
				'hoverSelector' => '{{parentSelector}}',
			]
		);

		// Content.
		$content = $elements->render(
			[
				'attrName'      => 'content',
				'hoverSelector' => '{{parentSelector}}',
			]
		);

		$button = $elements->render(
			[
				'attrName'      => 'button',
				'attributes' => [
					'class' => "dotm_optimize_timeline_item_button",
					'href' => $btn_link,
					'target' => $btn_target === "on" ? "_blank" : "self",
				],
			]
		);

		$button_container = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'dotm_optimize_timeline_item_button_container',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $button,
			]
		);

		$timeline_content_wrapper = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'dotm_optimize_timeline_content_wrapper',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $title . ($content_type === "custom-content" ? $content : "") . ($content_type === "shortcode" ? "ShortCode Content" : "") . $button_container,
			]
		);

		// Content container.
		$image_container = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'dotm_optimize_timeline_item_image_container',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => ($use_icon === "off" ? $image : "") . ($use_icon === "on" ? $icon_container : "") . $timeline_content_wrapper,
			]
		);

		$oppsiteText = $elements->render(
			[
				'attrName'      => 'oppsiteText',
				'hoverSelector' => '{{parentSelector}}',
			]
		);

		$opposite_text_container = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'dotm_optimize_timeline_item_oppsite_text_container',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $oppsiteText,
			]
		);

		

		// timeline_content_container.
		$timeline_content_container = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'dotm_optimize_timeline_item_timeline_content',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $image_container,
			]
		);

		return Module::render(
			[
				// FE only.
				'orderIndex'         => $block->parsed_block['orderIndex'],
				'storeInstance'      => $block->parsed_block['storeInstance'],

				// VB equivalent.
				'id'                 => $block->parsed_block['id'],
				'name'               => $block->block_type->name,
				'moduleCategory'     => $block->block_type->category,
				'attrs'              => $attrs,
				'elements'           => $elements,
				'classnamesFunction' => [OptimizeTimelineItem::class, 'module_classnames'],
				'stylesComponent'    => [OptimizeTimelineItem::class, 'module_styles'],
				'parentAttrs'        => $parent_attrs,
				'parentId'           => $parent->id ?? '',
				'parentName'         => $parent->blockName ?? '',
				'children'           => ElementComponents::component(
					[
						'attrs'         => $attrs['module']['decoration'] ?? [],
						'id'            => $block->parsed_block['id'],

						// FE only.
						'orderIndex'    => $block->parsed_block['orderIndex'],
						'storeInstance' => $block->parsed_block['storeInstance'],
					]
				) . $pointer_container . $timeline_content_container . $opposite_text_container,
			]
		);
	}
}
