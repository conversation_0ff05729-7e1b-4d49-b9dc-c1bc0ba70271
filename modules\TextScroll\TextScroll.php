<?php

/**
 * Module: TextScroll class.
 *
 * @package MEE\Modules\TextScroll
 * @since ??
 */

namespace MEE\Modules\TextScroll;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

use ET\Builder\Framework\DependencyManagement\Interfaces\DependencyInterface;
use ET\Builder\Packages\ModuleLibrary\ModuleRegistration;


/**
 * `TextScroll` is consisted of functions used for Static Module such as Front-End rendering, REST API Endpoints etc.
 *
 * This is a dependency class and can be used as a dependency for `DependencyTree`.
 *
 * @since ??
 */
class TextScroll implements DependencyInterface
{
	use TextScrollTrait\RenderCallbackTrait;
	use TextScrollTrait\ModuleClassnamesTrait;
	use TextScrollTrait\ModuleStylesTrait;
	use TextScrollTrait\ModuleScriptDataTrait;

	/**
	 * Loads `TextScroll` and registers Front-End render callback and REST API Endpoints.
	 *
	 * @since ??
	 *
	 * @return void
	 */
	public function load()
	{
		$module_json_folder_path = DIVIOPTIMIZER_MODULES_JSON_PATH . 'TextScroll/';

		add_action(
			'init',
			function () use ($module_json_folder_path) {
				ModuleRegistration::register_module(
					$module_json_folder_path,
					[
						'render_callback' => [TextScroll::class, 'render_callback'],
					]
				);
			}
		);
	}
}
