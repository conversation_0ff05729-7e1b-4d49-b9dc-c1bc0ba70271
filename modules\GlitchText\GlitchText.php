<?php

/**
 * Module: Static Module class.
 *
 * @package MEE\Modules\GlitchText
 * @since ??
 */

namespace MEE\Modules\GlitchText;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

use ET\Builder\Framework\DependencyManagement\Interfaces\DependencyInterface;
use ET\Builder\Packages\ModuleLibrary\ModuleRegistration;


/**
 * `GlitchText` is consisted of functions used for Static Module such as Front-End rendering, REST API Endpoints etc.
 *
 * This is a dependency class and can be used as a dependency for `DependencyTree`.
 *
 * @since ??
 */
class GlitchText implements DependencyInterface
{
	use GlitchTextTrait\RenderCallbackTrait;
	use GlitchTextTrait\ModuleClassnamesTrait;
	use GlitchTextTrait\ModuleStylesTrait;
	use GlitchTextTrait\ModuleScriptDataTrait;

	/**
	 * Loads `GlitchText` and registers Front-End render callback and REST API Endpoints.
	 *
	 * @since ??
	 *
	 * @return void
	 */
	public function load()
	{
		$module_json_folder_path = DIVIOPTIMIZER_MODULES_JSON_PATH . 'GlitchText/';

		add_action(
			'init',
			function () use ($module_json_folder_path) {
				ModuleRegistration::register_module(
					$module_json_folder_path,
					[
						'render_callback' => [GlitchText::class, 'render_callback'],
					]
				);
			}
		);
	}
}
