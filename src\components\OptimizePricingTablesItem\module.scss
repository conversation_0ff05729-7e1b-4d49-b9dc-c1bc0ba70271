.dotm_optimize_pricing_tables {
  display: flex;
  justify-content: center;
  // gap: 20px;
}

.dotm_optimize_pricing_tables_item {
  position: relative !important;
  // z-index: 100 !important;
}

.dotm_optimize_pricing_tables_item_card {
  overflow: hidden;
  width: 100%;
}

.dotm_optimize_pricing_tables_item_price_section {
  display: flex;
  align-items: flex-end;
}

.dotm_optimize_pricing_tables_item_price_wrapper {
  display: flex;
  align-items: flex-start;
  justify-content: center;
}

.dotm_optimize_pricing_tables_item_features {
  padding: 30px 20px;
}

.dotm_optimize_pricing_tables_item_button_container {
  text-align: center;
}

.dotm_optimize_pricing_tables_item_button {
  color: #000;
}

.dotm_optimize_pricing_tables_item_button:after {
  margin-left: 10px !important;
  transition: all 0.3s ease-in-out;
}

.dotm_optimize_pricing_tables_item_button:before {
  margin-left: 0 !important;
  margin-right: 10px;
  transition: all 0.3s ease-in-out;
}

.dotm_optimize_pricing_tables_item_icon {
  display: inline-block;
}

.dotm_optimize_pricing_tables_item_button {
  display: inline-block;
}

.dotm_optimize_pricing_tables_item_image {
  object-fit: cover;
}


.dotm_optimize_pricing_tables_item_featureText {
  position: absolute;
  left: 50%;
  transform: translateX(-50%)
}