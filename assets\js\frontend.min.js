jQuery(function (t) { var s = t(".doptm-typing-effect .doptm-typing"); t(".doptm-typing-effect ").length && t(s).each(function (s, a) { var e = t(this).data("doptm-typing-strings").split("|"), d = t(this).data("doptm-typing-loop"), i = parseFloat(t(this).data("doptm-typing-speed"), 10), n = parseFloat(t(this).data("doptm-typing-backdelay"), 10), p = parseFloat(t(this).data("doptm-typing-backspeed"), 10), o = t(this).data("doptm-typing-cursor"), y = t(this).data("doptm-typing-fadeout"), r = t(this).data("doptm-typing-shuffle"), h = parseFloat(t(this).data("doptm-typing-delay"), 10) + 500, g = t(this).data("doptm-typing-viewport"), l = t(this).data("doptm-typing-repeat"), m = this, c = { strings: e, loop: d, startDelay: h, typeSpeed: i, backSpeed: p, backDelay: n, cursorChar: o, fadeOut: y, shuffle: r, contentType: "null", onComplete: s => { "on" == t(this).data("doptm-typing-remove-cursor") && t(this).next(".typed-cursor").hide() } }, f = ""; t(this).waypoint({ handler: function (t) { "on" === l ? "down" === t ? f = new Typed(m, c) : f.destroy() : (this.destroy(), f = new Typed(m, c)) }, offset: g }) }) });