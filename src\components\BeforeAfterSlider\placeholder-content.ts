// Divi dependencies.
import {placeholderContent as placeholder} from '@divi/module';

// Local dependencies.
import {BeforeAfterSliderAttrs} from './types';
import { getPlaceholderTitle, getPlaceholderBody, getPlaceholderImage } from '../../utils/placeholder-fallbacks';


export const placeholderContent: BeforeAfterSliderAttrs = {
  before_label: {
    innerContent: {
      desktop: {
        value: "Before Label",
      },
    }
  },
  after_label: {
    innerContent: {
      desktop: {
        value: "After Label",
      },
    }
  },
  content: {
    innerContent: {
      desktop: {
        value: getPlaceholderBody(placeholder, 'Your content goes here.'),
      },
    }
  },
  before_image: {
    innerContent: {
      desktop: {
        value: {
          // src: "https://thumbs.dreamstime.com/b/environment-earth-day-hands-trees-growing-seedlings-bokeh-green-background-female-hand-holding-tree-nature-field-gra-130247647.jpg",
          src: getPlaceholderImage(placeholder, 'portrait'),
          alt: "Before Image",
        },
      },
    },
  },
  after_image: {
    innerContent: {
      desktop: {
        value: {
          src: getPlaceholderImage(placeholder, 'landscape'),
          alt: "After Image",
        },
      },
    },
  },
};
