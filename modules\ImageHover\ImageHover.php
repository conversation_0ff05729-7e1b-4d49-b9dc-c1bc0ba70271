<?php

/**
 * Module: Static Module class.
 *
 * @package MEE\Modules\ImageHover
 * @since ??
 */

namespace MEE\Modules\ImageHover;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

use ET\Builder\Framework\DependencyManagement\Interfaces\DependencyInterface;
use ET\Builder\Packages\ModuleLibrary\ModuleRegistration;


/**
 * `ImageHover` is consisted of functions used for Static Module such as Front-End rendering, REST API Endpoints etc.
 *
 * This is a dependency class and can be used as a dependency for `DependencyTree`.
 *
 * @since ??
 */
class ImageHover implements DependencyInterface
{
	use ImageHoverTrait\RenderCallbackTrait;
	use ImageHoverTrait\ModuleClassnamesTrait;
	use ImageHoverTrait\ModuleStylesTrait;
	use ImageHoverTrait\ModuleScriptDataTrait;

	/**
	 * Loads `ImageHover` and registers Front-End render callback and REST API Endpoints.
	 *
	 * @since ??
	 *
	 * @return void
	 */
	public function load()
	{
		$module_json_folder_path = DIVIOPTIMIZER_MODULES_JSON_PATH . 'ImageHover/';

		add_action(
			'init',
			function () use ($module_json_folder_path) {
				ModuleRegistration::register_module(
					$module_json_folder_path,
					[
						'render_callback' => [ImageHover::class, 'render_callback'],
					]
				);
			}
		);
	}
}
