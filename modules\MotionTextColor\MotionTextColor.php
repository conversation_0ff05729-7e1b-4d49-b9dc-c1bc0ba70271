<?php

/**
 * Module: MotionTextColor class.
 *
 * @package MEE\Modules\MotionTextColor
 * @since ??
 */

namespace MEE\Modules\MotionTextColor;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

use ET\Builder\Framework\DependencyManagement\Interfaces\DependencyInterface;
use ET\Builder\Packages\ModuleLibrary\ModuleRegistration;


/**
 * `MotionTextColor` is consisted of functions used for Static Module such as Front-End rendering, REST API Endpoints etc.
 *
 * This is a dependency class and can be used as a dependency for `DependencyTree`.
 *
 * @since ??
 */
class MotionTextColor implements DependencyInterface
{
	use MotionTextColorTrait\RenderCallbackTrait;
	use MotionTextColorTrait\ModuleClassnamesTrait;
	use MotionTextColorTrait\ModuleStylesTrait;
	use MotionTextColorTrait\ModuleScriptDataTrait;

	/**
	 * Loads `MotionTextColor` and registers Front-End render callback and REST API Endpoints.
	 *
	 * @since ??
	 *
	 * @return void
	 */
	public function load()
	{
		$module_json_folder_path = DIVIOPTIMIZER_MODULES_JSON_PATH . 'MotionTextColor/';

		add_action(
			'init',
			function () use ($module_json_folder_path) {
				ModuleRegistration::register_module(
					$module_json_folder_path,
					[
						'render_callback' => [MotionTextColor::class, 'render_callback'],
					]
				);
			}
		);
	}
}
