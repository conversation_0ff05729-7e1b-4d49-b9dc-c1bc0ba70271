# Static Module
It is an example of a static module. It will help you to understand the basic Divi 5 module without dynamic content. This module will contain the tests and storybook example too.

## Folder Structure
```
static-module
├── README.md
├── __mock-data__
│   └── attrs.ts
├── __tests__
│   ├── __snapshots__
│   │   └── edit.tsx.snap
│   └── edit.tsx
├── custom-css.ts
├── edit.tsx
├── index.ts
├── module.json
├── module.scss
├── placeholder-content.ts
├── settings-advanced.tsx
├── settings-content.tsx
├── settings-design.tsx
├── stories
│   └── edit.stories.tsx
├── style.scss
├── styles.tsx
└── types.ts
```
