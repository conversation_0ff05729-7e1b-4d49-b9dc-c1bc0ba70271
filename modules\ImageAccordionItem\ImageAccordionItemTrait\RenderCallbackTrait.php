<?php

/**
 * ImageAccordionItem::render_callback()
 *
 * @package MEE\Modules\ImageAccordionItem
 * @since ??
 */

namespace MEE\Modules\ImageAccordionItem\ImageAccordionItemTrait;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

// phpcs:disable ET.Sniffs.ValidVariableName.UsedPropertyNotSnakeCase -- WP use snakeCase in \WP_Block_Parser_Block

use ET\Builder\Packages\Module\Module;
use ET\Builder\Framework\Utility\HTMLUtility;
use ET\Builder\FrontEnd\BlockParser\BlockParserStore;
use ET\Builder\Packages\IconLibrary\IconFont\Utils;
use ET\Builder\Packages\Module\Options\Element\ElementComponents;
use ET\Builder\Packages\ModuleLibrary\ModuleRegistration;
use MEE\Modules\ImageAccordionItem\ImageAccordionItem;

trait RenderCallbackTrait
{
	use ModuleClassnamesTrait;
	use ModuleStylesTrait;

	/**
	 * ImageAccordionItem render callback which outputs server side rendered HTML on the Front-End.
	 *
	 * @since ??
	 *
	 * @param array          $attrs Block attributes that were saved by VB.
	 * @param string         $content          Block content.
	 * @param WP_Block       $block            Parsed block object that being rendered.
	 * @param ModuleElements $elements         ModuleElements instance.
	 *
	 * @return string HTML rendered of Child module.
	 */
	public static function render_callback($attrs, $content, $block, $elements)
	{
		$parent = BlockParserStore::get_parent($block->parsed_block['id'], $block->parsed_block['storeInstance']);

		$parent_attrs = $parent->attrs ?? [];

		$parent_default_attributes = ModuleRegistration::get_default_attrs('dotm/image-accordion');
		$parent_attrs_with_default = array_replace_recursive($parent_default_attributes, $parent_attrs);

		$use_icon = $attrs['icon']['advanced']['use']['desktop']['value'] ?? '';
		$use_image = $attrs['image_as_icon']['advanced']['use']['desktop']['value'] ?? '';
		$button_link = $attrs['button']['desktop']['value']['url'] ?? '';
		$button_target = $attrs['button']['desktop']['value']['target'] ?? '';


		// image_as_icon.
		$image_as_icon = $elements->render(
			[
				'attrName'      => 'image_as_icon',
				'hoverSelector' => '{{parentSelector}}',
			]
		);


		// Image as icon container.
		$image_as_icon_container = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'dotm_image_accordion_item_image_as_icon_container',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $image_as_icon,
			]
		);


		// Icon.
		$icon_value = $attrs['icon']['innerContent']['desktop']['value'] ?? [];

		$icon       = HTMLUtility::render(
			[
				'tag'               => 'span',
				'attributes'        => [
					'class' => 'dotm_image_accordion_item_icon',
				],
				'childrenSanitizer' => 'esc_html',
				'children'          => Utils::process_font_icon($icon_value),
			]
		);

		$icon_container       = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'dotm_image_accordion_item_icon_container',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $icon,
			]
		);

		// Image.
		$image = $elements->render(
			[
				'attrName'      => 'image',
				'hoverSelector' => '{{parentSelector}}',
			]
		);

		

		// Title.
		$title = $elements->render(
			[
				'attrName'      => 'title',
				'hoverSelector' => '{{parentSelector}}',
			]
		);

		// sub_title.
		$sub_title = $elements->render(
			[
				'attrName'      => 'sub_title',
				'hoverSelector' => '{{parentSelector}}',
			]
		);

		// Content.
		$content = $elements->render(
			[
				'attrName'      => 'content',
				'hoverSelector' => '{{parentSelector}}',
			]
		);

		// button.
		$button = $elements->render(
			[
				'attrName'      => 'button',
				'hoverSelector' => '{{parentSelector}}',
				'attributes'    => [
					'target' => $button_target === "on" ? '_blank' : '_self',
					'href'   => $button_link,
					'class'         => 'dotm_image_accordion_item__button'
				],
			]
		);

		// Button container.
		$button_container = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'dotm_image_accordion_item_button_container',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $button,
			]
		);

		// Content container.
		$content_container = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'dotm_image_accordion_item_contents',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => ($use_image == 'on' ? $image_as_icon_container : '') . ($use_icon == 'on' ? $icon_container : '') . $title . $content . $button_container,
			]
		);

		// overlay.
		$overlay = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'dotm_image_accordion_item_overlay',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $content_container,
			]
		);



		return Module::render(
			[
				// FE only.
				'orderIndex'         => $block->parsed_block['orderIndex'],
				'storeInstance'      => $block->parsed_block['storeInstance'],

				// VB equivalent.
				'id'                 => $block->parsed_block['id'],
				'name'               => $block->block_type->name,
				'moduleCategory'     => $block->block_type->category,
				'attrs'              => $attrs,
				'elements'           => $elements,
				'classnamesFunction' => [ImageAccordionItem::class, 'module_classnames'],
				'stylesComponent'    => [ImageAccordionItem::class, 'module_styles'],
				'parentAttrs'        => $parent_attrs,
				'parentId'           => $parent->id ?? '',
				'parentName'         => $parent->blockName ?? '',
				'children'           => ElementComponents::component(
					[
						'attrs'         => $attrs['module']['decoration'] ?? [],
						'id'            => $block->parsed_block['id'],


						// FE only.
						'orderIndex'    => $block->parsed_block['orderIndex'],
						'storeInstance' => $block->parsed_block['storeInstance'],
					]
				) . $image . $overlay,
			]
		);
	}
}
