// External dependencies.
import React, { ReactElement } from "react";

// WordPress dependencies
import { __ } from "@wordpress/i18n";

// Divi dependencies.
import { BackgroundGroup, FieldContainer, LinkGroup } from "@divi/module";
import { GroupContainer } from "@divi/modal";
import {
  IconPickerContainer,
  RangeContainer,
  RichTextContainer,
  TextContainer,
  ToggleContainer,
  UploadContainer,
} from "@divi/field-library";
import { mergeAttrs } from "@divi/module-utils";
import { type Module } from "@divi/types";

// Local dependencies.
import { OptimizePricingTablesItemAttrs } from "./types";
import { OptimizePricingTablesAttrs } from "../OptimizePricingTables/types";

export const SettingsContent = ({
  defaultSettingsAttrs,
  parentAttrs,
  attrs,
}: Module.Settings.Panel.Props<
  OptimizePricingTablesItemAttrs,
  OptimizePricingTablesAttrs
>): ReactElement => {
  const defaultIconAttrs = mergeAttrs({
    defaultAttrs: defaultSettingsAttrs?.icon,
    attrs: parentAttrs?.asMutable({ deep: true })?.icon,
  });

  const useImage = attrs?.image?.advanced?.useImage?.desktop?.value ?? 'off';
  const useFeature = attrs?.featureText?.advanced?.useFeature?.desktop?.value ?? 'off';

  return (
    <React.Fragment>
      <GroupContainer
        id="imageIcon"
        title={__("Image & Icon", "divi-optimaizer-modules")}
      >
        <FieldContainer
          attrName="icon.innerContent"
          label={__('Icon', 'divi-optimaizer-modules')}
          description={__('Input your value to action icon here.', 'divi-optimaizer-modules')}
          features={{
            sticky: false,
          }}
          visible={useImage === 'off'}
        >
          <IconPickerContainer />
        </FieldContainer>

        <FieldContainer
          attrName="image.advanced.useImage"
          label={__('Use Image', 'divi-optimaizer-modules')}
          description={__('Input your value to action image here.', 'divi-optimaizer-modules')}
          features={{
            sticky: false,
          }}
        >
          <ToggleContainer />
        </FieldContainer>

        <FieldContainer
          attrName="image.innerContent"
          subName='src'
          label={__('Image', 'divi-optimaizer-modules')}
          description={__('Input your value to action image here.', 'divi-optimaizer-modules')}
          features={{
            sticky: false,
          }}
          visible={useImage === 'on'}
        >
          <UploadContainer />
        </FieldContainer>
      </GroupContainer>
      <GroupContainer
        id="mainContent"
        title={__("Contents", "divi-optimaizer-modules")}
      >
        <FieldContainer
          attrName="title.innerContent"
          label={__("Title", "divi-optimaizer-modules")}
          description={__(
            "Input your value to action title here.",
            "divi-optimaizer-modules"
          )}
          features={{
            sticky: false,
          }}
        >
          <TextContainer />
        </FieldContainer>
        <FieldContainer
          attrName="subTitle.innerContent"
          label={__("Subtitle", "divi-optimaizer-modules")}
          description={__(
            "Input your value to action here.",
            "divi-optimaizer-modules"
          )}
          features={{
            sticky: false,
          }}
        >
          <TextContainer />
        </FieldContainer>
        <FieldContainer
          attrName="currency.innerContent"
          label={__("Currency", "divi-optimaizer-modules")}
          description={__(
            "Input your value to action here.",
            "divi-optimaizer-modules"
          )}
          features={{
            sticky: false,
          }}
        >
          <TextContainer />
        </FieldContainer>
        <FieldContainer
          attrName="frequency.innerContent"
          label={__("Frequency", "divi-optimaizer-modules")}
          description={__(
            "Input your value to action here.",
            "divi-optimaizer-modules"
          )}
          features={{
            sticky: false,
          }}
        >
          <TextContainer />
        </FieldContainer>
        <FieldContainer
          attrName="price.innerContent"
          label={__("Price", "divi-optimaizer-modules")}
          description={__(
            "Input your value to action here.",
            "divi-optimaizer-modules"
          )}
          features={{
            sticky: false,
          }}
        >
          <TextContainer />
        </FieldContainer>
        <FieldContainer
          attrName="content.innerContent"
          label={__("Content", "divi-optimaizer-modules")}
          description={__(
            "Input the main text content for your module here.",
            "divi-optimaizer-modules"
          )}
          features={{
            sticky: false,
          }}
        >
          <RichTextContainer />
        </FieldContainer>
      </GroupContainer>
      <GroupContainer
        id="button"
        title={__("Button", "divi-optimaizer-modules")}
      >
        <FieldContainer
          attrName="button.innerContent"
          label={__("Button", "divi-optimaizer-modules")}
          description={__(
            "Input your value to action here.",
            "divi-optimaizer-modules"
          )}
          features={{
            sticky: false,
          }}
        >
          <TextContainer />
        </FieldContainer>
        <LinkGroup attrName='button.advanced.link' grouped={false} />
      </GroupContainer>
      <GroupContainer 
        id="icon" 
        title={__("Icon", "divi-optimaizer-modules")}
      >
        <FieldContainer
          attrName="icon.innerContent"
          label={__("Icon", "divi-optimaizer-modules")}
          description={__("Pick an Icon", "divi-optimaizer-modules")}
          features={{
            sticky: false,
          }}
          defaultAttr={defaultIconAttrs}
        >
          <IconPickerContainer />
        </FieldContainer>
      </GroupContainer>
      <GroupContainer
        id="extraFeature"
        title={__("Extra Features", "divi-optimaizer-modules")}
      >
        <FieldContainer
          attrName="featureText.advanced.useFeature"
          label={__('Use Extra Feature', 'divi-optimaizer-modules')}
          description={__('Input your value to action here.', 'divi-optimaizer-modules')}
          features={{
            sticky: false,
          }}
        >
          <ToggleContainer />
        </FieldContainer>
        <FieldContainer
          attrName="featureText.innerContent"
          label={__('Feature Badge Text', 'divi-optimaizer-modules')}
          description={__('Input your value to action here.', 'divi-optimaizer-modules')}
          features={{
            sticky: false,
          }}
          visible={useFeature === 'on'}
        >
          <TextContainer />
        </FieldContainer>
        <FieldContainer
          attrName="featureText.advanced.highlight"
          label={__('Scale', 'divi-optimaizer-modules')}
          description={__('Input your value to action here.', 'divi-optimaizer-modules')}
          features={{
            sticky: false,
          }}
          defaultAttr={defaultSettingsAttrs?.featureText?.advanced?.highlight}
          visible={useFeature === 'on'}
        >
          <RangeContainer defaultUnit="" min={0} max={2} step={0.1} />
        </FieldContainer>
        <FieldContainer
          attrName="featureText.advanced.zIndex"
          label={__('Z-index', 'divi-optimaizer-modules')}
          description={__('Input your value to action here.', 'divi-optimaizer-modules')}
          features={{
            sticky: false,
          }}
          defaultAttr={defaultSettingsAttrs?.featureText?.advanced?.zIndex}
          visible={useFeature === 'on'}
        >
          <RangeContainer defaultUnit="" min={3} max={10} />
        </FieldContainer>
        <FieldContainer
          attrName="featureText.advanced.badgePlacement"
          label={__('Badge Placement', 'divi-optimaizer-modules')}
          description={__('Input your value to action here.', 'divi-optimaizer-modules')}
          features={{
            sticky: false,
          }}
          defaultAttr={defaultSettingsAttrs?.featureText?.advanced?.badgePlacement}
          visible={useFeature === 'on'}
        >
          <RangeContainer />
        </FieldContainer>
      </GroupContainer>
      <LinkGroup />
      <BackgroundGroup
        defaultGroupAttr={
          defaultSettingsAttrs?.module?.decoration?.background?.asMutable({
            deep: true,
          }) ?? {}
        }
      />
    </React.Fragment>
  );
};
