<?php

/**
 * Module: Static Module class.
 *
 * @package MEE\Modules\PdfViewer
 * @since ??
 */

namespace MEE\Modules\PdfViewer;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

use ET\Builder\Framework\DependencyManagement\Interfaces\DependencyInterface;
use ET\Builder\Packages\ModuleLibrary\ModuleRegistration;


/**
 * `PdfViewer` is consisted of functions used for Static Module such as Front-End rendering, REST API Endpoints etc.
 *
 * This is a dependency class and can be used as a dependency for `DependencyTree`.
 *
 * @since ??
 */
class PdfViewer implements DependencyInterface
{
	use PdfViewerTrait\RenderCallbackTrait;
	use PdfViewerTrait\ModuleClassnamesTrait;
	use PdfViewerTrait\ModuleStylesTrait;
	use PdfViewerTrait\ModuleScriptDataTrait;

	/**
	 * Loads `PdfViewer` and registers Front-End render callback and REST API Endpoints.
	 *
	 * @since ??
	 *
	 * @return void
	 */
	public function load()
	{
		$module_json_folder_path = DIVIOPTIMIZER_MODULES_JSON_PATH . 'PdfViewer/';

		add_action(
			'init',
			function () use ($module_json_folder_path) {
				ModuleRegistration::register_module(
					$module_json_folder_path,
					[
						'render_callback' => [PdfViewer::class, 'render_callback'],
					]
				);
			}
		);
	}
}
