<?php

/**
 * Module: Static Module class.
 *
 * @package MEE\Modules\GoogleMap
 * @since ??
 */

namespace MEE\Modules\GoogleMap;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

use ET\Builder\Framework\DependencyManagement\Interfaces\DependencyInterface;
use ET\Builder\Packages\ModuleLibrary\ModuleRegistration;


/**
 * `GoogleMap` is consisted of functions used for Static Module such as Front-End rendering, REST API Endpoints etc.
 *
 * This is a dependency class and can be used as a dependency for `DependencyTree`.
 *
 * @since ??
 */
class GoogleMap implements DependencyInterface
{
	use GoogleMapTrait\RenderCallbackTrait;
	use GoogleMapTrait\ModuleClassnamesTrait;
	use GoogleMapTrait\ModuleStylesTrait;
	use GoogleMapTrait\ModuleScriptDataTrait;

	/**
	 * Loads `GoogleMap` and registers Front-End render callback and REST API Endpoints.
	 *
	 * @since ??
	 *
	 * @return void
	 */
	public function load()
	{
		$module_json_folder_path = DIVIOPTIMIZER_MODULES_JSON_PATH . 'GoogleMap/';

		add_action(
			'init',
			function () use ($module_json_folder_path) {
				ModuleRegistration::register_module(
					$module_json_folder_path,
					[
						'render_callback' => [GoogleMap::class, 'render_callback'],
					]
				);
			}
		);
	}
}
