// External Dependencies.
import React, { ReactElement } from 'react';

// Divi Dependencies.
import { ChildModulesContainer, ModuleContainer } from '@divi/module';

// Local Dependencies.
import { OptimizePricingTablesEditProps } from './types';
import { ModuleStyles } from './styles';
import { ModuleScriptData } from './module-script-data';
import { moduleClassnames } from './module-classnames';

/**
 * OptimizePricingTables edit component of visual builder.
 *
 * @since ??
 *
 * @param {OptimizePricingTablesEditProps} props React component props.
 *
 * @returns {ReactElement}
 */
export const OptimizePricingTablesEdit = (props: OptimizePricingTablesEditProps): ReactElement => {
  const {
    attrs,
    elements,
    id,
    name,
    childrenIds,
  } = props;

  return (
    <ModuleContainer
      attrs={attrs}
      elements={elements}
      id={id}
      name={name}
      stylesComponent={ModuleStyles}
      scriptDataComponent={ModuleScriptData}
      classnamesFunction={moduleClassnames}
      tag="div"
    >
      {elements.styleComponents({
        attrName: 'module',
      })}
      <ChildModulesContainer ids={childrenIds} />
    </ModuleContainer>
  );
}
