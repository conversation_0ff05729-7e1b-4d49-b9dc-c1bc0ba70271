<?php

/**
 * Module: OptimizeButtonItem class.
 *
 * @package MEE\Modules\OptimizeButtonItem
 * @since ??
 */

namespace MEE\Modules\OptimizeButtonItem;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

use ET\Builder\Framework\DependencyManagement\Interfaces\DependencyInterface;
use ET\Builder\Packages\ModuleLibrary\ModuleRegistration;

/**
 * `OptimizeButtonItem` is consisted of functions used for Child Module such as Front-End rendering, REST API Endpoints etc.
 *
 * This is a dependency class and can be used as a dependency for `DependencyTree`.
 *
 * @since ??
 */
class OptimizeButtonItem implements DependencyInterface
{
	use OptimizeButtonItemTrait\RenderCallbackTrait;

	/**
	 * Loads `OptimizeButtonItem` and registers Front-End render callback and REST API Endpoints.
	 *
	 * @since ??
	 *
	 * @return void
	 */
	public function load()
	{
		$module_json_folder_path = DIVIOPTIMIZER_MODULES_JSON_PATH . 'OptimizeButtonItem/';

		add_action(
			'init',
			function () use ($module_json_folder_path) {
				ModuleRegistration::register_module(
					$module_json_folder_path,
					[
						'render_callback' => [OptimizeButtonItem::class, 'render_callback'],
					]
				);
			}
		);
	}
}
