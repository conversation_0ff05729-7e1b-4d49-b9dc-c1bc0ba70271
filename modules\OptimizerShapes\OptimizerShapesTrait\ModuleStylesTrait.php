<?php
/**
 * OptimizerShapes::module_styles().
 *
 * @package MEE\Modules\OptimizerShapes
 * @since ??
 */

namespace MEE\Modules\OptimizerShapes\OptimizerShapesTrait;

if ( ! defined( 'ABSPATH' ) ) {
	die( 'Direct access forbidden.' );
}

use ET\Builder\FrontEnd\Module\Style;
use ET\Builder\Packages\Module\Options\Text\TextStyle;
use ET\Builder\Packages\Module\Options\Css\CssStyle;
use MEE\Modules\OptimizerShapes\OptimizerShapes;

trait ModuleStylesTrait {

	use CustomCssTrait;

	/**
	 * Static Module's style components.
	 *
	 * This function is equivalent of JS function ModuleStyles located in
	 * src/components/static-module/styles.tsx.
	 *
	 * @since ??
	 *
	 * @param array $args {
	 *     An array of arguments.
	 *
	 *      @type string $id                Module ID. In VB, the ID of module is UUIDV4. In FE, the ID is order index.
	 *      @type string $name              Module name.
	 *      @type string $attrs             Module attributes.
	 *      @type string $parentAttrs       Parent attrs.
	 *      @type string $orderClass        Selector class name.
	 *      @type string $parentOrderClass  Parent selector class name.
	 *      @type string $wrapperOrderClass Wrapper selector class name.
	 *      @type string $settings          Custom settings.
	 *      @type string $state             Attributes state.
	 *      @type string $mode              Style mode.
	 *      @type ModuleElements $elements  ModuleElements instance.
	 * }
	 */
	public static function module_styles( $args ) {
		$attrs    = $args['attrs'] ?? [];
		$elements = $args['elements'];
		$settings = $args['settings'] ?? [];
		$orderClass = $args['orderClass'];

		$shapeSelector  = "{$orderClass} .dotm_shapes_wrapper";
		$shape_type = $attrs['shapes']['innerContent']['desktop']['value'] ?? '';
		$shapeSize = $attrs['shapesSize']['innerContent']['desktop']['value'] ?? '';

		Style::add(
			[
				'id'            => $args['id'],
				'name'          => $args['name'],
				'orderIndex'    => $args['orderIndex'],
				'storeInstance' => $args['storeInstance'],
				'styles'        => [
					// Module.
					$elements->style(
						[
							'attrName'   => 'module',
							'styleProps' => [
								'disabledOn'     => [
									'disabledModuleVisibility' => $settings['disabledModuleVisibility'] ?? null,
								],
								'advancedStyles' => [
									[
										'componentName' => 'divi/text',
										'props'         => [
											'selector' => "{$args['orderClass']} .example_static_module__content-container",
											'attr'     => $attrs['module']['advanced']['text'] ?? [],
										]
									],

									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $shapeSelector,
											'attr'     => $attrs['shapesSize']['innerContent'] ?? [],
											'property' => ($shape_type === "square") ? "width: {$shapeSize}; height: {$shapeSize};" : '',
										]
									],

									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $shapeSelector,
											'attr'     => $attrs['shapesSize']['innerContent'] ?? [],
											'property' => ($shape_type === "circle") ? "width: {$shapeSize}; height: {$shapeSize};" : '',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $shapeSelector,
											'attr'     => $attrs['shapesSize']['innerContent'] ?? [],
											'property' => ($shape_type === "rectangle" ? "width:{$shapeSize}; height:calc({$shapeSize} / 2);" : ''),
										]
									],

									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $shapeSelector,
											'attr'     => $attrs['shapesSize']['innerContent'] ?? [],
											'property' =>($shape_type === "oval" ? "width:{$shapeSize}; height:calc({$shapeSize} / 2); border-radius:50%;" : ''),
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $shapeSelector,
											'attr'     => $attrs['shapesSize']['innerContent'] ?? [],
											'property' =>($shape_type === "parallelogram" ? "width:{$shapeSize}; height:calc({$shapeSize} / 2);" : ''),
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $shapeSelector,
											'attr'     => $attrs['shapesSize']['innerContent'] ?? [],
											'property' =>	($shape_type === "blob-two" ? "width:{$shapeSize}; height:{$shapeSize};" : '') ,
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $shapeSelector,
											'attr'     => $attrs['shapesSize']['innerContent'] ?? [],
											'property' =>	($shape_type === "blob-one" ? "width:{$shapeSize}; height:{$shapeSize};" : '') ,
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $shapeSelector,
											'attr'     => $attrs['shapesSize']['innerContent'] ?? [],
											'property' =>	($shape_type === "octagon" ? "width:{$shapeSize}; height:calc({$shapeSize} * 0.866);" : ''),
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $shapeSelector,
											'attr'     => $attrs['shapesSize']['innerContent'] ?? [],
											'property' =>	($shape_type === "heptagon" ? "width:{$shapeSize}; height:calc({$shapeSize} * 0.95);" : '') ,
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $shapeSelector,
											'attr'     => $attrs['shapesSize']['innerContent'] ?? [],
											'property' =>	($shape_type === "pentagon" ? "width:{$shapeSize}; height:{$shapeSize};" : '') ,
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $shapeSelector,
											'attr'     => $attrs['shapesSize']['innerContent'] ?? [],
											'property' =>	($shape_type === "diamond-square" ? "width:{$shapeSize}; height:{$shapeSize};" : ''),
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $shapeSelector,
											'attr'     => $attrs['shapesSize']['innerContent'] ?? [],
											'property' =>	($shape_type === "blob-eleven" ? "width:{$shapeSize}; height:{$shapeSize};" : ''),
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $shapeSelector,
											'attr'     => $attrs['shapesSize']['innerContent'] ?? [],
											'property' =>($shape_type === "blob-ten" ? "width:{$shapeSize}; height:{$shapeSize};" : '') 
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $shapeSelector,
											'attr'     => $attrs['shapesSize']['innerContent'] ?? [],
											'property' =>	($shape_type === "blob-nine" ? "width:{$shapeSize}; height:{$shapeSize};" : '') ,
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $shapeSelector,
											'attr'     => $attrs['shapesSize']['innerContent'] ?? [],
											'property' =>($shape_type === "blob-eight" ? "width:{$shapeSize}; height:{$shapeSize};" : '') ,
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $shapeSelector,
											'attr'     => $attrs['shapesSize']['innerContent'] ?? [],
											'property' =>	($shape_type === "blob-seven" ? "width:{$shapeSize}; height:{$shapeSize};" : '') ,
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $shapeSelector,
											'attr'     => $attrs['shapesSize']['innerContent'] ?? [],
											'property' =>($shape_type === "blob-six" ? "width:{$shapeSize}; height:{$shapeSize};" : '') ,
										]
									],

									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $shapeSelector,
											'attr'     => $attrs['shapesSize']['innerContent'] ?? [],
											'property' =>($shape_type === "blob-five" ? "width:{$shapeSize}; height:{$shapeSize};" : '') ,
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $shapeSelector,
											'attr'     => $attrs['shapesSize']['innerContent'] ?? [],
											'property' =>($shape_type === "blob-three" ? "width:{$shapeSize}; height:{$shapeSize};" : '') ,
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $shapeSelector,
											'attr'     => $attrs['shapesSize']['innerContent'] ?? [],
											'property' =>	($shape_type === "blob-two" ? "width:{$shapeSize}; height:{$shapeSize};" : '') ,
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $shapeSelector,
											'attr'     => $attrs['shapesSize']['innerContent'] ?? [],
											'property' =>	($shape_type === "blob-one" ? "width:{$shapeSize}; height:{$shapeSize};" : '') ,
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $shapeSelector,
											'attr'     => $attrs['shapesSize']['innerContent'] ?? [],
											'property' =>	($shape_type === "blob-four" ? "width:{$shapeSize}; height:{$shapeSize};" : '') ,
										]
									],
									

										
								]
							],
						]
					),

					// Image.
					$elements->style(
						[
							'attrName' => 'image',
						]
					),
					$elements->style(
						[
							'attrName' => 'shapes',
						]
					),

					// Title.
					$elements->style(
						[
							'attrName' => 'title',
						]
					),

					// Content.
					$elements->style(
						[
							'attrName' => 'content',
						]
					),

					/*
					 * We need to add CssStyle at the very bottom of other
					 * components so that custom css can override module styles
					 * till we find a more elegant solution.
					 */
					CssStyle::style(
						[
							'selector'  => $args['orderClass'],
							'attr'      => $attrs['css'] ?? [],
							'cssFields' => OptimizerShapes::custom_css(),
						]
					),

					// ATTENTION: The code is intentionally added and commented in FE only as an example of expected value format.
					// If you have custom style processing, the style output should be passed as an `array` of style declarations
					// to the `styles` property of the `Style::add` method. For example:
					// [
					// 	[
					// 		'atRules'     => false,
					// 		'selector'    => "{$args['orderClass']} .example_static_module__content-container",
					// 		'declaration' => 'color: red;'
					// 	],
					// 	[
					// 		'atRules'     => '@media only screen and (max-width: 767px)',
					// 		'selector'    => "{$args['orderClass']} .example_static_module__content-container",
					// 		'declaration' => 'color: green;'
					// 	],
					// ],

					// The code below is an example of how to use the `CssStyle::style` method to generate CSS style.
					CssStyle::style(
						[
							'selector'  => $args['orderClass'],
							'attr'      => $attrs['css'] ?? [],
							'cssFields' => OptimizerShapes::custom_css(),
						]
					),
				],
			]
		);
	}
}
