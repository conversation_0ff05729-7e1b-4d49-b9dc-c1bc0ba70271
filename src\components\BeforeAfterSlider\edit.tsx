// External Dependencies.
import React, { ReactElement, useEffect } from 'react';
import classnames from 'classnames';

// Divi Dependencies.
import { ModuleContainer } from '@divi/module';

// Local Dependencies.
import { BeforeAfterSliderEditProps } from './types';
import { ModuleStyles } from './styles';
import { moduleClassnames } from './module-classnames';
import { ModuleScriptData } from './module-script-data';
import { beforeAfterImageSlider } from './script/beforeAfterSlide.js';

/**
 * BeforeAfterSlider edit component of visual builder.
 *
 * @since ??
 *
 * @param {BeforeAfterSliderEditProps} props React component props.
 *
 * @returns {ReactElement}
 */
export const BeforeAfterSliderEdit = (props: BeforeAfterSliderEditProps): ReactElement => {
  const {
    attrs,
    elements,
    id,
    name,
  } = props;

  console.log(attrs, 'attrs');

  // Get attributes.
  const interactionMode = attrs?.slider_container?.advanced?.slide_trigger?.desktop?.value ?? '';
  const containerMode = attrs?.slider_container?.advanced?.vertical_mode?.desktop?.value ?? '';
  const slideStartingPoint = attrs?.slider_container?.advanced?.slide_starting_point?.desktop?.value ?? '';
  const use_labels = attrs?.slider_container?.advanced?.use_labels?.desktop?.value ?? '';
  const use_label_on_hover = attrs?.slider_container?.advanced?.use_label_on_hover?.desktop?.value ?? '';

  let mode;
  if (containerMode === 'on') {
    mode = 'vertical';
  } else {
    mode = 'horizontal';
  }

  let show_label_on_hover;
  if (use_label_on_hover === 'on') {
    show_label_on_hover = "show_label_on_hover";
  } else {
    show_label_on_hover = '';
  }

  useEffect(() => {
    beforeAfterImageSlider({
      interactionMode,
      mode,
      slideStartingPoint,
    });
  }, [interactionMode, mode, slideStartingPoint]);

  return (
    <ModuleContainer
      attrs={attrs}
      elements={elements}
      id={id}
      name={name}
      stylesComponent={ModuleStyles}
      classnamesFunction={moduleClassnames}
      scriptDataComponent={ModuleScriptData}
    >
      {elements.styleComponents({
        attrName: 'module',
      })}
      <div className="dotm_before_after_slider_image_container">
        {use_labels === 'on' && (
          elements.render({
            attrName: 'before_label',
            className: show_label_on_hover,
          })
        )}
        {elements.render({
          attrName: 'before_image',
        })}
        {elements.render({
          attrName: 'after_image',
        })}
        {use_labels === 'on' && (
          elements.render({
            attrName: 'after_label',
            className: show_label_on_hover,
          })
        )}
        <div className="dotm_before_after_slider_slide_line">
          <div className='dotm_before_after_slider_arrows'>
            <span className='dotm_before_after_slider_left_arrow'>E</span>
            <span className='dotm_before_after_slider_right_arrow'>D</span>
          </div>
        </div>

      </div>
    </ModuleContainer>
  );
};
