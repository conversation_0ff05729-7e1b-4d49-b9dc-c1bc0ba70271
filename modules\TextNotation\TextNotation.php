<?php

/**
 * Module: TextNotation class.
 *
 * @package MEE\Modules\TextNotation
 * @since ??
 */

namespace MEE\Modules\TextNotation;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

use ET\Builder\Framework\DependencyManagement\Interfaces\DependencyInterface;
use ET\Builder\Packages\ModuleLibrary\ModuleRegistration;


/**
 * `TextNotation` is consisted of functions used for Static Module such as Front-End rendering, REST API Endpoints etc.
 *
 * This is a dependency class and can be used as a dependency for `DependencyTree`.
 *
 * @since ??
 */
class TextNotation implements DependencyInterface
{
	use TextNotationTrait\RenderCallbackTrait;
	use TextNotationTrait\ModuleClassnamesTrait;
	use TextNotationTrait\ModuleStylesTrait;
	use TextNotationTrait\ModuleScriptDataTrait;

	/**
	 * Loads `TextNotation` and registers Front-End render callback and REST API Endpoints.
	 *
	 * @since ??
	 *
	 * @return void
	 */
	public function load()
	{
		$module_json_folder_path = DIVIOPTIMIZER_MODULES_JSON_PATH . 'TextNotation/';

		add_action(
			'init',
			function () use ($module_json_folder_path) {
				ModuleRegistration::register_module(
					$module_json_folder_path,
					[
						'render_callback' => [TextNotation::class, 'render_callback'],
					]
				);
			}
		);
	}
}
