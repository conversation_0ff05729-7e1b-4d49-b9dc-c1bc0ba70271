<?php

/**
 * OptimizeTimeline::module_styles().
 *
 * @package MEE\Modules\OptimizeTimeline
 * @since ??
 */

namespace MEE\Modules\OptimizeTimeline\OptimizeTimelineTrait;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

use ET\Builder\FrontEnd\Module\Style;
use ET\Builder\Packages\Module\Layout\Components\StyleCommon\CommonStyle;
use ET\Builder\Packages\Module\Options\Css\CssStyle;
use MEE\Modules\OptimizeTimelineItem\OptimizeTimelineItem;

trait ModuleStylesTrait
{

	use CustomCssTrait;

	/**
	 * Child Module's style components.
	 *
	 * This function is equivalent of JS function ModuleStyles located in
	 * src/components/OptimizeTimeline/styles.tsx.
	 *
	 * @param array $args {
	 *     An array of arguments.
	 *
	 * @type string $id Module ID. In VB, the ID of module is UUIDV4. In FE, the ID is order index.
	 * @type string $name Module name.
	 * @type string $attrs Module attributes.
	 * @type string $parentAttrs Parent attrs.
	 * @type string $orderClass Selector class name.
	 * @type string $parentOrderClass Parent selector class name.
	 * @type string $wrapperOrderClass Wrapper selector class name.
	 * @type string $settings Custom settings.
	 * @type string $state Attributes state.
	 * @type string $mode Style mode.
	 * @type ModuleElements $elements ModuleElements instance.
	 * }
	 * @since ??
	 */
	public static function module_styles($args)
	{
		$attrs        = $args['attrs'] ?? [];
		$parent_attrs = $args['parentAttrs'] ?? [];
		$order_class  = $args['orderClass'];
		$elements     = $args['elements'];
		$settings     = $args['settings'] ?? [];

		$line_selector = "{$order_class} .dotm_optimize_timeline_wrapper::before";
		$line_scroll_selector = "{$order_class} .dotm_optimize_timeline_wrapper::after";
		$timeline_content_selector = "{$order_class} .dotm_optimize_timeline_item_timeline_content";
		$wrapper_before_after_selector = "{$order_class} .dotm_optimize_timeline_wrapper::before, {$order_class} .dotm_optimize_timeline_item_pointer_icon, {$order_class} .dotm_optimize_timeline_wrapper::after";
		$arrow_after_selector = "{$order_class} .dotm_optimize_timeline_item_timeline_content::after";
		$image_container_selector = "{$order_class} .dotm_optimize_timeline_item_image_container";
		$icon_container_selector = "{$order_class} .dotm_optimize_timeline_item_icon_container";
		$oppositeTextContainerSelector = "{$order_class} .dotm_optimize_timeline_item_oppsite_text_container";

		$layout_type = $attrs['layout']['advanced']['layoutType']['desktop']['value'] ?? '';
		$use_arrow = $attrs['layout']['advanced']['useArrow']['desktop']['value'] ?? '';

		Style::add(
			[
				'id'            => $args['id'],
				'name'          => $args['name'],
				'orderIndex'    => $args['orderIndex'],
				'storeInstance' => $args['storeInstance'],
				'styles'        => [
					// Module.
					$elements->style(
						[
							'attrName'   => 'module',
							'styleProps' => [
								'disabledOn' => [
									'disabledModuleVisibility' => $settings['disabledModuleVisibility'] ?? null,
								],
								'advancedStyles' => [
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $line_selector,
											'attr'     => $attrs['line']['decoration']['color'] ?? [],
											'property' => 'background',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $line_scroll_selector,
											'attr'     => $attrs['line']['decoration']['ScrollColor'] ?? [],
											'property' => 'background',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $timeline_content_selector,
											'attr'     => $attrs['layout']['advanced']['layoutType'] ?? [],
											'property' => $layout_type !== "center" ? "width: 100%;" : "",
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $wrapper_before_after_selector,
											'attr'     => $attrs['layout']['advanced']['layoutType'] ?? [],
											'property' => ($layout_type === "right" ? "left: 105%;" : ($layout_type === "left" ? "left: -5%;" : "")),
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $arrow_after_selector,
											'attr'     => $attrs['layout']['advanced']['layoutType'] ?? [],
											'property' => ($layout_type === "right" ? "right: -1%;" : ($layout_type === "left" ? "left: -1%;" : "")),
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $arrow_after_selector,
											'attr'     => $attrs['layout']['advanced']['useArrow'] ?? [],
											'property' => $use_arrow === "on" ? "display: block;" : "display: none;",
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $oppositeTextContainerSelector,
											'attr'     => $attrs['layout']['advanced']['layoutType'] ?? [],
											'property' => $layout_type === "center" ? "display: block;" : "display: none;",
										]
									],
								]
							],
						]
					),

					// line.
					$elements->style(
						[
							'attrName' => 'line',
						]
					),

					// Title.
					$elements->style(
						[
							'attrName' => 'title',
						]
					),

					// Content.
					$elements->style(
						[
							'attrName' => 'content',
						]
					),

					// optional_container.
					$elements->style(
						[
							'attrName' => 'optional_container',
						]
					),

					// container.
					$elements->style(
						[
							'attrName' => 'container',
						]
					),

					// pointerIcon.
					$elements->style(
						[
							'attrName'   => 'pointerIcon',
							'styleProps' => [
								'advancedStyles' => [
									[
										'componentName' => 'divi/common',
										'props'         => [
											'attr'                => $attrs['pointerIcon']['innerContent'] ?? [],
											'declarationFunction' => [OptimizeTimelineItem::class, 'icon_font_declaration'],
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'attr'     => $attrs['pointerIcon']['advanced']['color'] ?? [],
											'property' => 'color',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'attr'     => $attrs['pointerIcon']['advanced']['size'] ?? [],
											'property' => 'font-size',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'attr'     => $attrs['pointerIcon']['decoration']['size'] ?? [],
											'property' => 'width',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'attr'     => $attrs['pointerIcon']['decoration']['size'] ?? [],
											'property' => 'height',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'attr'     => $attrs['pointerIcon']['decoration']['background'] ?? [],
											'property' => 'background',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'attr'     => $attrs['pointerIcon']['decoration']['color'] ?? [],
											'property' => 'color',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'attr'     => $attrs['pointerIcon']['decoration']['IconSize'] ?? [],
											'property' => 'font-size',
										]
									],
								]
							]
						]
					),

					// image.
					$elements->style(
						[
							'attrName'   => 'image',
							'styleProps' => [
								'advancedStyles' => [

									[
										'componentName' => 'divi/common',
										'props'         => [
											'attr'     => $attrs['image']['decoration']['imgWidth'] ?? [],
											'property' => 'width',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $image_container_selector,
											'attr'     => $attrs['image']['decoration']['alignment'] ?? [],
											'property' => 'text-align',
										]
									],
								]
							]
						]
					),

					// button.
					$elements->style(
						[
							'attrName' => 'button',
						]
					),

					// Icon.
					$elements->style(
						[
							'attrName'   => 'icon',
							'styleProps' => [
								'advancedStyles' => [
									[
										'componentName' => 'divi/common',
										'props'         => [
											'attr'                => $attrs['icon']['innerContent'] ?? [],
											'declarationFunction' => [OptimizeTimelineItem::class, 'icon_font_declaration'],
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'attr'     => $attrs['icon']['advanced']['color'] ?? [],
											'property' => 'color',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'attr'     => $attrs['icon']['advanced']['size'] ?? [],
											'property' => 'font-size',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $icon_container_selector,
											'attr'     => $attrs['icon']['advanced']['alignment'] ?? [],
											'property' => 'text-align',
										]
									],
								]
							]
						]
					),

					/*
					 * We need to add CssStyle at the very bottom of other
					 * components so that custom css can override module styles
					 * till we find a more elegant solution.
					 */
					CssStyle::style(
						[
							'selector'  => $args['orderClass'],
							'attr'      => $attrs['css'] ?? [],
							'cssFields' => self::custom_css(),
						]
					),

					// ATTENTION: The code is intentionally added and commented in FE only as an example of expected value format.
					// If you have custom style processing, the style output should be passed as an `array` of style declarations
					// to the `styles` property of the `Style::add` method. For example:
					// [
					// 	[
					// 		'atRules'     => false,
					// 		'selector'    => $icon_selector,
					// 		'declaration' => 'color: red;'
					// 	],
					// 	[
					// 		'atRules'     => '@media only screen and (max-width: 767px)',
					// 		'selector'    => $icon_selector,
					// 		'declaration' => 'color: green;'
					// 	],
					// ],

					// The code below is an example of how to use the `CssStyle::style` method to generate CSS style.
					CssStyle::style(
						[
							'selector'  => $args['orderClass'],
							'attr'      => $attrs['css'] ?? [],
							'cssFields' => self::custom_css(),
						]
					),
				],
			]
		);
	}
}
