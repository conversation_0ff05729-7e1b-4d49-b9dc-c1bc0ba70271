const revealText = (props = {}) => {
    const { animationDelay } = props;

    const observer = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                setTimeout(() => {
                    entry.target.classList.add('reveal');
                }, animationDelay);
                observer.unobserve(entry.target);
            }
        });
    }, {
        threshold: 0.5
    });

    const sections = document.querySelectorAll('.dotm_block_text_reveal_wrapper');
    sections.forEach(section => {
        observer.observe(section);
    });
}

