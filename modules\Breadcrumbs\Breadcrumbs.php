<?php

/**
 * Module: Parent Module class.
 *
 * @package MEE\Modules\Breadcrumbs
 * @since ??
 */

namespace MEE\Modules\Breadcrumbs;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}


use ET\Builder\Framework\DependencyManagement\Interfaces\DependencyInterface;
use ET\Builder\Packages\ModuleLibrary\ModuleRegistration;

/**
 * `Breadcrumbs` is consisted of functions used for Parent Module such as Front-End rendering, REST API Endpoints etc.
 *
 * This is a dependency class and can be used as a dependency for `DependencyTree`.
 *
 * @since ??
 */
class Breadcrumbs implements DependencyInterface
{
	use BreadcrumbsTrait\RenderCallbackTrait;

	/**
	 * Loads `Breadcrumbs` and registers Front-End render callback and REST API Endpoints.
	 *
	 * @since ??
	 *
	 * @return void
	 */
	public function load()
	{
		$module_json_folder_path = DIVIOPTIMIZER_MODULES_JSON_PATH . 'Breadcrumbs/';
		add_action(
			'init',
			function () use ($module_json_folder_path) {
				ModuleRegistration::register_module(
					$module_json_folder_path,
					[
						'render_callback' => [Breadcrumbs::class, 'render_callback'],
					]
				);
			}
		);
	}
}
