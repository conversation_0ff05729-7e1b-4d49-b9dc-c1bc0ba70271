<?php

/**
 * OptimizeImageTilt::module_styles().
 *
 * @package MEE\Modules\OptimizeImageTilt
 * @since ??
 */

namespace MEE\Modules\OptimizeImageTilt\OptimizeImageTiltTrait;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

use ET\Builder\FrontEnd\Module\Style;
use ET\Builder\Packages\Module\Options\Text\TextStyle;
use ET\Builder\Packages\Module\Options\Css\CssStyle;
use MEE\Modules\OptimizeImageTilt\OptimizeImageTilt;


trait ModuleStylesTrait
{

	use CustomCssTrait;
	use StyleDeclarationTrait;

	/**
	 * OptimizeImageTilt's style components.
	 *
	 * This function is equivalent of JS function ModuleStyles located in
	 * src/components/OptimizeImageTilt/styles.tsx.
	 *
	 * @since ??
	 *
	 * @param array $args {
	 *     An array of arguments.
	 *
	 *      @type string $id                Module ID. In VB, the ID of module is UUIDV4. In FE, the ID is order index.
	 *      @type string $name              Module name.
	 *      @type string $attrs             Module attributes.
	 *      @type string $parentAttrs       Parent attrs.
	 *      @type string $orderClass        Selector class name.
	 *      @type string $parentOrderClass  Parent selector class name.
	 *      @type string $wrapperOrderClass Wrapper selector class name.
	 *      @type string $settings          Custom settings.
	 *      @type string $state             Attributes state.
	 *      @type string $mode              Style mode.
	 *      @type ModuleElements $elements  ModuleElements instance.
	 * }
	 */
	public static function module_styles($args)
	{
		$attrs    = $args['attrs'] ?? [];
		$elements = $args['elements'];
		$settings = $args['settings'] ?? [];
		$orderClass = $args['orderClass'];

		$tilt_container_selector = "{$orderClass} .dotm_image_tilt_container";
		$image_selector = "{$orderClass} .dotm_image_tilt_image";
		$hover_content_selector = "{$orderClass} .dotm_image_tilt_container:hover .dotm_image_tilt_content_container";
		$content_selector = "{$orderClass} .dotm_image_tilt_content_container";
		$icon_container_selector = "{$orderClass} .dotm_image_tilt_icon_container";

		$tilt_speed = $attrs['tilt']['decoration']['speed']['desktop']['value'] ?? '';
		$use_3d = $attrs['tilt']['decoration']['use_3d']['desktop']['value'] ?? '';
		$use_content = $attrs['tilt']['decoration']['use_content']['desktop']['value'] ?? '';
		$content_vr_placement = $attrs['content_container']['decoration']['vr_placement']['desktop']['value'] ?? '';

		Style::add(
			[
				'id'            => $args['id'],
				'name'          => $args['name'],
				'orderIndex'    => $args['orderIndex'],
				'storeInstance' => $args['storeInstance'],
				'styles'        => [
					// Module.
					$elements->style(
						[
							'attrName'   => 'module',
							'styleProps' => [
								'disabledOn'     => [
									'disabledModuleVisibility' => $settings['disabledModuleVisibility'] ?? null,
								],
								'advancedStyles' => [
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $tilt_container_selector,
											'attr'     => $attrs['tilt']['decoration']['perspective'] ?? [],
											'property' => 'perspective',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $tilt_container_selector,
											'attr'     => $attrs['tilt']['decoration']['speed'] ?? [],
											'property' => "transition: transform {$tilt_speed} ease-out;",
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $use_content === 'on' ? $hover_content_selector : $content_selector,
											'attr'     => $attrs['tilt']['decoration']['use_content'] ?? [],
											'property' => $use_content === 'on' ? 'opacity: 1;' : 'opacity: 1;',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $content_selector,
											'attr'     => $attrs['content_container']['decoration']['hr_placement'] ?? [],
											'property' => "left: {$content_vr_placement}; top",
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $content_selector,
											'attr'     => $attrs['tilt']['decoration']['use_3d'] ?? [],
											'property' => $use_3d === 'on' ? 'transform: translateZ(50px);' : '',
										]
									]
								]
							],
						]
					),

					// Icon.
					$elements->style(
						[
							'attrName'   => 'icon',
							'styleProps' => [
								'advancedStyles' => [
									[
										'componentName' => 'divi/common',
										'props'         => [
											'attr'                => $attrs['icon']['innerContent'] ?? [],
											'declarationFunction' => [OptimizeImageTilt::class, 'icon_font_declaration'],
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'attr'     => $attrs['icon']['advanced']['color'] ?? [],
											'property' => 'color',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'attr'     => $attrs['icon']['advanced']['size'] ?? [],
											'property' => 'font-size',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $icon_container_selector,
											'attr'     => $attrs['icon']['advanced']['alignment'] ?? [],
											'property' => 'text-align',
										]
									],
								]
							]
						]
					),

					// Image.
					$elements->style(
						[
							'attrName' => 'image',
						]
					),

					// overlay.
					$elements->style(
						[
							'attrName' => 'overlay',
						]
					),

					// content_container.
					$elements->style(
						[
							'attrName' => 'content_container',
						]
					),


					// Title.
					$elements->style(
						[
							'attrName' => 'title',
						]
					),

					// Content.
					$elements->style(
						[
							'attrName' => 'content',
						]
					),

					/*
					 * We need to add CssStyle at the very bottom of other
					 * components so that custom css can override module styles
					 * till we find a more elegant solution.
					 */
					CssStyle::style(
						[
							'selector'  => $args['orderClass'],
							'attr'      => $attrs['css'] ?? [],
							'cssFields' => OptimizeImageTilt::custom_css(),
						]
					),

					// ATTENTION: The code is intentionally added and commented in FE only as an example of expected value format.
					// If you have custom style processing, the style output should be passed as an `array` of style declarations
					// to the `styles` property of the `Style::add` method. For example:
					// [
					// 	[
					// 		'atRules'     => false,
					// 		'selector'    => "{$args['orderClass']} .example_static_module__content-container",
					// 		'declaration' => 'color: red;'
					// 	],
					// 	[
					// 		'atRules'     => '@media only screen and (max-width: 767px)',
					// 		'selector'    => "{$args['orderClass']} .example_static_module__content-container",
					// 		'declaration' => 'color: green;'
					// 	],
					// ],

					// The code below is an example of how to use the `CssStyle::style` method to generate CSS style.
					CssStyle::style(
						[
							'selector'  => $args['orderClass'],
							'attr'      => $attrs['css'] ?? [],
							'cssFields' => OptimizeImageTilt::custom_css(),
						]
					),
				],
			]
		);
	}
}
