function createMarquee(options = {}) {
    const container = document.querySelector('.dotm_text_scroll_marquee_container');
    const textElement = document.querySelector('.dotm_text_scroll_marquee_text');

    if (!container || !textElement) {
        console.error("Marquee container or text element not found.");
        return;
    }

    // Destructure options with defaults
    const {
        marquee_speed = 2,          // Animation speed (pixels per frame)
        marquee_gap = 0,            // Gap between duplicated content
        marquee_delay = 0,          // Delay before the animation starts (ms)
        marquee_direction = 'left', // Direction of scrolling ('left', 'right', 'top', 'bottom')
        duplicated = false,         // Whether the content is duplicated for seamless scrolling
        pauseOnHover = false        // Pause scrolling when hovering
    } = options;

    const isHorizontal = marquee_direction === 'left' || marquee_direction === 'right';
    const isVertical = marquee_direction === 'top' || marquee_direction === 'bottom';

    let containerDimension, textDimension;

    if (isHorizontal) {
        containerDimension = container.offsetWidth;
        textDimension = textElement.scrollWidth;
    } else if (isVertical) {
        containerDimension = container.offsetHeight;
        textDimension = textElement.scrollHeight;
    }

    if (duplicated) {
        // Duplicate content for seamless scrolling
        if (isHorizontal) {
            textElement.innerHTML += '&nbsp;'.repeat(marquee_gap) + textElement.innerHTML;
            textDimension = textElement.scrollWidth;
        } else if (isVertical) {
            textElement.innerHTML += `<br />`.repeat(marquee_gap) + textElement.innerHTML;
            textDimension = textElement.scrollHeight;
        }
    }

    if (marquee_direction === 'right' || marquee_direction === 'bottom') {
        textElement.innerHTML = textElement.innerHTML.split(' ').reverse().join(' ');
    }

    // Normalize marquee speed based on the content size for consistency
    const normalizedSpeed = marquee_speed / (textDimension / 1000); // Adjust speed relative to content size

    // Define start and reset positions for all directions
    const startPosition =
        marquee_direction === 'left' || marquee_direction === 'top'
            ? containerDimension
            : -textDimension;

    const resetPosition =
        marquee_direction === 'left' || marquee_direction === 'top'
            ? -textDimension
            : containerDimension;

    // Apply initial positioning
    if (isHorizontal) {
        textElement.style.left = `${startPosition}px`;
    } else if (isVertical) {
        textElement.style.top = `${startPosition}px`;
    }

    let animationFrame;

    // Animation function
    const animate = () => {
        const step = marquee_direction === 'left' || marquee_direction === 'top'
            ? -normalizedSpeed
            : normalizedSpeed;

        if (isHorizontal) {
            const currentPos = parseFloat(textElement.style.left);
            if (
                (marquee_direction === 'left' && currentPos <= resetPosition) ||
                (marquee_direction === 'right' && currentPos >= resetPosition)
            ) {
                textElement.style.left = `${startPosition}px`;
            } else {
                textElement.style.left = `${currentPos + (step)}px`;
                textElement.style.right = `${currentPos + (step)}px`;
            }
        } else if (isVertical) {
            const currentPos = parseFloat(textElement.style.top);
            if (
                (marquee_direction === 'top' && currentPos <= resetPosition) ||
                (marquee_direction === 'bottom' && currentPos >= resetPosition)
            ) {
                textElement.style.top = `${startPosition}px`;
            } else {
                textElement.style.top = `${currentPos + (step / 100)}px`;
                textElement.style.bottom = `${currentPos + (step / 100)}px`;
            }
        }

        animationFrame = requestAnimationFrame(animate);
    };

    // Start the marquee animation
    const startMarquee = () => {
        cancelAnimationFrame(animationFrame); // Reset any ongoing animations
        animationFrame = requestAnimationFrame(animate);
    };

    // Pause marquee animation
    const pauseMarquee = () => {
        cancelAnimationFrame(animationFrame);
    };

    // Pause and resume on hover
    if (pauseOnHover) {
        container.addEventListener('mouseover', pauseMarquee); // Pause on mouse enter
        container.addEventListener('mouseout', startMarquee); // Resume on mouse leave
    }

    // Start the marquee animation after the delay
    setTimeout(startMarquee, marquee_delay);
}
