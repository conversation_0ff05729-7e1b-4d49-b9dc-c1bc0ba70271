import { ModuleClassnamesParams, textOptionsClassnames } from '@divi/module';
import { BeforeAfterSliderAttrs } from './types';


/**
 * Module classnames function for BeforeAfterSlider.
 *
 * @since ??
 *
 * @param {ModuleClassnamesParams<BeforeAfterSliderAttrs>} param0 Function parameters.
 */
export const moduleClassnames = ({
  classnamesInstance,
  attrs,
}: ModuleClassnamesParams<BeforeAfterSliderAttrs>): void => {
  // Text Options.
  classnamesInstance.add(textOptionsClassnames(attrs?.module?.advanced?.text));
};
