<?php

/**
 * FbVideo::render_callback()
 *
 * @package MEE\Modules\FbVideo
 * @since ??
 */

namespace MEE\Modules\FbVideo\FbVideoTrait;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

// phpcs:disable ET.Sniffs.ValidVariableName.UsedPropertyNotSnakeCase -- WP use snakeCase in \WP_Block_Parser_Block

use ET\Builder\Packages\Module\Module;
use ET\Builder\Framework\Utility\HTMLUtility;
use ET\Builder\FrontEnd\BlockParser\BlockParserStore;
use ET\Builder\Packages\Module\Options\Element\ElementComponents;
use MEE\Modules\FbVideo\FbVideo;

trait RenderCallbackTrait
{

	/**
	 * Static module render callback which outputs server side rendered HTML on the Front-End.
	 *
	 * @since ??
	 * @param array          $attrs    Block attributes that were saved by VB.
	 * @param string         $content  Block content.
	 * @param WP_Block       $block    Parsed block object that being rendered.
	 * @param ModuleElements $elements ModuleElements instance.
	 *
	 * @return string HTML rendered of Static module.
	 */
	public static function render_callback($attrs, $content, $block, $elements)
	{

		$fbVideourl = isset($attrs['fbvideourl']['innerContent']['desktop']['value']) ? $attrs['fbvideourl']['innerContent']['desktop']['value'] : '';
		$allowFullScreen = isset($attrs['fbvideostyle']['decoration']['allowFullScreen']['desktop']['value']) ? $attrs['fbvideostyle']['decoration']['allowFullScreen']['desktop']['value'] : '';
		$width = $allowFullScreen === 'on' ? '' : (isset($attrs['fbvideostyle']['decoration']['width']['desktop']['value']) ? $attrs['fbvideostyle']['decoration']['width']['desktop']['value'] : '');
		$autoPlay = isset($attrs['fbvideostyle']['decoration']['autoPlay']['desktop']['value']) ? $attrs['fbvideostyle']['decoration']['autoPlay']['desktop']['value'] : '';
		$showCaption = isset($attrs['fbvideostyle']['decoration']['showCaption']['desktop']['value']) ? $attrs['fbvideostyle']['decoration']['showCaption']['desktop']['value'] : '';


		$anchor = HTMLUtility::render(
			[
				'tag'               => 'a',
				'attributes'        => [
					'href' => $fbVideourl,
				],
				'childrenSanitizer' => 'et_core_esc_previously',

			]
		);

		$blockquote = HTMLUtility::render(
			[
				'tag'               => 'blockquote',
				'attributes'        => [
					'cite' => $fbVideourl,
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $anchor,
			]
		);
		$fbXfbml = HTMLUtility::render(
			[
				'tag'               => 'fb:video',
				'attributes'        => [
					'class' => 'fb-xfbml-parse-ignore'
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $blockquote,
			]
		);
		$parent       = BlockParserStore::get_parent($block->parsed_block['id'], $block->parsed_block['storeInstance']);
		$parent_attrs = $parent->attrs ?? [];

		return Module::render(
			[
				// FE only.
				'orderIndex'          => $block->parsed_block['orderIndex'],
				'storeInstance'       => $block->parsed_block['storeInstance'],

				// VB equivalent.
				'attrs'               => $attrs,
				'elements'            => $elements,
				'id'                  => $block->parsed_block['id'],
				'name'                => $block->block_type->name,
				'moduleCategory'      => $block->block_type->category,
				'classnamesFunction'  => [FbVideo::class, 'module_classnames'],
				'stylesComponent'     => [FbVideo::class, 'module_styles'],
				'scriptDataComponent' => [FbVideo::class, 'module_script_data'],
				'parentAttrs'         => $parent_attrs,
				'parentId'            => $parent->id ?? '',
				'parentName'          => $parent->blockName ?? '',
				'children'            => [
					ElementComponents::component(
						[
							'attrs'         => $attrs['module']['decoration'] ?? [],
							'id'            => $block->parsed_block['id'],

							// FE only.
							'orderIndex'    => $block->parsed_block['orderIndex'],
							'storeInstance' => $block->parsed_block['storeInstance'],
						]
					),
					HTMLUtility::render(
						[
							'tag'               => 'div',
							'attributes'        => [
								'class' => 'fb-video',
								'data-href' => $fbVideourl,
								'data-width' => $width,
								'data-allowfullscreen' => $allowFullScreen === 'on' ? 'true' : 'false',
								'data-autoplay' => $autoPlay === 'on' ? 'true' : 'false',
								'data-showtext' => $showCaption === 'on' ? 'true' : 'false',
							],
							'childrenSanitizer' => 'et_core_esc_previously',
							'children'          => $fbXfbml,
						]
					)
				],
			]
		);
	}
}
