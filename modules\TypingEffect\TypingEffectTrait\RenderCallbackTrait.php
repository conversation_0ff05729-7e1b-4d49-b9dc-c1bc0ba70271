<?php

/**
 * TypingEffect::render_callback()
 *
 * @package MEE\Modules\TypingEffect
 * @since ??
 */

namespace MEE\Modules\TypingEffect\TypingEffectTrait;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

// phpcs:disable ET.Sniffs.ValidVariableName.UsedPropertyNotSnakeCase -- WP use snakeCase in \WP_Block_Parser_Block

use ET\Builder\Packages\Module\Module;
use ET\Builder\Framework\Utility\HTMLUtility;
use ET\Builder\FrontEnd\BlockParser\BlockParserStore;
use ET\Builder\Packages\Module\Options\Element\ElementComponents;
use MEE\Modules\TypingEffect\TypingEffect;

trait RenderCallbackTrait
{

	/**
	 * Static module render callback which outputs server side rendered HTML on the Front-End.
	 *
	 * @since ??
	 * @param array          $attrs    Block attributes that were saved by VB.
	 * @param string         $content  Block content.
	 * @param WP_Block       $block    Parsed block object that being rendered.
	 * @param ModuleElements $elements ModuleElements instance.
	 *
	 * @return string HTML rendered of Static module.
	 */
	public static function render_callback($attrs, $content, $block, $elements)
	{
		$beforeNewLine = $attrs['typingtextnewline']['innerContent']['desktop']['value'] ?? null;
		$beforeText = $attrs['beforetext']['innerContent']['desktop']['value'] ?? '';
		$afterText = $attrs['aftertext']['innerContent']['desktop']['value'] ?? null;
		$typingText = $attrs['typingtext']['innerContent']['desktop']['value'] ?? '';
		$afterNewLine = $attrs['aftertextnewline']['innerContent']['desktop']['value'] ?? null;
		$heading = $attrs['doptm_typing_effect']['decoration']['font']['font']['desktop']['value']['headingLevel'] ?? null;
		$removeCursor = $attrs['typing_effect']['advanced']['removeCursor']['desktop']['value'] ?? null;
		$HeadingTag = $heading ?? 'h1';
		$beforeTextStyles = [
			"display" => $beforeNewLine === "on" ? 'block' : 'inline-block',
			"padding-right" => $beforeNewLine === "on" ? '' : '10px'
		];
		$afterTextStyles = [
			"display" => $afterNewLine === "on" ? 'block' : 'inline-block',
			"padding-left" => $afterNewLine === "on" ? '' : '10px'
		];
		$typingTextStyles = [
			"display" => $afterNewLine && $beforeNewLine ? 'block' : 'inline-block',
		];

		// typing setting 
		$typeSpeed = $attrs['typing_effect']['decoration']['speed']['desktop']['value'] ?? null;
		$backSpeed = $attrs['typing_effect']['decoration']['typingbackspeed']['desktop']['value'] ?? null;
		$backDelay = $attrs['typing_effect']['decoration']['backdelay']['desktop']['value'] ?? null;
		$repeatTyping = $attrs['typing_effect']['advanced']['use']['desktop']['value'] ?? null;
		$cursorChar = $attrs['cursortext']['innerContent']['desktop']['value'] ?? null;

		// Content container.
		$before_text = HTMLUtility::render(
			[
				'tag'               => 'span',
				'attributes'        => [
					'class' => 'doptm-before-text',
					'style' => $beforeTextStyles
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $beforeText,
			]
		);

		$afterText = HTMLUtility::render(
			[
				'tag'               => 'span',
				'attributes'        => [
					'class' => 'doptm-after-text',
					'style' => $afterTextStyles
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $afterText,
			]
		);
				$typingText = HTMLUtility::render(
			[
				'tag'               => 'span',
				'attributes'        => [
					'class' => 'doptm-typing',
					"data-doptm-typing-strings" => $typingText,
					"data-doptm-typing-speed" => $typeSpeed,
					"data-doptm-typing-backspeed" => $backSpeed,
					"data-doptm-typing-backdelay" => $backDelay,
					"data-doptm-typing-cursor" => $removeCursor === "on" ? "" : $cursorChar ?? '|',
					"data-doptm-typing-loop" => $repeatTyping,
					"data-doptm-typing-fadeout" => "false",
					"data-doptm-typing-shuffle" => "false",
					"data-doptm-typing-delay" => "0ms",
					"data-doptm-typing-viewport" => "100%",
					"data-doptm-typing-repeat" => "on",
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => "",
			]
		);

		$typingTextwrapper = HTMLUtility::render(
			[
				'tag'               => 'span',
				'attributes'        => [
					'class' => 'doptm-typing-text',
					'style' => $typingTextStyles
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $typingText,
			]
		);


		// Heading.
		$heading = HTMLUtility::render(
			[
				'tag'               => $HeadingTag,
				'attributes'        => [
					'class' => 'doptm-typing-effect',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $before_text  . $typingTextwrapper  . $afterText,
			]
		);



		$parent       = BlockParserStore::get_parent($block->parsed_block['id'], $block->parsed_block['storeInstance']);
		$parent_attrs = $parent->attrs ?? [];

		return Module::render(
			[
				// FE only.
				'orderIndex'          => $block->parsed_block['orderIndex'],
				'storeInstance'       => $block->parsed_block['storeInstance'],

				// VB equivalent.
				'attrs'               => $attrs,
				'elements'            => $elements,
				'id'                  => $block->parsed_block['id'],
				'name'                => $block->block_type->name,
				'moduleCategory'      => $block->block_type->category,
				'classnamesFunction'  => [TypingEffect::class, 'module_classnames'],
				'stylesComponent'     => [TypingEffect::class, 'module_styles'],
				'scriptDataComponent' => [TypingEffect::class, 'module_script_data'],
				'parentAttrs'         => $parent_attrs,
				'parentId'            => $parent->id ?? '',
				'parentName'          => $parent->blockName ?? '',
				'children'            => [
					ElementComponents::component(
						[
							'attrs'         => $attrs['module']['decoration'] ?? [],
							'id'            => $block->parsed_block['id'],

							// FE only.
							'orderIndex'    => $block->parsed_block['orderIndex'],
							'storeInstance' => $block->parsed_block['storeInstance'],
						]
					),
					HTMLUtility::render(
						[
							'tag'               => 'div',
							'attributes'        => [
								'class' => 'doptm-typing-effect',
							],
							'childrenSanitizer' => 'et_core_esc_previously',
							'children'          =>  $heading,
						]
					),
				],
			]
		);
	}
}
