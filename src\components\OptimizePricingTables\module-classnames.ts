import { ModuleClassnamesParams, textOptionsClassnames } from '@divi/module';
import { OptimizePricingTablesAttrs } from './types';


/**
 * Module classnames function for OptimizePricingTables.
 *
 * @since ??
 *
 * @param {ModuleClassnamesParams<OptimizePricingTablesAttrs>} param0 Function parameters.
 */
export const moduleClassnames = ({
  classnamesInstance,
  attrs,
}: ModuleClassnamesParams<OptimizePricingTablesAttrs>): void => {
  // Text Options.
  classnamesInstance.add(textOptionsClassnames(attrs?.module?.advanced?.text ?? {}));
};
