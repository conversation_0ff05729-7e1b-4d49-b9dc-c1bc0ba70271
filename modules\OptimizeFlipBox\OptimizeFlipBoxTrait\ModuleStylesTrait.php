<?php

/**
 * OptimizeFlipBox::module_styles().
 *
 * @package MEE\Modules\OptimizeFlipBox
 * @since ??
 */

namespace MEE\Modules\OptimizeFlipBox\OptimizeFlipBoxTrait;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

use ET\Builder\FrontEnd\Module\Style;
use ET\Builder\Packages\Module\Options\Text\TextStyle;
use ET\Builder\Packages\Module\Options\Css\CssStyle;
use MEE\Modules\OptimizeFlipBox\OptimizeFlipBox;

trait ModuleStylesTrait
{

	use CustomCssTrait;
	use StyleDeclarationTrait;

	/**
	 * OptimizeFlipBox's style components.
	 *
	 * This function is equivalent of JS function ModuleStyles located in
	 * src/components/OptimizeFlipBox/styles.tsx.
	 *
	 * @since ??
	 *
	 * @param array $args {
	 *     An array of arguments.
	 *
	 *      @type string $id                Module ID. In VB, the ID of module is UUIDV4. In FE, the ID is order index.
	 *      @type string $name              Module name.
	 *      @type string $attrs             Module attributes.
	 *      @type string $parentAttrs       Parent attrs.
	 *      @type string $orderClass        Selector class name.
	 *      @type string $parentOrderClass  Parent selector class name.
	 *      @type string $wrapperOrderClass Wrapper selector class name.
	 *      @type string $settings          Custom settings.
	 *      @type string $state             Attributes state.
	 *      @type string $mode              Style mode.
	 *      @type ModuleElements $elements  ModuleElements instance.
	 * }
	 */
	public static function module_styles($args)
	{
		$attrs    = $args['attrs'] ?? [];
		$elements = $args['elements'];
		$settings = $args['settings'] ?? [];
		$orderClass = $args['orderClass'] ?? '';

		$btn_hover_selector = "{$orderClass} .dotm_optimize_flip_box_btn:hover";
		$btn_txt_hover_color = $attrs['btn']['decoration']['hoverColor']['desktop']['value'] ?? '';
		$btn_hover_bg_color = $attrs['btn']['decoration']['hoverBgColor']['desktop']['value'] ?? '';

		// Front Attributes
		$front_img_selector = "{$orderClass} .dotm_optimize_flip_box_front_img";
		$front_img_container_selector = "{$orderClass} .dotm_optimize_flip_box_front_img_container";
		$front_icon_container_selector = "{$orderClass} .dotm_optimize_flip_box_front_icon_container";

		// Back Attributes
		$back_img_selector = "{$orderClass} .dotm_optimize_flip_box_back_img";
		$back_img_container_selector = "{$orderClass} .dotm_optimize_flip_box_back_img_container";
		$back_icon_container_selector = "{$orderClass} .dotm_optimize_flip_box_back_icon_container";
		$btn_container_selector = "{$orderClass} .dotm_optimize_flip_box_btn_container";

		Style::add(
			[
				'id'            => $args['id'],
				'name'          => $args['name'],
				'orderIndex'    => $args['orderIndex'],
				'storeInstance' => $args['storeInstance'],
				'styles'        => [
					// Module.
					$elements->style(
						[
							'attrName'   => 'module',
							'styleProps' => [
								'disabledOn'     => [
									'disabledModuleVisibility' => $settings['disabledModuleVisibility'] ?? null,
								],
								'advancedStyles' => [
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $front_img_selector,
											'attr'     => $attrs['front_image']['decoration']['img_width'] ?? [],
											'property' => 'width',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $back_img_selector,
											'attr'     => $attrs['back_image']['decoration']['img_width'] ?? [],
											'property' => 'width',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $front_img_container_selector,
											'attr'     => $attrs['front_image']['decoration']['img_alignment'] ?? [],
											'property' => 'text-align',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $back_img_container_selector,
											'attr'     => $attrs['back_image']['decoration']['img_alignment'] ?? [],
											'property' => 'text-align',
										]
									],
									[
										'componentName' => 'divi/text',
										'props'         => [
											'selector' => $btn_container_selector,
											'attr'     => $attrs['btn']['decoration']['text'] ?? [],
										]
										],
										[
											'componentName' => 'divi/common',
											'props'         => [
												'selector' => $btn_hover_selector,
												'attr'     => $attrs['btn']['decoration']['hoverColor'] ?? [],
												'property' => "color: {$btn_txt_hover_color} !important; background: {$btn_hover_bg_color} !important;",
											]
										]
								]
							],
						]
					),

					// Front Icon Style
					$elements->style(
						[
							'attrName'   => 'front_icon',
							'styleProps' => [
								'advancedStyles' => [
									[
										'componentName' => 'divi/common',
										'props'         => [
											'attr'                => $attrs['front_icon']['innerContent'] ?? [],
											'declarationFunction' => [OptimizeFlipBox::class, 'icon_font_declaration'],
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'attr'     => $attrs['front_icon']['advanced']['color'] ?? [],
											'property' => 'color',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'attr'     => $attrs['front_icon']['advanced']['size'] ?? [],
											'property' => 'font-size',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $front_icon_container_selector,
											'attr'     => $attrs['front_icon']['decoration']['icon_alignment'] ?? [],
											'property' => 'text-align',
										]
									],
								]
							]
						]
					),

					// Back Icon Style
					$elements->style(
						[
							'attrName'   => 'back_icon',
							'styleProps' => [
								'advancedStyles' => [
									[
										'componentName' => 'divi/common',
										'props'         => [
											'attr'                => $attrs['back_icon']['innerContent'] ?? [],
											'declarationFunction' => [OptimizeFlipBox::class, 'icon_font_declaration'],
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'attr'     => $attrs['back_icon']['advanced']['color'] ?? [],
											'property' => 'color',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'attr'     => $attrs['back_icon']['advanced']['size'] ?? [],
											'property' => 'font-size',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $front_icon_container_selector,
											'attr'     => $attrs['back_icon']['decoration']['icon_alignment'] ?? [],
											'property' => 'text-align',
										]
									],
								]
							]
						]
					),

					// inner_wrapper.
					$elements->style(
						[
							'attrName' => 'inner_wrapper',
						]
					),

					// front_container.
					$elements->style(
						[
							'attrName' => 'front_container',
						]
					),

					// front_overlay
					$elements->style(
						[
							'attrName' => 'front_overlay',
						]
					),

					// front_image.
					$elements->style(
						[
							'attrName' => 'front_image',
						]
					),

					// front_title.
					$elements->style(
						[
							'attrName' => 'front_title',
						]
					),

					// front_content.
					$elements->style(
						[
							'attrName' => 'front_content',
						]
					),

					// back styles

					// back_container.
					$elements->style(
						[
							'attrName' => 'back_container',
						]
					),

					// back_overlay.
					$elements->style(
						[
							'attrName' => 'back_overlay',
						]
					),

					// back_image.
					$elements->style(
						[
							'attrName' => 'back_image',
						]
					),

					// back_title.
					$elements->style(
						[
							'attrName' => 'back_title',
						]
					),

					// back_content.
					$elements->style(
						[
							'attrName' => 'back_content',
						]
					),

					// btn.
					$elements->style(
						[
							'attrName' => 'btn',
						]
					),


					/*
					 * We need to add CssStyle at the very bottom of other
					 * components so that custom css can override module styles
					 * till we find a more elegant solution.
					 */
					CssStyle::style(
						[
							'selector'  => $args['orderClass'],
							'attr'      => $attrs['css'] ?? [],
							'cssFields' => OptimizeFlipBox::custom_css(),
						]
					),

					// ATTENTION: The code is intentionally added and commented in FE only as an example of expected value format.
					// If you have custom style processing, the style output should be passed as an `array` of style declarations
					// to the `styles` property of the `Style::add` method. For example:
					// [
					// 	[
					// 		'atRules'     => false,
					// 		'selector'    => "{$args['orderClass']} .example_static_module__content-container",
					// 		'declaration' => 'color: red;'
					// 	],
					// 	[
					// 		'atRules'     => '@media only screen and (max-width: 767px)',
					// 		'selector'    => "{$args['orderClass']} .example_static_module__content-container",
					// 		'declaration' => 'color: green;'
					// 	],
					// ],

					// The code below is an example of how to use the `CssStyle::style` method to generate CSS style.
					CssStyle::style(
						[
							'selector'  => $args['orderClass'],
							'attr'      => $attrs['css'] ?? [],
							'cssFields' => OptimizeFlipBox::custom_css(),
						]
					),
				],
			]
		);
	}
}
