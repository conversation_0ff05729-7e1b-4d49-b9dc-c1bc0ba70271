<?php

/**
 * Module: Static Module class.
 *
 * @package MEE\Modules\FacebookPost
 * @since ??
 */

namespace MEE\Modules\FacebookPost;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

use ET\Builder\Framework\DependencyManagement\Interfaces\DependencyInterface;
use ET\Builder\Packages\ModuleLibrary\ModuleRegistration;


/**
 * `FacebookPost` is consisted of functions used for Static Module such as Front-End rendering, REST API Endpoints etc.
 *
 * This is a dependency class and can be used as a dependency for `DependencyTree`.
 *
 * @since ??
 */
class FacebookPost implements DependencyInterface
{
	use FacebookPostTrait\RenderCallbackTrait;
	use FacebookPostTrait\ModuleClassnamesTrait;
	use FacebookPostTrait\ModuleStylesTrait;
	use FacebookPostTrait\ModuleScriptDataTrait;

	/**
	 * Loads `FacebookPost` and registers Front-End render callback and REST API Endpoints.
	 *
	 * @since ??
	 *
	 * @return void
	 */
	public function load()
	{
		$module_json_folder_path = DIVIOPTIMIZER_MODULES_JSON_PATH . 'FacebookPost/';

		add_action(
			'init',
			function () use ($module_json_folder_path) {
				ModuleRegistration::register_module(
					$module_json_folder_path,
					[
						'render_callback' => [FacebookPost::class, 'render_callback'],
					]
				);
			}
		);
	}
}
