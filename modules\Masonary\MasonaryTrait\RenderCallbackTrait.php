<?php

/**
 * ChildModule::render_callback()
 *
 * @package MEE\Modules\ChildModule
 * @since ??
 */

namespace MEE\Modules\Masonary\MasonaryTrait;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

// phpcs:disable ET.Sniffs.ValidVariableName.UsedPropertyNotSnakeCase -- WP use snakeCase in \WP_Block_Parser_Block

use ET\Builder\FrontEnd\BlockParser\BlockParserStore;
use ET\Builder\Packages\Module\Module;
use ET\Builder\Framework\Utility\HTMLUtility;
use ET\Builder\Packages\IconLibrary\IconFont\Utils;
use ET\Builder\Packages\Module\Options\Element\ElementComponents;
use MEE\Modules\Masonary\Masonary;

trait RenderCallbackTrait
{
	use ModuleClassnamesTrait;
	use ModuleStylesTrait;
	use ModuleScriptDataTrait;
	/**
	 * Parent module render callback which outputs server side rendered HTML on the Front-End.
	 *
	 * @since ??
	 *
	 * @param array          $attrs Block attributes that were saved by VB.
	 * @param string         $content          Block content.
	 * @param \WP_Block      $block            Parsed block object that being rendered.
	 * @param ModuleElements $elements         ModuleElements instance.
	 *
	 * @return string HTML rendered of Parent module.
	 */


	/**
	 * Fetch image data from WordPress by image IDs.
	 *
	 * @param string $carousel_image_ids Comma-separated image IDs.
	 * @return array List of images with their URLs and details.
	 */
	public static function fetch_images($carousel_image_ids)
	{
		$image_data = [];

		if (!empty($carousel_image_ids)) {
			$image_ids = explode(',', $carousel_image_ids);

			foreach ($image_ids as $id) {
				$id = intval(trim($id)); // Ensure ID is an integer
				if ($id > 0) {
					$image = get_post($id);
					if ($image) {
						$image_url = wp_get_attachment_url($id);
						if ($image_url) {
							$image_data[] = [
								'id' => $id,
								'source_url' => esc_url($image_url),
								'title' => esc_html(get_the_title($id)),
								'alt' => esc_attr(get_post_meta($id, '_wp_attachment_image_alt', true)),
							];
						}
					}
				}
			}
		}

		return $image_data;
	}
	public static function render_callback($attrs, $content, $block, $elements)
	{
		$children_ids = $block->parsed_block['innerBlocks'] ? array_map(
			function ($inner_block) {
				return $inner_block['id'];
			},
			$block->parsed_block['innerBlocks']
		) : [];

		$parent       = BlockParserStore::get_parent($block->parsed_block['id'], $block->parsed_block['storeInstance']);
		$parent_attrs = $parent->attrs ?? [];

		$showTitle = $attrs['showTitle']['innerContent']['desktop']['value'] ?? '';
		$showCaption = $attrs['showCaption']['innerContent']['desktop']['value'] ?? '';
		$showIcon = $attrs['showIcon']['innerContent']['desktop']['value'] ?? '';
		$icon = $attrs['icon']['innerContent']['desktop']['value'] ?? '';
		$image_ids = $attrs['imagegallery']['innerContent']['desktop']['value'] ?? '';
		$overlay_class = $attrs['overlay_hover_effect']['advanced']['overlayClass']['desktop']['value'] ?? '';

		// Fetch images using the newly created function
		$images = self::fetch_images($image_ids);
		$image_html = '';
		foreach ($images as $image) {
			
			// title 
			$title = HTMLUtility::render(
				[
					'tag'               => 'h2',
					'attributes'        => [
						'class' => 'dotm_masonary_title',
					],
					'childrenSanitizer' => 'et_core_esc_previously',
					'children'          => $image['title'], // Direct access to title from fetch_images
				]
			);

			$caption = HTMLUtility::render(
				[
					'tag'               => 'p',
					'attributes'        => [
						'class' => 'dotm_masonary_contents',
					],
					'childrenSanitizer' => 'et_core_esc_previously',
					'children'          => get_post($image['id'])->post_excerpt, // Get caption from post
				]
			);

			$icon_html = HTMLUtility::render(
				[
					'tag'               => 'div',
					'attributes'        => [
						'class' => "dotm_masonary_item_icon et-pb-icon",
					],
					'childrenSanitizer' => 'et_core_esc_previously',
					'children'          => Utils::process_font_icon($icon),
				]
			);

			$content = HTMLUtility::render(
				[
					'tag'               => 'div',
					'attributes'        => [
						'class' => "content",
					],
					'childrenSanitizer' => 'et_core_esc_previously',
					'children'          =>  ($showIcon === 'on' ? $icon_html:'') . ($showTitle ==="on" ? $title : '') . ($showCaption==="on" ? $caption : ''),
				]
			);

			$overlay_content = HTMLUtility::render(
				[
					'tag'               => 'div',
					'attributes'        => [
						'class' => "overlay " . $overlay_class,
					],
					'childrenSanitizer' => 'et_core_esc_previously',
					'children'          => $content,
				]
			);

			$image_= HTMLUtility::render(
				[
					'tag'               => 'img',
					'attributes'        => [
						'src' => $image['source_url'], // Corrected variable
						'alt' => $image['alt'], // Corrected key
					],
					'childrenSanitizer' => 'et_core_esc_previously',
				]
			);

			$anchor = HTMLUtility::render(
				[
					'tag'               => 'a',
					'attributes'        => [
						'href'  => $image['source_url'], // You can modify this to add an actual link
						'title' => $image['alt'],
					],
					'childrenSanitizer' => 'et_core_esc_previously',
					'children'          => $image_ . $overlay_content,
				]
			);
			$masonary_item = HTMLUtility::render(
				[
					'tag'               => 'div',
					'attributes'        => [
						'class' => "dotm_masonary_item",
					],
					'childrenSanitizer' => 'et_core_esc_previously',
					'children'          => $anchor,
				]
			);

			$image_html .= $masonary_item;
		}

		$masonary_container = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => "dotm_masonary_container gallery",
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $image_html,
			]
		);

		return Module::render(
			[
				// FE only.
				'orderIndex'          => $block->parsed_block['orderIndex'],
				'storeInstance'       => $block->parsed_block['storeInstance'],

				// VB equivalent.
				'id'                  => $block->parsed_block['id'],
				'name'                => $block->block_type->name,
				'moduleCategory'      => $block->block_type->category,
				'attrs'               => $attrs,
				'elements'            => $elements,
				'classnamesFunction'  => [Masonary::class, 'module_classnames'],
				'scriptDataComponent' => [Masonary::class, 'module_script_data'],
				'stylesComponent'     => [Masonary::class, 'module_styles'],
				'parentAttrs'         => $parent_attrs,
				'parentId'            => $parent->id ?? '',
				'parentName'          => $parent->blockName ?? '',
				'children'            => [
					ElementComponents::component(
						[
							'attrs'         => $attrs['module']['decoration'] ?? [],
							'id'            => $block->parsed_block['id'],
							'orderIndex'    => $block->parsed_block['orderIndex'],
							'storeInstance' => $block->parsed_block['storeInstance'],
						]
					) . $masonary_container,
					'childrenIds' => $children_ids,
				],

				
			]
		);
	}
}
