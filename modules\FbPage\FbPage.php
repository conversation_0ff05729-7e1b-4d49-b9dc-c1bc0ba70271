<?php

/**
 * Module: Static Module class.
 *
 * @package MEE\Modules\FbPage
 * @since ??
 */

namespace MEE\Modules\FbPage;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

use ET\Builder\Framework\DependencyManagement\Interfaces\DependencyInterface;
use ET\Builder\Packages\ModuleLibrary\ModuleRegistration;


/**
 * `FbPage` is consisted of functions used for Static Module such as Front-End rendering, REST API Endpoints etc.
 *
 * This is a dependency class and can be used as a dependency for `DependencyTree`.
 *
 * @since ??
 */
class FbPage implements DependencyInterface
{
	use FbPageTrait\RenderCallbackTrait;
	use FbPageTrait\ModuleClassnamesTrait;
	use FbPageTrait\ModuleStylesTrait;
	use FbPageTrait\ModuleScriptDataTrait;

	/**
	 * Loads `FbPage` and registers Front-End render callback and REST API Endpoints.
	 *
	 * @since ??
	 *
	 * @return void
	 */
	public function load()
	{
		$module_json_folder_path = DIVIOPTIMIZER_MODULES_JSON_PATH . 'FbPage/';

		add_action(
			'init',
			function () use ($module_json_folder_path) {
				ModuleRegistration::register_module(
					$module_json_folder_path,
					[
						'render_callback' => [FbPage::class, 'render_callback'],
					]
				);
			}
		);
	}
}
