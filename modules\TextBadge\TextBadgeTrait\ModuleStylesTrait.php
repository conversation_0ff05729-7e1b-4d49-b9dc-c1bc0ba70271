<?php

/**
 * TextBadge::module_styles().
 *
 * @package MEE\Modules\TextBadge
 * @since ??
 */

namespace MEE\Modules\TextBadge\TextBadgeTrait;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

use ET\Builder\FrontEnd\Module\Style;
use ET\Builder\Packages\Module\Options\Text\TextStyle;
use ET\Builder\Packages\Module\Options\Css\CssStyle;
use MEE\Modules\TextBadge\TextBadge;

trait ModuleStylesTrait
{

	use CustomCssTrait;

	/**
	 * TextBadge's style components.
	 *
	 * This function is equivalent of JS function ModuleStyles located in
	 * src/components/TextBadge/styles.tsx.
	 *
	 * @since ??
	 *
	 * @param array $args {
	 *     An array of arguments.
	 *
	 *      @type string $id                Module ID. In VB, the ID of module is UUIDV4. In FE, the ID is order index.
	 *      @type string $name              Module name.
	 *      @type string $attrs             Module attributes.
	 *      @type string $parentAttrs       Parent attrs.
	 *      @type string $orderClass        Selector class name.
	 *      @type string $parentOrderClass  Parent selector class name.
	 *      @type string $wrapperOrderClass Wrapper selector class name.
	 *      @type string $settings          Custom settings.
	 *      @type string $state             Attributes state.
	 *      @type string $mode              Style mode.
	 *      @type ModuleElements $elements  ModuleElements instance.
	 * }
	 */
	public static function module_styles($args)
	{
		$attrs    = $args['attrs'] ?? [];
		$elements = $args['elements'];
		$settings = $args['settings'] ?? [];
		$order_class = $args['orderClass'];

		$badge_selector = $order_class . ' .dotm_text_badge_container';
		$use_text = $attrs['mainText']['advanced']['use']['desktop']['value'] ?? 'on';
		$use_image = $attrs['image']['advanced']['use']['desktop']['value'] ?? '';

		Style::add(
			[
				'id'            => $args['id'],
				'name'          => $args['name'],
				'orderIndex'    => $args['orderIndex'],
				'storeInstance' => $args['storeInstance'],
				'styles'        => [
					// Module.
					$elements->style(
						[
							'attrName'   => 'module',
							'styleProps' => [
								'disabledOn'     => [
									'disabledModuleVisibility' => $settings['disabledModuleVisibility'] ?? null,
								],
								'advancedStyles' => [
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $badge_selector,
											'attr'     => $attrs['badgeText']['advanced']['alignment'] ?? [],
											'property' => 'justify-content'
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $badge_selector,
											'attr'     => $attrs['badgeText']['advanced']['eatch_gap'] ?? [],
											'property' => $use_text === 'on' ? 'gap' : ''
										]
									]
								]
							],
						]
					),

					// Image.
					$elements->style(
						[
							'attrName' => 'image',
						]
					),

					// mainText.
					$elements->style(
						[
							'attrName' => 'mainText',
						]
					),

					// badgeText.
					$elements->style(
						[
							'attrName' => 'badgeText',
							'styleProps' => [
								'disabledOn'     => [
									'disabledModuleVisibility' => $settings['disabledModuleVisibility'] ?? null,
								],
								'advancedStyles' => [
									[
										'componentName' => 'divi/common',
										'props'         => [
											'attr'     => $attrs['image']['advanced']['use'] ?? [],
											'property' => $use_image === 'on' && $use_text === 'off' ? 'position: absolute;' : '',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'attr'     => $attrs['badgeText']['advanced']['vr_position'] ?? [],
											'property' => $use_image === 'on' && $use_text === 'off' ? 'left' : '',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'attr'     => $attrs['badgeText']['advanced']['hr_position'] ?? [],
											'property' => $use_image === 'on' && $use_text === 'off' ? 'top' : '',
										]
									]
								]
							],
						]
					),

					/*
					 * We need to add CssStyle at the very bottom of other
					 * components so that custom css can override module styles
					 * till we find a more elegant solution.
					 */
					CssStyle::style(
						[
							'selector'  => $args['orderClass'],
							'attr'      => $attrs['css'] ?? [],
							'cssFields' => TextBadge::custom_css(),
						]
					),

					// ATTENTION: The code is intentionally added and commented in FE only as an example of expected value format.
					// If you have custom style processing, the style output should be passed as an `array` of style declarations
					// to the `styles` property of the `Style::add` method. For example:
					// [
					// 	[
					// 		'atRules'     => false,
					// 		'selector'    => "{$args['orderClass']} .example_static_module__content-container",
					// 		'declaration' => 'color: red;'
					// 	],
					// 	[
					// 		'atRules'     => '@media only screen and (max-width: 767px)',
					// 		'selector'    => "{$args['orderClass']} .example_static_module__content-container",
					// 		'declaration' => 'color: green;'
					// 	],
					// ],

					// The code below is an example of how to use the `CssStyle::style` method to generate CSS style.
					CssStyle::style(
						[
							'selector'  => $args['orderClass'],
							'attr'      => $attrs['css'] ?? [],
							'cssFields' => TextBadge::custom_css(),
						]
					),
				],
			]
		);
	}
}
