// External dependencies.
import React, { ReactElement } from 'react';

// WordPress dependencies.
import { __ } from '@wordpress/i18n';

// Divi dependencies.
import {
  AnimationGroup,
  BorderGroup,
  BoxShadowGroup,
  FieldContainer,
  FiltersGroup,
  FontGroup,
  FontBodyGroup,
  SizingGroup,
  SpacingGroup,
  TextGroup,
  TransformGroup,
  BackgroundGroup,
} from '@divi/module';
import { GroupContainer } from '@divi/modal';
import { ColorPickerContainer, RangeContainer, Select, SelectContainer } from '@divi/field-library';
import {
  type Module,
} from '@divi/types';
import { BreadcrumbsAttrs } from "./types";


export const SettingsDesign = ({
  defaultSettingsAttrs,
}: Module.Settings.Panel.Props<BreadcrumbsAttrs>): ReactElement => (
  <React.Fragment>
    <GroupContainer id="Home button settings" title={__('Home button Settings', 'divi-optimaizer-modules')}>
      <GroupContainer id="icon" title={__('Home Icon Style', 'divi-optimaizer-modules')}>
        <FieldContainer
          attrName="home_icon.advanced.color"
          label={__('Home Icon Color', 'divi-optimaizer-modules')}
          description={__('Input your value to action title here.', 'divi-optimaizer-modules')}
          features={{
            sticky: false,
          }}
          defaultAttr={defaultSettingsAttrs?.icon}
        >
          <ColorPickerContainer />
        </FieldContainer>
        <FieldContainer
          attrName="home_icon.advanced.size"
          label={__('Home Icon Size', 'divi-optimaizer-modules')}
          description={__('Input your value to action title here.', 'divi-optimaizer-modules')}
          features={{
            sticky: false,
          }}
          defaultAttr={defaultSettingsAttrs}
        >
          <RangeContainer />
        </FieldContainer>
        <FieldContainer
          attrName="home_btn.decoration.icon_placement"
          label={__('Home Icon Placement', 'divi-optimaizer-modules')}
          description={__('Input your value to action title here.', 'divi-optimaizer-modules')}
          features={{
            sticky: false,
          }}
          defaultAttr={defaultSettingsAttrs?.home_btn?.decoration?.icon_placement}
        >
          <SelectContainer
            options={{
              'row': { label: 'Left' },
              'row-reverse': { label: 'Right' },
            }}
          />

        </FieldContainer>
        <FieldContainer
          attrName="home_btn.decoration.icon_gap"
          label={__('Home Icon Gap', 'divi-optimaizer-modules')}
          description={__('Input your value to action title here.', 'divi-optimaizer-modules')}
          features={{
            sticky: false,
          }}
          defaultAttr={defaultSettingsAttrs?.home_btn?.decoration?.icon_gap}
        >
          <RangeContainer />

        </FieldContainer>
      </GroupContainer>

      <FontGroup
        attrName="home_btn.decoration.font"
        fieldLabel='Home'
        grouped={false}
      />
      <BackgroundGroup
        attrName="home_btn.decoration.background"
        groupLabel={__('Home Button Background', 'divi-optimaizer-modules')}
        grouped={false}
      />
      <BorderGroup
        attrName="home_btn.decoration.border"
        fieldLabel='Home'
        grouped={false}
      />
      <SpacingGroup
        attrName="home_btn.decoration.spacing"
        fieldLabel='Home'
        grouped={false}
      />
      <BoxShadowGroup
        attrName="home_btn.decoration.boxShadow"
        fieldLabel='Home'
        grouped={false}
      />
    </GroupContainer>
    <GroupContainer id="icon" title={__('Separetor Icon Style', 'divi-optimaizer-modules')}>
      <FieldContainer
        attrName="icon.advanced.color"
        label={__('Icon Color', 'divi-optimaizer-modules')}
        description={__('Input your value to action title here.', 'divi-optimaizer-modules')}
        features={{
          sticky: false,
        }}
        defaultAttr={defaultSettingsAttrs?.icon}
      >
        <ColorPickerContainer />
      </FieldContainer>
      <FieldContainer
        attrName="icon.advanced.size"
        label={__('Icon Size', 'divi-optimaizer-modules')}
        description={__('Input your value to action title here.', 'divi-optimaizer-modules')}
        features={{
          sticky: false,
        }}
        defaultAttr={defaultSettingsAttrs}
      >
        <RangeContainer />
      </FieldContainer>
      <SpacingGroup attrName="icon.decoration.spacing"  grouped={false} />
    </GroupContainer>
    <TextGroup
      defaultGroupAttr={defaultSettingsAttrs?.module?.advanced?.text}
      fields={{
        color: {
          render: false,
        },
      }}
    />


    <GroupContainer id="Separator Settings" title={__('Separator Settings', 'divi-optimaizer-modules')}>
      <FieldContainer
        attrName="icon.advanced.color"
        label={__('Home Icon Color', 'divi-optimaizer-modules')}
        description={__('Input your value to action title here.', 'divi-optimaizer-modules')}
        features={{
          sticky: false,
        }}
        defaultAttr={defaultSettingsAttrs?.icon}
      >
        <ColorPickerContainer />
      </FieldContainer>
      <FieldContainer
        attrName="icon.advanced.size"
        label={__('Home Icon Size', 'divi-optimaizer-modules')}
        description={__('Input your value to action title here.', 'divi-optimaizer-modules')}
        features={{
          sticky: false,
        }}
        defaultAttr={defaultSettingsAttrs}
      >
        <RangeContainer />
      </FieldContainer>
      <SpacingGroup
        attrName="icon.decoration.spacing"
        fieldLabel='Separator'
        grouped={false}
      />
    </GroupContainer>
    <SizingGroup />
    <SpacingGroup />
    <BorderGroup />
    <BoxShadowGroup />
    <FiltersGroup />
    <TransformGroup />
    <AnimationGroup />
  </React.Fragment>
);
