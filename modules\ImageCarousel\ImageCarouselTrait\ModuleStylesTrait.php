<?php

/**
 * ImageCarousel::module_styles().
 *
 * @package MEE\Modules\ImageCarousel
 * @since ??
 */

namespace MEE\Modules\ImageCarousel\ImageCarouselTrait;


if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

use ET\Builder\FrontEnd\Module\Style;
use ET\Builder\Packages\Module\Layout\Components\StyleCommon\CommonStyle;
use ET\Builder\Packages\Module\Options\Css\CssStyle;
use MEE\Modules\ImageCarouselItem\ImageCarouselItem;


trait ModuleStylesTrait
{

	use CustomCssTrait;

	/**
	 * Child Module's style components.
	 *
	 * This function is equivalent of JS function ModuleStyles located in
	 * src/components/ImageCarousel/styles.tsx.
	 *
	 * @param array $args {
	 *     An array of arguments.
	 *
	 * @type string $id Module ID. In VB, the ID of module is UUIDV4. In FE, the ID is order index.
	 * @type string $name Module name.
	 * @type string $attrs Module attributes.
	 * @type string $parentAttrs Parent attrs.
	 * @type string $orderClass Selector class name.
	 * @type string $parentOrderClass Parent selector class name.
	 * @type string $wrapperOrderClass Wrapper selector class name.
	 * @type string $settings Custom settings.
	 * @type string $state Attributes state.
	 * @type string $mode Style mode.
	 * @type ModuleElements $elements ModuleElements instance.
	 * }
	 * @since ??
	 */
	public static function module_styles($args)
	{
		$attrs        = $args['attrs'] ?? [];
		$parent_attrs = $args['parentAttrs'] ?? [];
		$order_class  = $args['orderClass'];
		$elements     = $args['elements'];
		$settings     = $args['settings'] ?? [];

		$prevSelector = "{$order_class} .dotm_image_carousel_prev";
		$nextSelector = "{$order_class} .dotm_image_carousel_next";
		$activeIndicatorSelector = "{$order_class} .dotm_image_carousel_indicator.active";
		$idicatorSelector = "{$order_class} .dotm_image_carousel_indicators";
		$btnContainerSelector = "{$order_class} .dotm_image_carousel_button_container";
		$arrowButtonsSelector = "{$order_class} .dotm_image_carousel_nav_button";
		$buttonALignValue = $attrs['button']['decoration']['button']['desktop']['value']['alignment'] ?? '';
		$show_arrows_onHover = $attrs['carousel']['advanced']['show_arrows_onHover']['desktop']['value'] ?? "";

		Style::add(
			[
				'id'            => $args['id'],
				'name'          => $args['name'],
				'orderIndex'    => $args['orderIndex'],
				'storeInstance' => $args['storeInstance'],
				'styles'        => [
					// Module.
					$elements->style(
						[
							'attrName'   => 'module',
							'styleProps' => [
								'disabledOn' => [
									'disabledModuleVisibility' => $settings['disabledModuleVisibility'] ?? null,
								],
							],
						]
					),

					// contents.
					$elements->style(
						[
							'attrName' => 'contents',
						]
					),

					// image_overlay.
					$elements->style(
						[
							'attrName' => 'image_overlay',
						]
					),

					// Title.
					$elements->style(
						[
							'attrName' => 'title',
						]
					),

					// Content.
					$elements->style(
						[
							'attrName' => 'content',
						]
					),

					// button.
					$elements->style(
						[
							'attrName' => 'button',
							'styleProps' => [
								'advancedStyles' => [
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $btnContainerSelector,
											'attr'     => $attrs['button']['advanced']['btnALignment'] ?? [],
											'property' => 'text-align: ' . $buttonALignValue . ';',
										]
									]
								]
							]
						]
					),

					// indicators.
					$elements->style(
						[
							'attrName' => 'indicators',
							'styleProps' => [
								'advancedStyles' => [
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $activeIndicatorSelector,
											'attr'     => $attrs['indicators']['decoration']['active_indicator_color'] ?? [],
											'property' => 'background',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $idicatorSelector,
											'attr'     => $attrs['indicators']['decoration']['hr_position'] ?? [],
											'property' => 'bottom',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $idicatorSelector,
											'attr'     => $attrs['indicators']['decoration']['vr_position'] ?? [],
											'property' => 'left',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $idicatorSelector,
											'attr'     => $attrs['indicators']['decoration']['indicator_gap'] ?? [],
											'property' => 'gap',
										]
									]
								]
							]
						]
					),

					// navigation_btns.
					$elements->style(
						[
							'attrName'   => 'navigation_btns',
							'styleProps' => [
								'advancedStyles' => [
									[
										'componentName' => 'divi/common',
										'props'         => [
											'attr'     => $attrs['navigation_btns']['advanced']['color'] ?? [],
											'property' => 'color',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'attr'     => $attrs['navigation_btns']['advanced']['size'] ?? [],
											'property' => 'font-size',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'attr'     => $attrs['navigation_btns']['advanced']['hr_position'] ?? [],
											'property' => 'top',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $prevSelector,
											'attr'     => $attrs['navigation_btns']['advanced']['vr_position'] ?? [],
											'property' => 'left',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $nextSelector,
											'attr'     => $attrs['navigation_btns']['advanced']['vr_position'] ?? [],
											'property' => 'right',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'attr'     => $attrs['carousel']['advanced']['show_arrows_onHover'] ?? [],
											'property' => $show_arrows_onHover === 'on' ? 'opacity: 0; transition: all 0.5s ease-in-out;' : '',
										]
									],
								]
							]
						]
					),

					/*
					 * We need to add CssStyle at the very bottom of other
					 * components so that custom css can override module styles
					 * till we find a more elegant solution.
					 */
					CssStyle::style(
						[
							'selector'  => $args['orderClass'],
							'attr'      => $attrs['css'] ?? [],
							'cssFields' => self::custom_css(),
						]
					),

					// ATTENTION: The code is intentionally added and commented in FE only as an example of expected value format.
					// If you have custom style processing, the style output should be passed as an `array` of style declarations
					// to the `styles` property of the `Style::add` method. For example:
					// [
					// 	[
					// 		'atRules'     => false,
					// 		'selector'    => $icon_selector,
					// 		'declaration' => 'color: red;'
					// 	],
					// 	[
					// 		'atRules'     => '@media only screen and (max-width: 767px)',
					// 		'selector'    => $icon_selector,
					// 		'declaration' => 'color: green;'
					// 	],
					// ],

					// The code below is an example of how to use the `CssStyle::style` method to generate CSS style.
					CssStyle::style(
						[
							'selector'  => $args['orderClass'],
							'attr'      => $attrs['css'] ?? [],
							'cssFields' => self::custom_css(),
						]
					),
				],
			]
		);
	}
}
