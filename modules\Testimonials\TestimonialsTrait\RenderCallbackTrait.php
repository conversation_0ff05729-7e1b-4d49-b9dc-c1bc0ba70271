<?php
/**
 * Testimonials::render_callback()
 *
 * @package MEE\Modules\Testimonials
 * @since ??
 */

namespace MEE\Modules\Testimonials\TestimonialsTrait;

if ( ! defined( 'ABSPATH' ) ) {
	die( 'Direct access forbidden.' );
}

// phpcs:disable ET.Sniffs.ValidVariableName.UsedPropertyNotSnakeCase -- WP use snakeCase in \WP_Block_Parser_Block

use ET\Builder\Packages\Module\Module;
use ET\Builder\Framework\Utility\HTMLUtility;
use ET\Builder\FrontEnd\BlockParser\BlockParserStore;
use ET\Builder\Packages\Module\Options\Element\ElementComponents;
use MEE\Modules\Testimonials\Testimonials;
use ET\Builder\Packages\IconLibrary\IconFont\Utils;

trait RenderCallbackTrait {

	/**
	 * Testimonials render callback which outputs server side rendered HTML on the Front-End.
	 *
	 * @since ??
	 * @param array          $attrs    Block attributes that were saved by VB.
	 * @param string         $content  Block content.
	 * @param WP_Block       $block    Parsed block object that being rendered.
	 * @param ModuleElements $elements ModuleElements instance.
	 *
	 * @return string HTML rendered of Testimonials.
	 */
	public static function render_callback( $attrs, $content, $block, $elements ) {
		$uuid = substr(str_shuffle(str_repeat($x='0123456789abcdefghijklmnopqrstuvwxyz', ceil(10/strlen($x)) )), 1, 10) . (new \DateTime())->format('U');
		// Image.
		$useImage = $attrs['image']['advanced']['useImage']['desktop']['value']?? 'off';
		$icon = $attrs['icon']['innerContent']['desktop']['value'] ?? [];
		$maxRating = $attrs['rating']['advanced']['rating_range']['desktop']['value'] ?? '';
		$currentRating = (int) ($attrs['rating']['advanced']['rating']['desktop']['value'] ?? '');

		$option = [
			'maxRating' => $maxRating,
			'currentRating' => $currentRating,
			'containerId' => $uuid,
		];

		

		// Title.
		$title = $elements->render(
			[
				'attrName' => 'title',
			]
		);

		// Content.
		$content = $elements->render(
			[
				'attrName' => 'content',
			]
		);

		// stars.
		$stars_container = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'dotm_optimize_rating_stars',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => '',
			]
		);

		// image_container
		$rating_container = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'dotm_optimize_rating_container',
					'id' => $uuid,
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $stars_container,
			]
		);

		// image.
		$image = $elements->render(
			[
				'attrName' => 'image',
			]
		);

		// image_container
		$image_container = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'dotm_testimonials_image_container',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $image,
			]
		);

		$icon = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => "dotm_testimonials_icon",
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => Utils::process_font_icon($icon),
			]
		);

		// icon_container
		$icon_container = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'dotm_testimonials_icon_container',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $icon,
			]
		);

		// label.
		$label = $elements->render(
			[
				'attrName' => 'label',
			]
		);

		// name.
		$name = $elements->render(
			[
				'attrName' => 'name',
			]
		);

		// profile_content
		$profile_content = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'dotm_testimonials_profile_content',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $name . $label,
			]
		);

		// profile.
		$profile = $elements->render(
			[
				'attrName' => 'profile',
			]
		);

		// profile_container
		$profile_container = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'dotm_testimonials_profile_container',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $profile . $profile_content,
			]
		);

		$parent       = BlockParserStore::get_parent( $block->parsed_block['id'], $block->parsed_block['storeInstance'] );
		$parent_attrs = $parent->attrs ?? [];

		return Module::render(
			[
				// FE only.
				'orderIndex'          => $block->parsed_block['orderIndex'],
				'storeInstance'       => $block->parsed_block['storeInstance'],

				// VB equivalent.
				'attrs'               => $attrs,
				'elements'            => $elements,
				'id'                  => $block->parsed_block['id'],
				'name'                => $block->block_type->name,
				'moduleCategory'      => $block->block_type->category,
				'classnamesFunction'  => [ Testimonials::class, 'module_classnames' ],
				'stylesComponent'     => [ Testimonials::class, 'module_styles' ],
				'scriptDataComponent' => [ Testimonials::class, 'module_script_data' ],
				'parentAttrs'         => $parent_attrs,
				'parentId'            => $parent->id ?? '',
				'parentName'          => $parent->blockName ?? '',
				'children'            => [
					ElementComponents::component(
						[
							'attrs'         => $attrs['module']['decoration'] ?? [],
							'id'            => $block->parsed_block['id'],

							// FE only.
							'orderIndex'    => $block->parsed_block['orderIndex'],
							'storeInstance' => $block->parsed_block['storeInstance'],
						]
					),
					HTMLUtility::render(
						[
							'tag'               => 'div',
							'attributes'        => [
								'class' => 'dotm_testimonials_container',
							],
							'childrenSanitizer' => 'et_core_esc_previously',
							'children'          => ($useImage === "on" ? $image_container : "") . ($useImage === "off" ? $icon_container : "") . $content . $rating_container . $profile_container,
						]
					),
					HTMLUtility::render(
						[
							'tag' => 'script',
							'attributes' => [],
							'childrenSanitizer' => 'et_core_esc_previously',
							'children' => "document.addEventListener('DOMContentLoaded', function() {
                                const options = " . json_encode($option) . ";
                                rating(options);
                            });",
						]
					),
				],
			]
		);
	}
}
