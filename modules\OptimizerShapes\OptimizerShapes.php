<?php
/**
 * Module: Static Module class.
 *
 * @package MEE\Modules\OptimizerShapes
 * @since ??
 */

namespace MEE\Modules\OptimizerShapes;

if ( ! defined( 'ABSPATH' ) ) {
	die( 'Direct access forbidden.' );
}

use ET\Builder\Framework\DependencyManagement\Interfaces\DependencyInterface;
use ET\Builder\Packages\ModuleLibrary\ModuleRegistration;


/**
 * `OptimizerShapes` is consisted of functions used for Static Module such as Front-End rendering, REST API Endpoints etc.
 *
 * This is a dependency class and can be used as a dependency for `DependencyTree`.
 *
 * @since ??
 */
class OptimizerShapes implements DependencyInterface {
	use OptimizerShapesTrait\RenderCallbackTrait;
	use OptimizerShapesTrait\ModuleClassnamesTrait;
	use OptimizerShapesTrait\ModuleStylesTrait;
	use OptimizerShapesTrait\ModuleScriptDataTrait;

	/**
	 * Loads `OptimizerShapes` and registers Front-End render callback and REST API Endpoints.
	 *
	 * @since ??
	 *
	 * @return void
	 */
	public function load() {
		$module_json_folder_path = DIVIOPTIMIZER_MODULES_JSON_PATH . 'OptimizerShapes/';
		add_action(
			'init',
			function() use ( $module_json_folder_path ) {
				ModuleRegistration::register_module(
					$module_json_folder_path,
					[
						'render_callback' => [ OptimizerShapes::class, 'render_callback' ],
					]
				);
			}
		);
	}
}
