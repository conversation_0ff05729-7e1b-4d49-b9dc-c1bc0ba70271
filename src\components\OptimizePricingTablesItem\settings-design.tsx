// External dependencies.
import React, { ReactElement } from "react";

// WordPress dependencies.
import { __ } from "@wordpress/i18n";

// Divi dependencies.
import {
  AnimationGroup,
  BorderGroup,
  BoxShadowGroup,
  FieldContainer,
  FiltersGroup,
  FontGroup,
  FontBodyGroup,
  SizingGroup,
  SpacingGroup,
  TextGroup,
  TransformGroup,
  BackgroundGroup,
  ButtonGroupContainer,
} from "@divi/module";
import { GroupContainer } from "@divi/modal";
import {
  ColorPickerContainer,
  RangeContainer,
  SelectContainer,
} from "@divi/field-library";
import { mergeAttrs } from "@divi/module-utils";
import { OptimizePricingTablesAttrs } from "../OptimizePricingTables/types";
import { OptimizePricingTablesItemAttrs } from "./types";
import { type Module } from "@divi/types";

export const SettingsDesign = ({
  defaultSettingsAttrs,
  parentAttrs,
  attrs,
}: Module.Settings.Panel.Props<
  OptimizePricingTablesItemAttrs,
  OptimizePricingTablesAttrs
>): ReactElement => {
  const defaultIconColorAttrs = mergeAttrs({
    defaultAttrs:
      defaultSettingsAttrs?.icon?.advanced?.color?.asMutable({ deep: true }) ??
      {},
    attrs: parentAttrs?.icon?.advanced?.color?.asMutable({ deep: true }) ?? {},
  });

  const defaultIconSizeAttrs = mergeAttrs({
    defaultAttrs:
      defaultSettingsAttrs?.icon?.advanced?.size?.asMutable({ deep: true }) ??
      {},
    attrs: parentAttrs?.icon?.advanced?.size?.asMutable({ deep: true }) ?? {},
  });

  const defaultTextAttrs = mergeAttrs({
    defaultAttrs: defaultSettingsAttrs?.module?.advanced?.asMutable({
      deep: true,
    })?.text,
    attrs: parentAttrs?.module?.advanced?.asMutable({ deep: true })?.text,
  });

  const defaultTitleFontAttrs = mergeAttrs({
    defaultAttrs:
      defaultSettingsAttrs?.title?.decoration?.font?.asMutable({
        deep: true,
      }) ?? {},
    attrs:
      parentAttrs?.title?.decoration?.font?.asMutable({ deep: true }) ?? {},
  });

  const defaultSubTitleFontAttrs = mergeAttrs({
    defaultAttrs:
      defaultSettingsAttrs?.subTitle?.decoration?.font?.asMutable({
        deep: true,
      }) ?? {},
    attrs:
      parentAttrs?.subTitle?.decoration?.font?.asMutable({ deep: true }) ?? {},
  });

  const defaultCurrencyFontAttrs = mergeAttrs({
    defaultAttrs:
      defaultSettingsAttrs?.currency?.decoration?.font?.asMutable({
        deep: true,
      }) ?? {},
    attrs:
      parentAttrs?.currency?.decoration?.font?.asMutable({ deep: true }) ?? {},
  });

  const defaultPriceFontAttrs = mergeAttrs({
    defaultAttrs:
      defaultSettingsAttrs?.priceWrapper?.decoration?.font?.asMutable({
        deep: true,
      }) ?? {},
    attrs:
      parentAttrs?.priceWrapper?.decoration?.font?.asMutable({ deep: true }) ??
      {},
  });

  const defaultFrequencyFontAttrs = mergeAttrs({
    defaultAttrs:
      defaultSettingsAttrs?.frequency?.decoration?.font?.asMutable({
        deep: true,
      }) ?? {},
    attrs:
      parentAttrs?.frequency?.decoration?.font?.asMutable({ deep: true }) ?? {},
  });

  const defaultIconBgAttrs = mergeAttrs({
    defaultAttrs:
      defaultSettingsAttrs?.icon?.decoration?.background?.asMutable({
        deep: true,
      }) ?? {},
    attrs:
      parentAttrs?.icon?.decoration?.background?.asMutable({ deep: true }) ??
      {},
  });

  const defaultIconSpacingAttrs = mergeAttrs({
    defaultAttrs:
      defaultSettingsAttrs?.icon?.decoration?.spacing?.asMutable({
        deep: true,
      }) ?? {},
    attrs:
      parentAttrs?.icon?.decoration?.spacing?.asMutable({ deep: true }) ?? {},
  });

  const defaultIconBorderAttrs = mergeAttrs({
    defaultAttrs:
      defaultSettingsAttrs?.icon?.decoration?.border?.asMutable({
        deep: true,
      }) ?? {},
    attrs:
      parentAttrs?.icon?.decoration?.border?.asMutable({ deep: true }) ?? {},
  });

  const defaultBodyFontAttrs = mergeAttrs({
    defaultAttrs:
      defaultSettingsAttrs?.content?.decoration?.bodyFont?.asMutable({
        deep: true,
      }) ?? {},
    attrs:
      parentAttrs?.content?.decoration?.bodyFont?.asMutable({ deep: true }) ??
      {},
  });

  const defaultImageWidthAttrs = mergeAttrs({
    defaultAttrs:
      defaultSettingsAttrs?.image?.decoration?.img_width?.asMutable({
        deep: true,
      }) ?? {},
    attrs:
      parentAttrs?.image?.decoration?.img_width?.asMutable({ deep: true }) ??
      {},
  });

  const defaultImageHeightAttrs = mergeAttrs({
    defaultAttrs:
      defaultSettingsAttrs?.image?.decoration?.img_height?.asMutable({
        deep: true,
      }) ?? {},
    attrs:
      parentAttrs?.image?.decoration?.img_height?.asMutable({ deep: true }) ??
      {},
  });

  const defaultImageSpacingAttrs = mergeAttrs({
    defaultAttrs:
      defaultSettingsAttrs?.image?.decoration?.spacing?.asMutable({
        deep: true,
      }) ?? {},
    attrs:
      parentAttrs?.image?.decoration?.spacing?.asMutable({ deep: true }) ?? {},
  });

  const defaultImageBorderAttrs = mergeAttrs({
    defaultAttrs:
      defaultSettingsAttrs?.image?.decoration?.border?.asMutable({
        deep: true,
      }) ?? {},
    attrs:
      parentAttrs?.image?.decoration?.border?.asMutable({ deep: true }) ?? {},
  });

  const defaultHeadingWrapperBGAttrs = mergeAttrs({
    defaultAttrs:
      defaultSettingsAttrs?.headingWrapper?.decoration?.background?.asMutable({
        deep: true,
      }) ?? {},
    attrs:
      parentAttrs?.headingWrapper?.decoration?.background?.asMutable({
        deep: true,
      }) ?? {},
  });

  const defaultHeadingWrapperSpacingAttrs = mergeAttrs({
    defaultAttrs:
      defaultSettingsAttrs?.headingWrapper?.decoration?.spacing?.asMutable({
        deep: true,
      }) ?? {},
    attrs:
      parentAttrs?.headingWrapper?.decoration?.spacing?.asMutable({
        deep: true,
      }) ?? {},
  });

  const defaultHeadingWrapperBorderAttrs = mergeAttrs({
    defaultAttrs:
      defaultSettingsAttrs?.headingWrapper?.decoration?.border?.asMutable({
        deep: true,
      }) ?? {},
    attrs:
      parentAttrs?.headingWrapper?.decoration?.border?.asMutable({
        deep: true,
      }) ?? {},
  });

  const defaultHeadingWrapperTextAttrs = mergeAttrs({
    defaultAttrs:
      defaultSettingsAttrs?.headingWrapper?.advanced?.text?.asMutable({
        deep: true,
      }) ?? {},
    attrs:
      parentAttrs?.headingWrapper?.advanced?.text?.asMutable({ deep: true }) ??
      {},
  });

  const buttonType =
    attrs?.button?.advanced?.type?.desktop?.value ?? "inline-block";
  const useImage = attrs?.image?.advanced?.useImage?.desktop?.value ?? "off";
  const useFeature = attrs?.featureText?.advanced?.useFeature?.desktop?.value ?? "off";

  return (
    <React.Fragment>
      <GroupContainer
        id="feature"
        title={__("Feature Style", "divi-optimaizer-modules")}
        visible={useFeature === "on"}
      >
        <FontGroup
          groupLabel={__("Feature Typography", "divi-optimaizer-modules")}
          attrName="featureText.decoration.font"
          fieldLabel={__("Feature", "divi-optimaizer-modules")}
          defaultGroupAttr={
            defaultSettingsAttrs?.featureText?.decoration?.font?.asMutable({
              deep: true,
            }) ?? {}
          }
        />
        <BackgroundGroup
          attrName="featureText.decoration.background"
          groupLabel="Feature Background"
          fieldLabel="Feature"
          defaultGroupAttr={
            defaultSettingsAttrs?.featureText?.decoration?.background?.asMutable({
              deep: true,
            }) ?? {}
          }
        />
        <SpacingGroup
          attrName="featureText.decoration.spacing"
          groupLabel="Feature Spacing"
          fieldLabel="Feature"
          defaultGroupAttr={
            defaultSettingsAttrs?.featureText?.decoration?.spacing?.asMutable({
              deep: true,
            }) ?? {}
          }
        />
        <BorderGroup
          attrName="featureText.decoration.border"
          groupLabel="Feature Border"
          fieldLabel="Feature"
          defaultGroupAttr={
            defaultSettingsAttrs?.featureText?.decoration?.border?.asMutable({
              deep: true,
            }) ?? {}
          }
        />
      </GroupContainer>
      <GroupContainer
        id="icon"
        title={__("Icon Style", "divi-optimaizer-modules")}
        visible={useImage === "off"}
      >
        <FieldContainer
          attrName="icon.advanced.color"
          label={__("Icon Color", "divi-optimaizer-modules")}
          description={__(
            "Input your value to action title here.",
            "divi-optimaizer-modules"
          )}
          features={{
            sticky: false,
          }}
          defaultAttr={defaultIconColorAttrs}
        >
          <ColorPickerContainer />
        </FieldContainer>
        <FieldContainer
          attrName="icon.advanced.size"
          label={__("Icon Size", "divi-optimaizer-modules")}
          description={__(
            "Input your value to action title here.",
            "divi-optimaizer-modules"
          )}
          features={{
            sticky: false,
          }}
          defaultAttr={defaultIconSizeAttrs}
        >
          <RangeContainer />
        </FieldContainer>
        <BackgroundGroup
          attrName="icon.decoration.background"
          grouped={false}
          fieldLabel="Icon"
          defaultGroupAttr={defaultIconBgAttrs}
        />
        <SpacingGroup
          attrName="icon.decoration.spacing"
          grouped={false}
          fieldLabel="Icon"
          defaultGroupAttr={defaultIconSpacingAttrs}
        />
        <BorderGroup
          attrName="icon.decoration.border"
          grouped={false}
          fieldLabel="Icon"
          defaultGroupAttr={defaultIconBorderAttrs}
        />
      </GroupContainer>
      <GroupContainer
        id="image"
        title={__("Image Style", "divi-optimaizer-modules")}
        visible={useImage === "on"}
      >
        <FieldContainer
          attrName="image.decoration.img_width"
          label={__("Image Width", "divi-optimaizer-modules")}
          description={__(
            "Input your value to action here.",
            "divi-optimaizer-modules"
          )}
          features={{
            sticky: false,
          }}
          defaultAttr={defaultImageWidthAttrs}
        >
          <RangeContainer max={1000} />
        </FieldContainer>
        <FieldContainer
          attrName="image.decoration.img_height"
          label={__("Image Height", "divi-optimaizer-modules")}
          description={__(
            "Input your value to action here.",
            "divi-optimaizer-modules"
          )}
          features={{
            sticky: false,
          }}
          defaultAttr={defaultImageHeightAttrs}
        >
          <RangeContainer max={1000} />
        </FieldContainer>
        <SpacingGroup
          attrName="image.decoration.spacing"
          grouped={false}
          fieldLabel="Image"
          defaultGroupAttr={defaultImageSpacingAttrs}
        />
        <BorderGroup
          attrName="image.decoration.border"
          grouped={false}
          fieldLabel="Image"
          defaultGroupAttr={defaultImageBorderAttrs}
        />
      </GroupContainer>
      <GroupContainer
        id="heading"
        title={__("Heading Style", "divi-optimaizer-modules")}
      >
        <BackgroundGroup
          attrName="headingWrapper.decoration.background"
          groupLabel="Heading Background"
          fieldLabel="Heading"
          defaultGroupAttr={defaultHeadingWrapperBGAttrs}
        />
        <SpacingGroup
          attrName="headingWrapper.decoration.spacing"
          groupLabel="Heading Spacing"
          fieldLabel="Heading"
          defaultGroupAttr={defaultHeadingWrapperSpacingAttrs}
        />
        <BorderGroup
          attrName="headingWrapper.decoration.border"
          groupLabel="Heading Border"
          fieldLabel="Heading"
          defaultGroupAttr={defaultHeadingWrapperBorderAttrs}
        />
        <TextGroup
          attrName="headingWrapper.advanced.text"
          groupLabel="Heading Alignment"
          defaultGroupAttr={defaultHeadingWrapperTextAttrs}
          fields={{
            color: {
              render: false,
            },
            textShadowGroup: {
              render: false,
            },
          }}
        />
      </GroupContainer>
      <GroupContainer
        id="alignment"
        title={__("Price Container Alignment", "divi-optimaizer-modules")}
      >
        <FieldContainer
          attrName="priceContainer.decoration.alignment"
          label={__("Price Container Alignment", "divi-optimaizer-modules")}
          description={__(
            "Input your value to action here.",
            "divi-optimaizer-modules"
          )}
          features={{
            sticky: false,
          }}
          defaultAttr={
            defaultSettingsAttrs?.priceContainer?.decoration?.alignment
          }
        >
          <SelectContainer
            options={{
              "flex-start": { label: "Left" },
              center: { label: "Center" },
              "flex-end": { label: "Right" },
            }}
          />
        </FieldContainer>
      </GroupContainer>

      {/* <TextGroup
        defaultGroupAttr={defaultTextAttrs}
        fields={{
          color: {
            render: false,
          },
        }}
      /> */}

      <FontGroup
        groupLabel={__("Title Typography", "divi-optimaizer-modules")}
        attrName="title.decoration.font"
        fieldLabel={__("Title", "divi-optimaizer-modules")}
        defaultGroupAttr={defaultTitleFontAttrs}
        fields={{
          headingLevel: {
            render: true,
          },
        }}
      />
      <FontGroup
        groupLabel={__("Subtitle Typography", "divi-optimaizer-modules")}
        attrName="subTitle.decoration.font"
        fieldLabel={__("Subtitle", "divi-optimaizer-modules")}
        defaultGroupAttr={defaultSubTitleFontAttrs}
        fields={{
          headingLevel: {
            render: true,
          },
        }}
      />
      <FontGroup
        groupLabel={__("Currency Typography", "divi-optimaizer-modules")}
        attrName="currency.decoration.font"
        fieldLabel={__("Currency", "divi-optimaizer-modules")}
        defaultGroupAttr={defaultCurrencyFontAttrs}
      />
      <FontGroup
        groupLabel={__("Price Typography", "divi-optimaizer-modules")}
        attrName="priceWrapper.decoration.font"
        fieldLabel={__("Price", "divi-optimaizer-modules")}
        defaultGroupAttr={defaultPriceFontAttrs}
      />
      <FontGroup
        groupLabel={__("Frequency Typography", "divi-optimaizer-modules")}
        attrName="frequency.decoration.font"
        fieldLabel={__("frequency", "divi-optimaizer-modules")}
        defaultGroupAttr={defaultFrequencyFontAttrs}
      />
      <FontBodyGroup
        attrName="content.decoration.bodyFont"
        defaultGroupAttr={defaultBodyFontAttrs}
      />
      <ButtonGroupContainer
        fields={{
          alignment: {
            render: false,
          },
        }}
      />
      <GroupContainer
        id="buttonType"
        title={__("Button Type", "divi-optimaizer-modules")}
      >
        <FieldContainer
          attrName="button.advanced.type"
          label={__("Button Type", "divi-optimaizer-modules")}
          description={__(
            "Input your value to action here.",
            "divi-optimaizer-modules"
          )}
          features={{
            sticky: false,
          }}
          defaultAttr={defaultSettingsAttrs?.button?.advanced?.type}
        >
          <SelectContainer
            options={{
              "inline-block": { label: "Content Width" },
              block: { label: "Full Width" },
            }}
          />
        </FieldContainer>
        <FieldContainer
          attrName="button.advanced.buttonAlignment"
          label={__("Button Alignment", "divi-optimaizer-modules")}
          description={__(
            "Input your value to action here.",
            "divi-optimaizer-modules"
          )}
          features={{
            sticky: false,
          }}
          defaultAttr={defaultSettingsAttrs?.button?.advanced?.buttonAlignment}
          visible={buttonType === "inline-block"}
        >
          <SelectContainer
            options={{
              left: { label: "Left" },
              center: { label: "Center" },
              right: { label: "Right" },
            }}
          />
        </FieldContainer>
      </GroupContainer>
      <SizingGroup
        attrName="module.decoration.sizing"
        defaultGroupAttr={defaultSettingsAttrs?.module?.decoration?.sizing}
      />
      <GroupContainer
        id="custom"
        title={__("Custom Spacing", "divi-optimaizer-modules")}
      >
        <SpacingGroup
          attrName="title.decoration.spacing"
          fieldLabel="Title"
          grouped={false}
        />
        <SpacingGroup
          attrName="subTitle.decoration.spacing"
          fieldLabel="Subtitle"
          grouped={false}
        />
        <SpacingGroup
          attrName="priceContainer.decoration.spacing"
          fieldLabel="Price Container"
          grouped={false}
          defaultGroupAttr={
            defaultSettingsAttrs?.priceContainer?.decoration?.spacing?.asMutable(
              { deep: true }
            ) ?? {}
          }
        />
        <SpacingGroup
          attrName="priceWrapper.decoration.spacing"
          fieldLabel="Price"
          grouped={false}
        />
        <SpacingGroup
          attrName="frequency.decoration.spacing"
          fieldLabel="Frequency"
          grouped={false}
        />
        <SpacingGroup attrName="module.decoration.spacing" grouped={false} />
      </GroupContainer>

      <BorderGroup
        attrName="module.decoration.border"
        defaultGroupAttr={defaultSettingsAttrs?.module?.decoration?.border}
      />

      <BorderGroup
        attrName="priceContainer.decoration.border"
        defaultGroupAttr={
          defaultSettingsAttrs?.priceContainer?.decoration?.border
        }
        fields={{
          radius: {
            render: false,
          },
        }}
        groupLabel="Price Container Border"
      />
      <BoxShadowGroup />
      <FiltersGroup />
      <TransformGroup />
      <AnimationGroup />
    </React.Fragment>
  );
};
