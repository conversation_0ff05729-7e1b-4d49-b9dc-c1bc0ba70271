<?php

/**
 * Module: TextBadge class.
 *
 * @package MEE\Modules\TextBadge
 * @since ??
 */

namespace MEE\Modules\TextBadge;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

use ET\Builder\Framework\DependencyManagement\Interfaces\DependencyInterface;
use ET\Builder\Packages\ModuleLibrary\ModuleRegistration;


/**
 * `TextBadge` is consisted of functions used for Static Module such as Front-End rendering, REST API Endpoints etc.
 *
 * This is a dependency class and can be used as a dependency for `DependencyTree`.
 *
 * @since ??
 */
class TextBadge implements DependencyInterface
{
	use TextBadgeTrait\RenderCallbackTrait;
	use TextBadgeTrait\ModuleClassnamesTrait;
	use TextBadgeTrait\ModuleStylesTrait;
	use TextBadgeTrait\ModuleScriptDataTrait;

	/**
	 * Loads `TextBadge` and registers Front-End render callback and REST API Endpoints.
	 *
	 * @since ??
	 *
	 * @return void
	 */
	public function load()
	{
		$module_json_folder_path = DIVIOPTIMIZER_MODULES_JSON_PATH . 'TextBadge/';

		add_action(
			'init',
			function () use ($module_json_folder_path) {
				ModuleRegistration::register_module(
					$module_json_folder_path,
					[
						'render_callback' => [TextBadge::class, 'render_callback'],
					]
				);
			}
		);
	}
}
