<?php

/**
 * Module: Static Module class.
 *
 * @package MEE\Modules\TypingEffect
 * @since ??
 */

namespace MEE\Modules\TypingEffect;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

use ET\Builder\Framework\DependencyManagement\Interfaces\DependencyInterface;
use ET\Builder\Packages\ModuleLibrary\ModuleRegistration;


/**
 * ` TypingEffect` is consisted of functions used for Static Module such as Front-End rendering, REST API Endpoints etc.
 *
 * This is a dependency class and can be used as a dependency for `DependencyTree`.
 *
 * @since ??
 */
class TypingEffect implements DependencyInterface
{
	use TypingEffectTrait\RenderCallbackTrait;
	use TypingEffectTrait\ModuleClassnamesTrait;
	use TypingEffectTrait\ModuleStylesTrait;
	use TypingEffectTrait\ModuleScriptDataTrait;

	/**
	 * Loads ` TypingEffect` and registers Front-End render callback and REST API Endpoints.
	 *
	 * @since ??
	 *
	 * @return void
	 */
	public function load()
	{
		$module_json_folder_path = DIVIOPTIMIZER_MODULES_JSON_PATH . 'typing-effect/';

		add_action(
			'init',
			function () use ($module_json_folder_path) {
				ModuleRegistration::register_module(
					$module_json_folder_path,
					[
						'render_callback' => [TypingEffect::class, 'render_callback'],
					]
				);
			}
		);
	}
}
