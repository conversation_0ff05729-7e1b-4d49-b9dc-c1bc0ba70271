<?php
/**
 * MotionTextColor::render_callback()
 *
 * @package MEE\Modules\MotionTextColor
 * @since ??
 */

namespace MEE\Modules\MotionTextColor\MotionTextColorTrait;

if ( ! defined( 'ABSPATH' ) ) {
	die( 'Direct access forbidden.' );
}

// phpcs:disable ET.Sniffs.ValidVariableName.UsedPropertyNotSnakeCase -- WP use snakeCase in \WP_Block_Parser_Block

use ET\Builder\Packages\Module\Module;
use ET\Builder\Framework\Utility\HTMLUtility;
use ET\Builder\FrontEnd\BlockParser\BlockParserStore;
use ET\Builder\Packages\Module\Options\Element\ElementComponents;
use MEE\Modules\MotionTextColor\MotionTextColor;

trait RenderCallbackTrait {

	/**
	 * MotionTextColor render callback which outputs server side rendered HTML on the Front-End.
	 *
	 * @since ??
	 * @param array          $attrs    Block attributes that were saved by VB.
	 * @param string         $content  Block content.
	 * @param WP_Block       $block    Parsed block object that being rendered.
	 * @param ModuleElements $elements ModuleElements instance.
	 *
	 * @return string HTML rendered of MotionTextColor.
	 */
	public static function render_callback( $attrs, $content, $block, $elements ) {
		// Image.
		$motion_style = $attrs['motion_text']['advanced']['motionStyle']['desktop']['value'] ?? '';

		// motion_text.
		$motion_text = $elements->render(
			[
				'attrName' => 'motion_text',
				'attributes' => [
					'class' => "dotm_motion_text_color_text dotm_motion_text_color_{$motion_style}",
				],
			]
		);


		

		$parent       = BlockParserStore::get_parent( $block->parsed_block['id'], $block->parsed_block['storeInstance'] );
		$parent_attrs = $parent->attrs ?? [];

		return Module::render(
			[
				// FE only.
				'orderIndex'          => $block->parsed_block['orderIndex'],
				'storeInstance'       => $block->parsed_block['storeInstance'],

				// VB equivalent.
				'attrs'               => $attrs,
				'elements'            => $elements,
				'id'                  => $block->parsed_block['id'],
				'name'                => $block->block_type->name,
				'moduleCategory'      => $block->block_type->category,
				'classnamesFunction'  => [ MotionTextColor::class, 'module_classnames' ],
				'stylesComponent'     => [ MotionTextColor::class, 'module_styles' ],
				'scriptDataComponent' => [ MotionTextColor::class, 'module_script_data' ],
				'parentAttrs'         => $parent_attrs,
				'parentId'            => $parent->id ?? '',
				'parentName'          => $parent->blockName ?? '',
				'children'            => [
					ElementComponents::component(
						[
							'attrs'         => $attrs['module']['decoration'] ?? [],
							'id'            => $block->parsed_block['id'],

							// FE only.
							'orderIndex'    => $block->parsed_block['orderIndex'],
							'storeInstance' => $block->parsed_block['storeInstance'],
						]
					),
					HTMLUtility::render(
						[
							'tag'               => 'div',
							'attributes'        => [
								'class' => 'dotm_motion_text_color_container',
							],
							'childrenSanitizer' => 'et_core_esc_previously',
							'children'          => $motion_text,
						]
					),
				],
			]
		);
	}
}
