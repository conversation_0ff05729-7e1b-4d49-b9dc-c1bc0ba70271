<?php

/**
 * OptimizeCardCarousel::module_styles().
 *
 * @package MEE\Modules\OptimizeCardCarousel
 * @since ??
 */

namespace MEE\Modules\OptimizeCardCarousel\OptimizeCardCarouselTrait;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

use ET\Builder\FrontEnd\Module\Style;
use ET\Builder\Packages\Module\Layout\Components\StyleCommon\CommonStyle;
use ET\Builder\Packages\Module\Options\Css\CssStyle;
use MEE\Modules\OptimizeCardCarouselItem\OptimizeCardCarouselItem;

trait ModuleStylesTrait
{

	use CustomCssTrait;

	/**
	 * OptimizeCardCarouselItem's style components.
	 *
	 * This function is equivalent of JS function ModuleStyles located in
	 * src/components/OptimizeCardCarousel/styles.tsx.
	 *
	 * @param array $args {
	 *     An array of arguments.
	 *
	 * @type string $id Module ID. In VB, the ID of module is UUIDV4. In FE, the ID is order index.
	 * @type string $name Module name.
	 * @type string $attrs Module attributes.
	 * @type string $parentAttrs Parent attrs.
	 * @type string $orderClass Selector class name.
	 * @type string $parentOrderClass Parent selector class name.
	 * @type string $wrapperOrderClass Wrapper selector class name.
	 * @type string $settings Custom settings.
	 * @type string $state Attributes state.
	 * @type string $mode Style mode.
	 * @type ModuleElements $elements ModuleElements instance.
	 * }
	 * @since ??
	 */
	public static function module_styles($args)
	{
		$attrs        = $args['attrs'] ?? [];
		$parent_attrs = $args['parentAttrs'] ?? [];
		$order_class  = $args['orderClass'];
		$elements     = $args['elements'];
		$settings     = $args['settings'] ?? [];

		$slick_slide_selector = $order_class . ' .slick-slide';
		$slick_current_selector = $order_class . ' .slick-current';
		$arrows_selector = $order_class . ' .slick-arrow';
		$sliders_selector = $order_class . ' .slick-track';
		$dot_selector = $order_class . ' .slick-dots li button:before';
		$active_dot_selector = $order_class . ' .slick-dots li.slick-active button:before';
		$dot_list_selector = $order_class . ' .slick-dots';
		// $prev_icon = isset($attrs['prev_arrow']['innerContent']) ? self::get_attr_by_mode($attrs['prev_arrow']['innerContent']) : '';
		// $next_icon = isset($attrs['nxt_arrow']['innerContent']) ? self::get_attr_by_mode($attrs['nxt_arrow']['innerContent']) : '';
		$dots_direction = $attrs['dots']['decoration']['dots_direction']['desktop']['value'] ?? 'off';
		$use_fade = $attrs['carousel']['advanced']['use_fade']['desktop']['value'] ?? '';

		Style::add(
			[
				'id'            => $args['id'],
				'name'          => $args['name'],
				'orderIndex'    => $args['orderIndex'],
				'storeInstance' => $args['storeInstance'],
				'styles'        => [
					// Module.
					$elements->style(
						[
							'attrName'   => 'module',
							'styleProps' => [
								'disabledOn' => [
									'disabledModuleVisibility' => $settings['disabledModuleVisibility'] ?? null,
								],
								'advancedStyles' => [
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector'        => $sliders_selector,
											'attr'                => $attrs['carousel']['advanced']['slider_gap'] ?? [],
											'property'             => 'gap',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector'        => $slick_slide_selector,
											'attr'                => $attrs['carousel']['advanced']['use_fade'] ?? [],
											'property'             => $use_fade === 'on' ? 'visibility: hidden;' : '',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector'        => $slick_current_selector,
											'attr'                => $attrs['carousel']['advanced']['use_fade'] ?? [],
											'property'             => $use_fade === 'on' ? 'visibility: visible;' : '',
										]
									],
								]
							],
						]
					),

					// Title.
					$elements->style(
						[
							'attrName' => 'title',
						]
					),

					// Content.
					$elements->style(
						[
							'attrName' => 'content',
						]
					),

					// Button.
					$elements->style(
						[
							'attrName' => 'button',
						]
					),

					// Dots.
					$elements->style(
						[
							'attrName'   => 'dots',
							'styleProps' => [
								'advancedStyles' => [
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $dot_selector,
											'attr'     => $attrs['dots']['decoration']['dots_width'] ?? [],
											'property' => 'width',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $dot_selector,
											'attr'     => $attrs['dots']['decoration']['dots_height'] ?? [],
											'property' => 'height',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $dot_selector,
											'attr'     => $attrs['dots']['decoration']['dots_color'] ?? [],
											'property' => 'background',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $active_dot_selector,
											'attr'     => $attrs['dots']['decoration']['active_dot_color'] ?? [],
											'property' => 'background',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $dot_list_selector,
											'attr'     => $attrs['dots']['decoration']['dots_gap'] ?? [],
											'property' => 'gap',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $dot_list_selector,
											'attr'     => $attrs['dots']['decoration']['vr_position'] ?? [],
											'property' => 'bottom',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $dot_list_selector,
											'attr'     => $attrs['dots']['decoration']['hr_position'] ?? [],
											'property' => 'left',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $dot_list_selector,
											'attr'     => $attrs['dots']['decoration']['dots_direction'] ?? [],
											'property' => $dots_direction === 'on' ? 'flex-direction: column;' : 'flex-direction: row;',
										]
									],
								]
							]
						]
					),

					// image_wrapper.
					$elements->style(
						[
							'attrName' => 'image_wrapper',
						]
					),

					// image.
					$elements->style(
						[
							'attrName' => 'image',
						]
					),

					// marge_arrow.
					$elements->style(
						[
							'attrName' => 'marge_arrow',
						]
					),

					// prev_arrow.
					$elements->style(
						[
							'attrName'   => 'prev_arrow',
							'styleProps' => [
								'advancedStyles' => [
									[
										'componentName' => 'divi/common',
										'props'         => [
											'attr'                => $attrs['prev_arrow']['innerContent'] ?? [],
											'declarationFunction' => [OptimizeCardCarouselItem::class, 'icon_font_declaration'],
										]
									]
								]
							]
						]
					),

					// next_arrow.
					$elements->style(
						[
							'attrName'   => 'nxt_arrow',
							'styleProps' => [
								'advancedStyles' => [
									[
										'componentName' => 'divi/common',
										'props'         => [
											'attr'                => $attrs['nxt_arrow']['innerContent'] ?? [],
											'declarationFunction' => [OptimizeCardCarouselItem::class, 'icon_font_declaration'],
										]
									]
								]
							]
						]
					),

					// arrows.
					$elements->style(
						[
							'attrName'   => 'arrows',
							'styleProps' => [
								'advancedStyles' => [
									[
										'componentName' => 'divi/common',
										'props'         => [
											'attr'                => $attrs['arrows']['advanced']['color'] ?? [],
											'property' => 'color',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'attr'                => $attrs['arrows']['advanced']['size'] ?? [],
											'property' => 'font-size',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector'        => $arrows_selector,
											'attr'                => $attrs['arrows']['advanced']['vr_possition'] ?? [],
											'property' => 'top',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector'        => "{$order_class} .slick-prev",
											'attr'                => $attrs['arrows']['advanced']['prev_hr_possition'] ?? [],
											'property' => 'left',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector'        => "{$order_class} .slick-next",
											'attr'                => $attrs['arrows']['advanced']['next_hr_possition'] ?? [],
											'property' => 'right',
										]
									]
								]
							]
						]
					),

					/*
					 * We need to add CssStyle at the very bottom of other
					 * components so that custom css can override module styles
					 * till we find a more elegant solution.
					 */
					CssStyle::style(
						[
							'selector'  => $args['orderClass'],
							'attr'      => $attrs['css'] ?? [],
							'cssFields' => self::custom_css(),
						]
					),

					// ATTENTION: The code is intentionally added and commented in FE only as an example of expected value format.
					// If you have custom style processing, the style output should be passed as an `array` of style declarations
					// to the `styles` property of the `Style::add` method. For example:
					// [
					// 	[
					// 		'atRules'     => false,
					// 		'selector'    => $icon_selector,
					// 		'declaration' => 'color: red;'
					// 	],
					// 	[
					// 		'atRules'     => '@media only screen and (max-width: 767px)',
					// 		'selector'    => $icon_selector,
					// 		'declaration' => 'color: green;'
					// 	],
					// ],

					// The code below is an example of how to use the `CssStyle::style` method to generate CSS style.
					CssStyle::style(
						[
							'selector'  => $args['orderClass'],
							'attr'      => $attrs['css'] ?? [],
							'cssFields' => self::custom_css(),
						]
					),
				],
			]
		);
	}
}
