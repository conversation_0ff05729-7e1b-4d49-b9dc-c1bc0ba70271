<?php

/**
 * Module: OptimizeCard class.
 *
 * @package MEE\Modules\OptimizeCard
 * @since ??
 */

namespace MEE\Modules\OptimizeCard;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

use ET\Builder\Framework\DependencyManagement\Interfaces\DependencyInterface;
use ET\Builder\Packages\ModuleLibrary\ModuleRegistration;


/**
 * `OptimizeCard` is consisted of functions used for OptimizeCard such as Front-End rendering, REST API Endpoints etc.
 *
 * This is a dependency class and can be used as a dependency for `DependencyTree`.
 *
 * @since ??
 */
class OptimizeCard implements DependencyInterface
{
	use OptimizeCardTrait\RenderCallbackTrait;
	use OptimizeCardTrait\ModuleClassnamesTrait;
	use OptimizeCardTrait\ModuleStylesTrait;
	use OptimizeCardTrait\ModuleScriptDataTrait;

	/**
	 * Loads `OptimizeCard` and registers Front-End render callback and REST API Endpoints.
	 *
	 * @since ??
	 *
	 * @return void
	 */
	public function load()
	{
		$module_json_folder_path = DIVIOPTIMIZER_MODULES_JSON_PATH . 'OptimizeCard/';

		add_action(
			'init',
			function () use ($module_json_folder_path) {
				ModuleRegistration::register_module(
					$module_json_folder_path,
					[
						'render_callback' => [OptimizeCard::class, 'render_callback'],
					]
				);
			}
		);
	}
}
