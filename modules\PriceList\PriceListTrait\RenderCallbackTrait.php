<?php
/**
 * PriceListItem::render_callback()
 *
 * @package MEE\Modules\PriceListItem
 * @since ??
 */

namespace MEE\Modules\PriceList\PriceListTrait;

if ( ! defined( 'ABSPATH' ) ) {
	die( 'Direct access forbidden.' );
}

// phpcs:disable ET.Sniffs.ValidVariableName.UsedPropertyNotSnakeCase -- WP use snakeCase in \WP_Block_Parser_Block

use ET\Builder\Framework\Utility\HTMLUtility;
use ET\Builder\FrontEnd\BlockParser\BlockParserStore;
use ET\Builder\Packages\IconLibrary\IconFont\Utils;
use ET\Builder\Packages\Module\Module;
use ET\Builder\Packages\Module\Options\Element\ElementComponents;
use MEE\Modules\PriceList\PriceList;

trait RenderCallbackTrait {
	use ModuleClassnamesTrait;
	use ModuleStylesTrait;
	use ModuleScriptDataTrait;
	/**
	 * Parent module render callback which outputs server side rendered HTML on the Front-End.
	 *
	 * @since ??
	 *
	 * @param array          $attrs Block attributes that were saved by VB.
	 * @param string         $content          Block content.
	 * @param \WP_Block      $block            Parsed block object that being rendered.
	 * @param ModuleElements $elements         ModuleElements instance.
	 *
	 * @return string HTML rendered of Parent module.
	 */
	public static function render_callback( $attrs, $content, $block, $elements ) {
		$children_ids = $block->parsed_block['innerBlocks'] ? array_map(
			function( $inner_block ) {
				return $inner_block['id'];
			},
			$block->parsed_block['innerBlocks']
		) : [];

		$parent       = BlockParserStore::get_parent( $block->parsed_block['id'], $block->parsed_block['storeInstance'] );
		$parent_attrs = $parent->attrs ?? [];

		$show_contents = $attrs['contentContainer']['advanced']['use']['desktop']['value'] ?? '';
		$show_image = $attrs['image']['advanced']['use']['desktop']['value'] ?? '';
		$show_icon = $attrs['icon']['advanced']['use']['desktop']['value'] ?? '';
		$icon = $attrs['icon']['innerContent']['desktop']['value'] ?? '';

		$image = $elements->render(
			[
				'attrName' => 'image',
			]
		);

		$image_container = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'dotm_price_list_image_container',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $image,
			]
		);

		$icon = HTMLUtility::render(
			[
				'tag'               => 'span',
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => Utils::process_font_icon($icon),
			]
		);

		$icon_container = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'dotm_price_list_icon_container',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $icon,
			]
		);

		$header = $elements->render(
			[
				'attrName' => 'header',
			]
		);

		$content_container = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'dotm_price_list_container',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => ($show_contents === "on" ? ($show_image === "on" ? $image_container : '') . ($show_icon === "on" ? $icon_container : '') . $header : ''),
			]
		);

		$child_container = HTMLUtility::render(
			[
				'tag'               => 'ul',
				'attributes'        => [
					'class' => 'dotm_price_list_child_wrapper',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $content,
			]
		);

		return Module::render(
			[
				// FE only.
				'orderIndex'          => $block->parsed_block['orderIndex'],
				'storeInstance'       => $block->parsed_block['storeInstance'],

				// VB equivalent.
				'id'                  => $block->parsed_block['id'],
				'name'                => $block->block_type->name,
				'moduleCategory'      => $block->block_type->category,
				'attrs'               => $attrs,
				'elements'            => $elements,
				'classnamesFunction'  => [ PriceList::class, 'module_classnames' ],
				'scriptDataComponent' => [ PriceList::class, 'module_script_data' ],
				'stylesComponent'     => [ PriceList::class, 'module_styles' ],
				'parentAttrs'         => $parent_attrs,
				'parentId'            => $parent->id ?? '',
				'parentName'          => $parent->blockName ?? '',
				'children'            => ElementComponents::component(
					[
						'attrs'         => $attrs['module']['decoration'] ?? [],
						'id'            => $block->parsed_block['id'],

						// FE only.
						'orderIndex'    => $block->parsed_block['orderIndex'],
						'storeInstance' => $block->parsed_block['storeInstance'],
					]
				) . $content_container . $child_container,
				'childrenIds'         => $children_ids,
			]
		);
	}
}
