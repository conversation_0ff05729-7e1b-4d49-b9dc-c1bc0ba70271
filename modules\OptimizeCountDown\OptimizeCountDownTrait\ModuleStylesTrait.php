<?php

/**
 * OptimizeCountDown::module_styles().
 *
 * @package MEE\Modules\OptimizeCountDown
 * @since ??
 */

namespace MEE\Modules\OptimizeCountDown\OptimizeCountDownTrait;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

use ET\Builder\FrontEnd\Module\Style;
use ET\Builder\Packages\Module\Layout\Components\StyleCommon\CommonStyle;
use ET\Builder\Packages\Module\Options\Css\CssStyle;
use MEE\Modules\ChildModule\ChildModule;

trait ModuleStylesTrait
{

	use CustomCssTrait;

	/**
	 * Child Module's style components.
	 *
	 * This function is equivalent of JS function ModuleStyles located in
	 * src/components/OptimizeCountDown/styles.tsx.
	 *
	 * @param array $args {
	 *     An array of arguments.
	 *
	 * @type string $id Module ID. In VB, the ID of module is UUIDV4. In FE, the ID is order index.
	 * @type string $name Module name.
	 * @type string $attrs Module attributes.
	 * @type string $parentAttrs Parent attrs.
	 * @type string $orderClass Selector class name.
	 * @type string $parentOrderClass Parent selector class name.
	 * @type string $wrapperOrderClass Wrapper selector class name.
	 * @type string $settings Custom settings.
	 * @type string $state Attributes state.
	 * @type string $mode Style mode.
	 * @type ModuleElements $elements ModuleElements instance.
	 * }
	 * @since ??
	 */
	public static function module_styles($args)
	{
		$attrs        = $args['attrs'] ?? [];
		$parent_attrs = $args['parentAttrs'] ?? [];
		$order_class  = $args['orderClass'];
		$elements     = $args['elements'];
		$settings     = $args['settings'] ?? [];

		$tikTikSelector = "{$order_class} .dotm_coming_soon_timer_tik";
		$tikTikContainerSelector = "{$order_class} .dotm_coming_soon_timer_blink";
		$timerSelector = "{$order_class} .dotm_coming_soon_timer";

		Style::add(
			[
				'id'            => $args['id'],
				'name'          => $args['name'],
				'orderIndex'    => $args['orderIndex'],
				'storeInstance' => $args['storeInstance'],
				'styles'        => [
					// Module.
					$elements->style(
						[
							'attrName'   => 'module',
							'styleProps' => [
								'disabledOn' => [
									'disabledModuleVisibility' => $settings['disabledModuleVisibility'] ?? null,
								],
							],
						]
					),

					// Title.
					$elements->style(
						[
							'attrName' => 'title',
						]
					),

					// blinkWrapper.
					$elements->style(
						[
							'attrName' => 'blinkWrapper',
						]
					),

					// blink.
					$elements->style(
						[
							'attrName' => 'blink',
						]
					),

					// timerContainer.
					$elements->style(
						[
							'attrName' => 'timerContainer',
							'styleProps' => [
								'advancedStyles' => [
									[
										'componentName' => 'divi/common',
										'props'         => [
											'attr'     => $attrs['timerContainer']['decoration']['gap'] ?? [],
											'property' => 'gap',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $tikTikSelector,
											'attr'     => $attrs['timerContainer']['decoration']['blinkSize'] ?? [],
											'property' => 'width',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $tikTikSelector,
											'attr'     => $attrs['timerContainer']['decoration']['blinkSize'] ?? [],
											'property' => 'height',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $tikTikSelector,
											'attr'     => $attrs['timerContainer']['decoration']['blinkColor'] ?? [],
											'property' => 'background-color',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $tikTikContainerSelector,
											'attr'     => $attrs['timerContainer']['decoration']['blinkGap'] ?? [],
											'property' => 'gap',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $timerSelector,
											'attr'     => $attrs['timerContainer']['decoration']['alignment'] ?? [],
											'property' => 'justify-content',
										]
									],
								]
							]
						]
					),

					// timer.
					$elements->style(
						[
							'attrName' => 'timer',
						]
					),

					// time.
					$elements->style(
						[
							'attrName' => 'time',
						]
					),

					// timerLabel.
					$elements->style(
						[
							'attrName' => 'timerLabel',
						]
					),

					// days_wrapper.
					$elements->style(
						[
							'attrName' => 'days_wrapper',
							'styleProps' => [
								'advancedStyles' => [
									[
										'componentName' => 'divi/common',
										'props'         => [
											'attr'     => $attrs['days_wrapper']['decoration']['dayasWidth'] ?? [],
											'property' => 'width',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'attr'     => $attrs['days_wrapper']['decoration']['dayasHeight'] ?? [],
											'property' => 'height',
										]
									],
								]
							]
						]
					),

					// hours_wrapper.
					$elements->style(
						[
							'attrName' => 'hours_wrapper',
							'styleProps' => [
								'advancedStyles' => [
									[
										'componentName' => 'divi/common',
										'props'         => [
											'attr'     => $attrs['hours_wrapper']['decoration']['dayasWidth'] ?? [],
											'property' => 'width',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'attr'     => $attrs['hours_wrapper']['decoration']['dayasHeight'] ?? [],
											'property' => 'height',
										]
									],
								]
							]
						]
					),

					// minutes_wrapper.
					$elements->style(
						[
							'attrName' => 'minutes_wrapper',
							'styleProps' => [
								'advancedStyles' => [
									[
										'componentName' => 'divi/common',
										'props'         => [
											'attr'     => $attrs['minutes_wrapper']['decoration']['dayasWidth'] ?? [],
											'property' => 'width',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'attr'     => $attrs['minutes_wrapper']['decoration']['dayasHeight'] ?? [],
											'property' => 'height',
										]
									],
								]
							]
						]
					),

					// seconds_wrapper.
					$elements->style(
						[
							'attrName' => 'seconds_wrapper',
							'styleProps' => [
								'advancedStyles' => [
									[
										'componentName' => 'divi/common',
										'props'         => [
											'attr'     => $attrs['seconds_wrapper']['decoration']['dayasWidth'] ?? [],
											'property' => 'width',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'attr'     => $attrs['seconds_wrapper']['decoration']['dayasHeight'] ?? [],
											'property' => 'height',
										]
									],
								]
							]
						]
					),

					/*
					 * We need to add CssStyle at the very bottom of other
					 * components so that custom css can override module styles
					 * till we find a more elegant solution.
					 */
					CssStyle::style(
						[
							'selector'  => $args['orderClass'],
							'attr'      => $attrs['css'] ?? [],
							'cssFields' => self::custom_css(),
						]
					),

					// ATTENTION: The code is intentionally added and commented in FE only as an example of expected value format.
					// If you have custom style processing, the style output should be passed as an `array` of style declarations
					// to the `styles` property of the `Style::add` method. For example:
					// [
					// 	[
					// 		'atRules'     => false,
					// 		'selector'    => $icon_selector,
					// 		'declaration' => 'color: red;'
					// 	],
					// 	[
					// 		'atRules'     => '@media only screen and (max-width: 767px)',
					// 		'selector'    => $icon_selector,
					// 		'declaration' => 'color: green;'
					// 	],
					// ],

					// The code below is an example of how to use the `CssStyle::style` method to generate CSS style.
					CssStyle::style(
						[
							'selector'  => $args['orderClass'],
							'attr'      => $attrs['css'] ?? [],
							'cssFields' => self::custom_css(),
						]
					),
				],
			]
		);
	}
}
