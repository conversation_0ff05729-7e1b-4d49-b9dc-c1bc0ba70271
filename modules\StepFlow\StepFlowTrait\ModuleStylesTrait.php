<?php

/**
 * StepFlow::module_styles().
 *
 * @package MEE\Modules\StepFlow
 * @since ??
 */

namespace MEE\Modules\StepFlow\StepFlowTrait;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

use ET\Builder\FrontEnd\Module\Style;
use ET\Builder\Packages\Module\Layout\Components\StyleCommon\CommonStyle;
use ET\Builder\Packages\Module\Options\Text\TextStyle;
use ET\Builder\Packages\Module\Options\Css\CssStyle;
use MEE\Modules\StepFlow\StepFlow;

trait ModuleStylesTrait
{

	use CustomCssTrait;
	use StyleDeclarationTrait;

	/**
	 * StepFlow's style components.
	 *
	 * This function is equivalent of JS function ModuleStyles located in
	 * src/components/StepFlow/styles.tsx.
	 *
	 * @since ??
	 *
	 * @param array $args {
	 *     An array of arguments.
	 *
	 *      @type string $id                Module ID. In VB, the ID of module is UUIDV4. In FE, the ID is order index.
	 *      @type string $name              Module name.
	 *      @type string $attrs             Module attributes.
	 *      @type string $parentAttrs       Parent attrs.
	 *      @type string $orderClass        Selector class name.
	 *      @type string $parentOrderClass  Parent selector class name.
	 *      @type string $wrapperOrderClass Wrapper selector class name.
	 *      @type string $settings          Custom settings.
	 *      @type string $state             Attributes state.
	 *      @type string $mode              Style mode.
	 *      @type ModuleElements $elements  ModuleElements instance.
	 * }
	 */
	public static function module_styles($args)
	{
		$attrs    = $args['attrs'] ?? [];
		$elements = $args['elements'];
		$settings = $args['settings'] ?? [];

		$order_Class = $args['orderClass'];

		$imageContainerSelector = "{$order_Class} .dotm_step_flow_image_container";
		$imageSelector = "{$order_Class} .dotm_step_flow_image";
		$iconContainerSelector = "{$order_Class} .dotm_step_flow_icon_container";
		$buttonContainerSelector = "{$order_Class} .dotm_step_flow_button_container";
		$directionSelector = "{$order_Class} .dotm_step_flow_direction";
		$arrowSelector = "{$order_Class} .dotm_step_flow_direction::after";
		$maskSelector = "{$order_Class} .dotm_step_flow_image_mask";

		$directionWidth = $attrs['direction']['decoration']['width']['desktop']['value'] ?? '';
		$directionColor = $attrs['direction']['decoration']['color']['desktop']['value'] ?? '';
		$directionStyle = $attrs['direction']['decoration']['style']['desktop']['value'] ?? '';
		$directionTopOffset = $attrs['direction']['decoration']['top_offset']['desktop']['value'] ?? '';
		$directionLeftOffset = $attrs['direction']['decoration']['left_offset']['desktop']['value'] ?? '';
		$directionAngle = $attrs['direction']['decoration']['angle']['desktop']['value'] ?? '';
		$mask_width = $attrs['image']['advanced']['shape_width']['desktop']['value'] ?? '';
		$image_alignment = $attrs['image']['advanced']['image_alignment']['desktop']['value'] ?? '';

		Style::add(
			[
				'id'            => $args['id'],
				'name'          => $args['name'],
				'orderIndex'    => $args['orderIndex'],
				'storeInstance' => $args['storeInstance'],
				'styles'        => [
					// Module.
					$elements->style(
						[
							'attrName'   => 'module',
							'styleProps' => [
								'disabledOn'     => [
									'disabledModuleVisibility' => $settings['disabledModuleVisibility'] ?? null,
								],
								'advancedStyles' => [
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $imageContainerSelector,
											'attr'     => $attrs['image']['advanced']['image_alignment'] ?? [],
											'property' => "display: flex; justify-content: {$image_alignment};",
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $imageSelector,
											'attr'     => $attrs['image']['decoration']['image_width'] ?? [],
										]
									],
									[
										'componentName' => 'divi/text',
										'props'         => [
											'selector' => $buttonContainerSelector,
											'attr'     => $attrs['button']['advanced']['text'] ?? [],
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $maskSelector,
											'attr'     => $attrs['image']['advanced']['use_mask'] ?? [],
											'property' => "width: {$mask_width}; height: {$mask_width}; position: relative; overflow: hidden;",
										]
									]
								]
							],
						]
					),

					CommonStyle::style(
						[
							'selector' => $directionSelector,
							'attr'     => $attrs['direction']['advanced']['use'] ?? [],
							'property' => "width: {$directionWidth}; border-top-color: {$directionColor}; border-top-style: {$directionStyle}; top: {$directionTopOffset}; left: {$directionLeftOffset}; transform: rotate({$directionAngle});",
						]
					),

					CommonStyle::style(
						[
							'selector' => $arrowSelector,
							'attr'     => $attrs['direction']['advanced']['use'] ?? [],
							'property' => "border-top-color: {$directionColor}; border-right-color: {$directionColor}; border-top-style: {$directionStyle}; border-right-style: {$directionStyle};",
						]
					),

					// Icon.
					$elements->style(
						[
							'attrName'   => 'icon',
							'styleProps' => [
								'advancedStyles' => [
									[
										'componentName' => 'divi/common',
										'props'         => [
											'attr'                => $attrs['icon']['innerContent'] ?? [],
											'declarationFunction' => [StepFlow::class, 'icon_font_declaration'],
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'attr'     => $attrs['icon']['advanced']['color'] ?? [],
											'property' => 'color',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'attr'     => $attrs['icon']['advanced']['size'] ?? [],
											'property' => 'font-size',
										]
									],
									[
										'componentName' => 'divi/text',
										'props'         => [
											'selector' => $iconContainerSelector,
											'attr'     => $attrs['icon']['advanced']['text'] ?? [],
										]
									],
								]
							]
						]
					),

					// content_wrapper.
					$elements->style(
						[
							'attrName' => 'content_wrapper',
						]
					),

					// step.
					$elements->style(
						[
							'attrName' => 'step',
						]
					),

					// image.
					$elements->style(
						[
							'attrName' => 'image',
						]
					),

					// icon.
					$elements->style(
						[
							'attrName' => 'icon',
						]
					),

					// title.
					$elements->style(
						[
							'attrName' => 'title',
						]
					),

					// content.
					$elements->style(
						[
							'attrName' => 'content',
						]
					),

					// button.
					$elements->style(
						[
							'attrName' => 'button',
						]
					),

					// direction.
					$elements->style(
						[
							'attrName' => 'direction',
						]
					),


					/*
					 * We need to add CssStyle at the very bottom of other
					 * components so that custom css can override module styles
					 * till we find a more elegant solution.
					 */
					CssStyle::style(
						[
							'selector'  => $args['orderClass'],
							'attr'      => $attrs['css'] ?? [],
							'cssFields' => StepFlow::custom_css(),
						]
					),

					// ATTENTION: The code is intentionally added and commented in FE only as an example of expected value format.
					// If you have custom style processing, the style output should be passed as an `array` of style declarations
					// to the `styles` property of the `Style::add` method. For example:
					// [
					// 	[
					// 		'atRules'     => false,
					// 		'selector'    => "{$args['orderClass']} .example_static_module__content-container",
					// 		'declaration' => 'color: red;'
					// 	],
					// 	[
					// 		'atRules'     => '@media only screen and (max-width: 767px)',
					// 		'selector'    => "{$args['orderClass']} .example_static_module__content-container",
					// 		'declaration' => 'color: green;'
					// 	],
					// ],

					// The code below is an example of how to use the `CssStyle::style` method to generate CSS style.
					CssStyle::style(
						[
							'selector'  => $args['orderClass'],
							'attr'      => $attrs['css'] ?? [],
							'cssFields' => StepFlow::custom_css(),
						]
					),
				],
			]
		);
	}
}
