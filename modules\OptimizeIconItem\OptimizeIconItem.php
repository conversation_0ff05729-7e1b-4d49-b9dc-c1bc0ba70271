<?php

/**
 * Module: OptimizeIconItem class.
 *
 * @package MEE\Modules\OptimizeIconItem
 * @since ??
 */

namespace MEE\Modules\OptimizeIconItem;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

use ET\Builder\Framework\DependencyManagement\Interfaces\DependencyInterface;
use ET\Builder\Packages\ModuleLibrary\ModuleRegistration;

/**
 * `OptimizeIconItem` is consisted of functions used for Child Module such as Front-End rendering, REST API Endpoints etc.
 *
 * This is a dependency class and can be used as a dependency for `DependencyTree`.
 *
 * @since ??
 */
class OptimizeIconItem implements DependencyInterface
{
	use OptimizeIconItemTrait\RenderCallbackTrait;

	/**
	 * Loads `OptimizeIconItem` and registers Front-End render callback and REST API Endpoints.
	 *
	 * @since ??
	 *
	 * @return void
	 */
	public function load()
	{
		$module_json_folder_path = DIVIOPTIMIZER_MODULES_JSON_PATH . 'OptimizeIconItem/';

		add_action(
			'init',
			function () use ($module_json_folder_path) {
				ModuleRegistration::register_module(
					$module_json_folder_path,
					[
						'render_callback' => [OptimizeIconItem::class, 'render_callback'],
					]
				);
			}
		);
	}
}
