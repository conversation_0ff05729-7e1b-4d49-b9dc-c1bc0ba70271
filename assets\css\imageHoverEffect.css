
/* Hover Effects */

/* Effect 1: Zoom In */
.dotm_effect-1:hover img {
    transform: scale(1.2);
}

/* Effect 2: Rotate */
.dotm_effect-2:hover img {
    transform: rotate(15deg);
}

/* Effect 3: Grayscale */
.dotm_effect-3:hover img {
    filter: grayscale(100%);
}

/* Effect 4: Blur */
.dotm_effect-4:hover img {
    filter: blur(4px);
}

/* Effect 5: Brightness */
.dotm_effect-5:hover img {
    filter: brightness(1.5);
}

/* Effect 1: Zoom out */
.dotm_effect-6 img {
    transform: scale(1.2);
}

.dotm_effect-6:hover img {
    transform: scale(1);
}

/* Effect 7: Vertical Flip */
.dotm_effect-7:hover img {
    transform: scaleY(-1);
}

/* Effect 8: Horizontal Flip */
.dotm_effect-8:hover img {
    transform: scaleX(-1);
}


/* Effect 9: Opacity */
.dotm_effect-9:hover img {
    opacity: 0.5;
}

/* Effect 10: Slide Up */
.dotm_effect-10 img {
    transition: transform 0.5s ease;
}

.dotm_effect-10:hover img {
    transform: translateY(-20px);
}

/* Effect 12: Rotating Border */
.dotm_effect-11 .dotm_step_flow_image_mask {
    position: relative;
    display: inline-block;
}

.dotm_effect-11:hover .dotm_step_flow_image_mask::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 4px solid transparent;
    border-top: 4px solid #007bff;
    border-radius: 8px;
    animation: rotate-border 2s linear infinite;
    box-sizing: border-box;
}

@keyframes rotate-border {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

.dotm_effect-12:hover img {
    transform: scale(1.4) rotate(20deg);
}