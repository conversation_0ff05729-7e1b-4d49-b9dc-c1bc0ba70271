<?php

/**
 * Module: BreadcrumbsItem class.
 *
 * @package MEE\Modules\BreadcrumbsItem
 * @since ??
 */

namespace MEE\Modules\BreadcrumbsItem;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

use ET\Builder\Framework\DependencyManagement\Interfaces\DependencyInterface;
use ET\Builder\Packages\ModuleLibrary\ModuleRegistration;

/**
 * `BreadcrumbsItem` is consisted of functions used for BreadcrumbsItem such as Front-End rendering, REST API Endpoints etc.
 *
 * This is a dependency class and can be used as a dependency for `DependencyTree`.
 *
 * @since ??
 */
class BreadcrumbsItem implements DependencyInterface
{
	use BreadcrumbsItemTrait\RenderCallbackTrait;

	/**
	 * Loads `BreadcrumbsItem` and registers Front-End render callback and REST API Endpoints.
	 *
	 * @since ??
	 *
	 * @return void
	 */
	public function load()
	{
		$module_json_folder_path = DIVIOPTIMIZER_MODULES_JSON_PATH . 'BreadcrumbsItem/';

		add_action(
			'init',
			function () use ($module_json_folder_path) {
				ModuleRegistration::register_module(
					$module_json_folder_path,
					[
						'render_callback' => [BreadcrumbsItem::class, 'render_callback'],
					]
				);
			}
		);
	}
}
