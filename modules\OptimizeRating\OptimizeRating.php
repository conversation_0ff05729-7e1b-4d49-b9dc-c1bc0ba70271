<?php

/**
 * Module: OptimizeRating class.
 *
 * @package MEE\Modules\OptimizeRating
 * @since ??
 */

namespace MEE\Modules\OptimizeRating;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

use ET\Builder\Framework\DependencyManagement\Interfaces\DependencyInterface;
use ET\Builder\Packages\ModuleLibrary\ModuleRegistration;


/**
 * `OptimizeRating` is consisted of functions used for OptimizeRating such as Front-End rendering, REST API Endpoints etc.
 *
 * This is a dependency class and can be used as a dependency for `DependencyTree`.
 *
 * @since ??
 */
class OptimizeRating implements DependencyInterface
{
	use OptimizeRatingTrait\RenderCallbackTrait;
	use OptimizeRatingTrait\ModuleClassnamesTrait;
	use OptimizeRatingTrait\ModuleStylesTrait;
	use OptimizeRatingTrait\ModuleScriptDataTrait;

	/**
	 * Loads `OptimizeRating` and registers Front-End render callback and REST API Endpoints.
	 *
	 * @since ??
	 *
	 * @return void
	 */
	public function load()
	{
		$module_json_folder_path = DIVIOPTIMIZER_MODULES_JSON_PATH . 'OptimizeRating/';

		add_action(
			'init',
			function () use ($module_json_folder_path) {
				ModuleRegistration::register_module(
					$module_json_folder_path,
					[
						'render_callback' => [OptimizeRating::class, 'render_callback'],
					]
				);
			}
		);
	}
}
