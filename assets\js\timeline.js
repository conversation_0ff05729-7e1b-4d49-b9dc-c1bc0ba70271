// const TimelineEffect = () => {

    // document.addEventListener('DOMContentLoaded', function () {
        const timelineItems = document.querySelectorAll('.dotm_optimize_timeline_item');
        const timeline = document.querySelector('.dotm_optimize_timeline_wrapper');

        // Function to check if element is in viewport or partially visible
        function isElementInViewport(el) {
            const rect = el.getBoundingClientRect();
            const windowHeight = window.innerHeight || document.documentElement.clientHeight;
            const windowWidth = window.innerWidth || document.documentElement.clientWidth;

            // Check if element is partially visible in the viewport
            return (
                // Element is at least partially visible vertically
                (rect.top <= windowHeight && rect.bottom >= 0) &&
                // Element is at least partially visible horizontally
                (rect.left <= windowWidth && rect.right >= 0)
            );
        }

        // Function to add visible class to timeline items in viewport
        function handleScroll() {
            // Handle animation of timeline items
            timelineItems.forEach(function (item) {
                if (isElementInViewport(item)) {
                    // Make item visible with a slight delay for a nice animation effect
                    setTimeout(() => {
                        item.style.opacity = '1';
                        item.style.transform = 'translateY(0)';
                    }, 50);
                }
            });

            // Update progress bar
            updateProgressBar();
        }

        // Function to update progress bar based on scroll position
        function updateProgressBar() {
            const timelineHeight = timeline.offsetHeight;
            const timelineTop = timeline.getBoundingClientRect().top;
            const scrollPosition = window.scrollY;
            const windowHeight = window.innerHeight;

            // Calculate how much of the timeline is scrolled
            let scrollPercentage = 0;

            if (timelineTop < windowHeight) {
                // Timeline is in view
                const visibleHeight = Math.min(timelineHeight, windowHeight - timelineTop);
                const totalScrollableDistance = timelineHeight - windowHeight + timelineTop + 100; // Add some padding

                if (totalScrollableDistance > 0) {
                    // Calculate scroll percentage based on visible height and scroll position
                    scrollPercentage = Math.min(100, Math.max(0, (scrollPosition / totalScrollableDistance) * 100));

                    // Adjust scroll percentage based on visible portion
                    if (visibleHeight < timelineHeight) {
                        // Only part of the timeline is visible
                        const visibilityRatio = visibleHeight / timelineHeight;
                        // Ensure progress is proportional to the visible part
                        scrollPercentage = Math.min(scrollPercentage, visibilityRatio * 100);
                    }
                }
            }

            // Update progress bar height
            timeline.style.setProperty('--scroll-progress', scrollPercentage + '%');
        }

        // Set initial styles for timeline items
        timelineItems.forEach(function (item) {
            item.style.opacity = '0';
            item.style.transform = 'translateY(20px)';
            item.style.transition = 'all 0.5s ease-in-out';
        });

        // Add CSS variable for progress
        timeline.style.setProperty('--scroll-progress', '0%');

        // Listen for scroll and load events
        window.addEventListener('load', handleScroll);
        window.addEventListener('scroll', handleScroll);
        window.addEventListener('resize', handleScroll);

        // Initial call - run immediately and then again after a short delay
        // to ensure items are visible regardless of layout type
        handleScroll();

        // Run again after a short delay to catch any items that might not have been properly positioned yet
        setTimeout(handleScroll, 100);
    // });

// }

