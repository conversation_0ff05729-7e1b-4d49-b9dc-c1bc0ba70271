function animateCounter(props={}) {
    const { elementId, target, duration } = props;
    const element = document.getElementById(elementId);

    if (!element) return;

    // Find the counter wrapper (parent element)
    const counterWrapper = element.closest('.dotm_optimizer_counter_wrapper');
    if (!counterWrapper) return;

    // Function to start the counter animation
    function startCounterAnimation() {
        let start = 0;
        const increment = Math.ceil(target / (duration / 16));
        let timer;

        function updateCounter() {
            start += increment;
            if (start > target) {
                start = target;
                clearInterval(timer);
            }
            if (element.textContent !== undefined) {
                element.textContent = start.toString();
            }
        }

        // Add class to trigger CSS animations
        counterWrapper.classList.add('in-viewport');

        // Start the counter
        timer = setInterval(updateCounter, 16);
        return timer;
    }

    // Set up the Intersection Observer
    const observerOptions = {
        threshold: 0.3, // Trigger when 30% of the element is visible
        rootMargin: '0px'
    };

    let timer;

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                // Start animation when element enters viewport
                timer = startCounterAnimation();

                // Stop observing once animation has started
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);

    // Start observing the counter wrapper
    observer.observe(counterWrapper);

    // Return cleanup function
    return () => {
        if (timer) clearInterval(timer);
        observer.disconnect();
    };
}