<?php

/**
 * OptimizeImageReveal::render_callback()
 *
 * @package MEE\Modules\OptimizeImageReveal
 * @since ??
 */

namespace MEE\Modules\OptimizeImageReveal\OptimizeImageRevealTrait;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

// phpcs:disable ET.Sniffs.ValidVariableName.UsedPropertyNotSnakeCase -- WP use snakeCase in \WP_Block_Parser_Block

use ET\Builder\Packages\Module\Module;
use ET\Builder\Framework\Utility\HTMLUtility;
use ET\Builder\FrontEnd\BlockParser\BlockParserStore;
use ET\Builder\Packages\Module\Options\Element\ElementComponents;
use MEE\Modules\OptimizeImageReveal\OptimizeImageReveal;

trait RenderCallbackTrait
{

	/**
	 * Static module render callback which outputs server side rendered HTML on the Front-End.
	 *
	 * @since ??
	 * @param array          $attrs    Block attributes that were saved by VB.
	 * @param string         $content  Block content.
	 * @param WP_Block       $block    Parsed block object that being rendered.
	 * @param ModuleElements $elements ModuleElements instance.
	 *
	 * @return string HTML rendered of Static module.
	 */
	public static function render_callback($attrs, $content, $block, $elements)
	{

		$reveal_direction = $attrs['image']['advanced']['revealDirection']['desktop']['value'] ?? '';
		$animationDelay = $attrs['image']['advanced']['animationDelay']['desktop']['value'] ?? '';

		$props = [
            'animationDelay' => $animationDelay,
        ];

		// image.
		$image = $elements->render(
			[
				'attrName' => 'image',
			]
		);

		// Image container.
		$image_container = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'dotm_optimize_image_reveal_img_container',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $image,
			]
		);




		// image_reveal_container.
		$image_reveal_container = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => "dotm_optimize_image_reveal_img_reveal dotm_optimize_image_reveal_{$reveal_direction}",
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => '',
			]
		);

		$image_reveal_overlay = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'dotm_optimize_image_reveal_overlay',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => '',
			]
		);

		$image_reveal_main = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'dotm_optimize_image_reveal_container',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $image_container . $image_reveal_container . $image_reveal_overlay,
			]
		);

		$parent       = BlockParserStore::get_parent($block->parsed_block['id'], $block->parsed_block['storeInstance']);
		$parent_attrs = $parent->attrs ?? [];

		return Module::render(
			[
				// FE only.
				'orderIndex'          => $block->parsed_block['orderIndex'],
				'storeInstance'       => $block->parsed_block['storeInstance'],

				// VB equivalent.
				'attrs'               => $attrs,
				'elements'            => $elements,
				'id'                  => $block->parsed_block['id'],
				'name'                => $block->block_type->name,
				'moduleCategory'      => $block->block_type->category,
				'classnamesFunction'  => [OptimizeImageReveal::class, 'module_classnames'],
				'stylesComponent'     => [OptimizeImageReveal::class, 'module_styles'],
				'scriptDataComponent' => [OptimizeImageReveal::class, 'module_script_data'],
				'parentAttrs'         => $parent_attrs,
				'parentId'            => $parent->id ?? '',
				'parentName'          => $parent->blockName ?? '',
				'children'            => [
					ElementComponents::component(
						[
							'attrs'         => $attrs['module']['decoration'] ?? [],
							'id'            => $block->parsed_block['id'],

							// FE only.
							'orderIndex'    => $block->parsed_block['orderIndex'],
							'storeInstance' => $block->parsed_block['storeInstance'],
						]
					),
					HTMLUtility::render(
						[
							'tag'               => 'div',
							'attributes'        => [
								'class' => 'dotm_optimize_image_reveal_main',
							],
							'childrenSanitizer' => 'et_core_esc_previously',
							'children'          => $image_reveal_main,
						]
					),
					HTMLUtility::render(
                        [
                            'tag' => 'script',
                            'attributes' => [],
                            'childrenSanitizer' => 'et_core_esc_previously',
                            'children' => "document.addEventListener('DOMContentLoaded', function() {
                                const props = " . json_encode($props) . ";
                                revealImage(props);
                            });",
                        ]
                    ),
				],
			]
		);
	}
}
