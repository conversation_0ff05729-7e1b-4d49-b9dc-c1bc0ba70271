<?php
/**
 * PriceListItem::render_callback()
 *
 * @package MEE\Modules\PriceListItem
 * @since ??
 */

namespace MEE\Modules\PriceListItem\PriceListItemTrait;

if ( ! defined( 'ABSPATH' ) ) {
	die( 'Direct access forbidden.' );
}

// phpcs:disable ET.Sniffs.ValidVariableName.UsedPropertyNotSnakeCase -- WP use snakeCase in \WP_Block_Parser_Block

use ET\Builder\Packages\Module\Module;
use ET\Builder\Framework\Utility\HTMLUtility;
use ET\Builder\FrontEnd\BlockParser\BlockParserStore;
use ET\Builder\Packages\IconLibrary\IconFont\Utils;
use ET\Builder\Packages\Module\Options\Element\ElementComponents;
use ET\Builder\Packages\ModuleLibrary\ModuleRegistration;
use MEE\Modules\PriceListItem\PriceListItem;

trait RenderCallbackTrait {
	use ModuleClassnamesTrait;
	use ModuleStylesTrait;

	/**
	 * PriceListItem render callback which outputs server side rendered HTML on the Front-End.
	 *
	 * @since ??
	 *
	 * @param array          $attrs Block attributes that were saved by VB.
	 * @param string         $content          Block content.
	 * @param WP_Block       $block            Parsed block object that being rendered.
	 * @param ModuleElements $elements         ModuleElements instance.
	 *
	 * @return string HTML rendered of PriceListItem.
	 */
	public static function render_callback( $attrs, $content, $block, $elements ) {
		$parent = BlockParserStore::get_parent( $block->parsed_block['id'], $block->parsed_block['storeInstance'] );

		$parent_attrs = $parent->attrs ?? [];

		$parent_default_attributes = ModuleRegistration::get_default_attrs( 'optimizer/dotm-price-list' );
		$parent_attrs_with_default = array_replace_recursive( $parent_default_attributes, $parent_attrs );

		$show_image = $attrs['image']['advanced']['use']['desktop']['value'] ?? '';
		$show_icon = $attrs['icon']['advanced']['use']['desktop']['value'] ?? '';
		$icon = $attrs['icon']['innerContent']['desktop']['value'] ?? [];

		

		// image.
		$image = $elements->render(
			[
				'attrName'      => 'image',
			]
		);

		// image container.
		$image_container = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'dotm_price_list_item_image_container',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $image,
			]
		);

		// icon.
		$icon = HTMLUtility::render(
			[
				'tag'               => 'span',
				'attributes'        => [
					'class' => 'dotm_price_list_item_icon',
				],
				'childrenSanitizer' => 'esc_html',
				'children'          => Utils::process_font_icon($icon),
				
			]
		);

		// icon container.
		$icon_container = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'dotm_price_list_item_icon_container',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $icon,
			]
		);

		// Title.
		$title = $elements->render(
			[
				'attrName'      => 'title',
			]
		);

		// separator.
		$separator = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'dotm_price_list_item_separator',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => '',
			]
		);

		// price_tag.
		$price_tag = $elements->render(
			[
				'attrName'      => 'price_tag',
			]
		);

		// wrapper container.
		$wrapper_container = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'dotm_price_list_item_wrapper',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $title . $separator . $price_tag,
			]
		);

		// content.
		$content = $elements->render(
			[
				'attrName'      => 'content',
			]
		);

		// description container.
		$description_container = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'dotm_price_list_item_content',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $content,
			]
		);

		// content container.
		$content_container = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'dotm_price_list_item_content-container',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $wrapper_container . $description_container,
			]
		);

		// main container.
		$main_container = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'dotm_price_list_item_container',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => ($show_image === 'on' ? $image_container : '') . ($show_icon === 'on' ? $icon_container : '') . $content_container,
			]
		);

		return Module::render(
			[
				// FE only.
				'orderIndex'         => $block->parsed_block['orderIndex'],
				'storeInstance'      => $block->parsed_block['storeInstance'],

				// VB equivalent.
				'id'                 => $block->parsed_block['id'],
				'name'               => $block->block_type->name,
				'moduleCategory'     => $block->block_type->category,
				'attrs'              => $attrs,
				'elements'           => $elements,
				'classnamesFunction' => [ PriceListItem::class, 'module_classnames' ],
				'stylesComponent'    => [ PriceListItem::class, 'module_styles' ],
				'parentAttrs'        => $parent_attrs,
				'parentId'           => $parent->id ?? '',
				'parentName'         => $parent->blockName ?? '',
				'tag'                => 'li',
				'children'           => ElementComponents::component(
					[
						'attrs'         => $attrs['module']['decoration'] ?? [],
						'id'            => $block->parsed_block['id'],

						// FE only.
						'orderIndex'    => $block->parsed_block['orderIndex'],
						'storeInstance' => $block->parsed_block['storeInstance'],
					]
				) . $main_container,
			]
		);
	}
}
