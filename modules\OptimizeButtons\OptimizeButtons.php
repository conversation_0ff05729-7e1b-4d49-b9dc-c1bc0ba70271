<?php

/**
 * Module: OptimizeButtons class.
 *
 * @package MEE\Modules\OptimizeButtons
 * @since ??
 */

namespace MEE\Modules\OptimizeButtons;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}


use ET\Builder\Framework\DependencyManagement\Interfaces\DependencyInterface;
use ET\Builder\Packages\ModuleLibrary\ModuleRegistration;

/**
 * `OptimizeButtons` is consisted of functions used for OptimizeButtons such as Front-End rendering, REST API Endpoints etc.
 *
 * This is a dependency class and can be used as a dependency for `DependencyTree`.
 *
 * @since ??
 */
class OptimizeButtons implements DependencyInterface
{
	use OptimizeButtonsTrait\RenderCallbackTrait;

	/**
	 * Loads `OptimizeButtons` and registers Front-End render callback and REST API Endpoints.
	 *
	 * @since ??
	 *
	 * @return void
	 */
	public function load()
	{
		$module_json_folder_path = DIVIOPTIMIZER_MODULES_JSON_PATH . 'OptimizeButtons/';

		add_action(
			'init',
			function () use ($module_json_folder_path) {
				ModuleRegistration::register_module(
					$module_json_folder_path,
					[
						'render_callback' => [OptimizeButtons::class, 'render_callback'],
					]
				);
			}
		);
	}
}
