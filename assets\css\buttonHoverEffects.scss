// Scale effect
.dotm_button_hover_effects_scale:hover {
    transform: scale(1.1);
}

// push effect
.dotm_button_hover_effects_push:hover {
    transform: perspective(1px) translateZ(0) translateY(-2px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
}

// pulse effect
.dotm_button_hover_effects_pulse:hover {
    animation: dotm_button_hover_effects_pulse 1s infinite;
}

@keyframes dotm_button_hover_effects_pulse {
    0% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.05);
    }

    100% {
        transform: scale(1);
    }
}

/* Horizontal buzz effect */
.dotm_button_hover_effects_buzz_horizontal:hover {
    animation: dotm_button_hover_effects_buzz_horizontal 1s linear infinite;
}

@keyframes dotm_button_hover_effects_buzz_horizontal {

    0%,
    100% {
        transform: translateX(0);
    }

    10%,
    30%,
    50%,
    70%,
    90% {
        transform: translateX(-2px);
    }

    20%,
    40%,
    60%,
    80% {
        transform: translateX(2px);
    }
}

/* Vertical buzz animation */
.dotm_button_hover_effects_buzz_vertical:hover {
    animation: dotm_button_hover_effects_buzz_vertical 1s linear infinite;
}

@keyframes dotm_button_hover_effects_buzz_vertical {

    0%,
    100% {
        transform: translateY(0);
    }

    10%,
    30%,
    50%,
    70%,
    90% {
        transform: translateY(-2px);
    }

    20%,
    40%,
    60%,
    80% {
        transform: translateY(2px);
    }
}

/* Rotational buzz animation */
.dotm_button_hover_effects_rotate:hover {
    animation: dotm_button_hover_effects_rotate 1s linear infinite;
}

@keyframes dotm_button_hover_effects_rotate {

    0%,
    100% {
        transform: rotate(0);
    }

    10%,
    30%,
    50%,
    70%,
    90% {
        transform: rotate(-2deg);
    }

    20%,
    40%,
    60%,
    80% {
        transform: rotate(2deg);
    }
}

/* tada animation */
.dotm_button_hover_effects_tada:hover {
    animation: dotm_button_hover_effects_tada 1.5s ease 1;
}

@keyframes dotm_button_hover_effects_tada {
    0% {
        transform: scale(1);
    }

    10%,
    20% {
        transform: scale(0.9) rotate(-3deg);
    }

    30%,
    50%,
    70%,
    90% {
        transform: scale(1.1) rotate(3deg);
    }

    40%,
    60%,
    80% {
        transform: scale(1.1) rotate(-3deg);
    }

    100% {
        transform: scale(1) rotate(0);
    }
}

/* wobble animation */
.dotm_button_hover_effects_wobble:hover {
    animation: dotm_button_hover_effects_wobble 2s ease 1;
}

@keyframes dotm_button_hover_effects_wobble {
    0% {
        transform: translateX(0%);
    }

    15% {
        transform: translateX(-15%) rotate(-5deg);
    }

    30% {
        transform: translateX(12%) rotate(3deg);
    }

    45% {
        transform: translateX(-9%) rotate(-3deg);
    }

    60% {
        transform: translateX(6%) rotate(2deg);
    }

    75% {
        transform: translateX(-3%) rotate(-1deg);
    }

    100% {
        transform: translateX(0%);
    }
}

/* jello animation */
.dotm_button_hover_effects_jello:hover {
    animation: dotm_button_hover_effects_jello 2s ease 1;
}

@keyframes dotm_button_hover_effects_jello {

    0%,
    11.1%,
    100% {
        transform: none;
    }

    22.2% {
        transform: skewX(-12.5deg) skewY(-12.5deg);
    }

    33.3% {
        transform: skewX(6.25deg) skewY(6.25deg);
    }

    44.4% {
        transform: skewX(-3.125deg) skewY(-3.125deg);
    }

    55.5% {
        transform: skewX(1.5625deg) skewY(1.5625deg);
    }

    66.6% {
        transform: skewX(-0.78125deg) skewY(-0.78125deg);
    }

    77.7% {
        transform: skewX(0.390625deg) skewY(0.390625deg);
    }

    88.8% {
        transform: skewX(-0.1953125deg) skewY(-0.1953125deg);
    }
}

.dotm_button_hover_effects_bounce:hover {
    animation: dotm_button_hover_effects_bounce 1.5s ease infinite;
}

@keyframes dotm_button_hover_effects_bounce {

    0%,
    20%,
    50%,
    80%,
    100% {
        transform: translateY(0);
    }

    40% {
        transform: translateY(-15px);
    }

    60% {
        transform: translateY(-7px);
    }
}

.dotm_button_hover_effects_wobble_bottom_right:hover {
    animation: dotm-wobble-bottom-right 0.6s ease-in-out;
}

@keyframes dotm-wobble-bottom-right {
    0% {
        transform: translate(0, 0);
    }

    25% {
        transform: translate(3px, 3px);
    }

    50% {
        transform: translate(-3px, 6px);
    }

    75% {
        transform: translate(4px, 4px);
    }

    100% {
        transform: translate(0, 0);
    }
}

.dotm_button_hover_effects_wobble_top_right:hover {
    animation: dotm-wobble-top-right 0.6s ease-in-out;
}

@keyframes dotm-wobble-top-right {
    0% {
        transform: translate(0, 0);
    }

    25% {
        transform: translate(3px, -3px);
    }

    50% {
        transform: translate(-3px, -6px);
    }

    75% {
        transform: translate(4px, -4px);
    }

    100% {
        transform: translate(0, 0);
    }
}

.dotm_button_hover_effects_wobble_top {
    display: inline-block;
    -webkit-transform: perspective(1px) translateZ(0);
    transform: perspective(1px) translateZ(0);
    -webkit-transform-origin: 0 100%;
    transform-origin: 0 100%
}

.dotm_button_hover_effects_wobble_top:hover {
    animation: dotm-wobble-top 0.6s ease-in-out;
}

@keyframes dotm-wobble-top {
    0% {
        transform: rotate(0deg);
    }

    17% {
        transform: skew(-12deg);
    }

    34% {
        transform: skew(10deg);
    }

    50% {
        transform: skew(-6deg);
    }

    67% {
        transform: skew(4deg);
    }

    84% {
        transform: skew(-2deg);
    }

    to {
        transform: skew(0)
    }
}


@keyframes dotm-wobble-bottom {
    17% {
        transform: skew(-12deg)
    }

    34% {
        transform: skew(10deg)
    }

    50% {
        transform: skew(-6deg)
    }

    67% {
        transform: skew(4deg)
    }

    84% {
        transform: skew(-2deg)
    }

    to {
        transform: skew(0)
    }
}

.dotm_button_hover_effects_wobble_bottom {
    display: inline-block;
    -webkit-transform: perspective(1px) translateZ(0);
    transform: perspective(1px) translateZ(0);
    -webkit-transform-origin: 100% 0;
    transform-origin: 100% 0
}

.dotm_button_hover_effects_wobble_bottom:hover {
    animation: dotm-wobble-bottom 0.6s ease-in-out;
}

@keyframes dotm-wobble-skew {
    17% {
        transform: skew(-12deg)
    }

    34% {
        transform: skew(10deg)
    }

    50% {
        transform: skew(-6deg)
    }

    67% {
        transform: skew(4deg)
    }

    84% {
        transform: skew(-2deg)
    }

    to {
        transform: skew(0)
    }
}

.dotm_button_hover_effects_wobble_skew {
    display: inline-block;
    -webkit-transform: perspective(1px) translateZ(0);
    transform: perspective(1px) translateZ(0);
}

.dotm_button_hover_effects_wobble_skew:hover {
    animation: dotm-wobble-skew 0.6s ease-in-out;
}

.dotm_button_hover_effects_wobble_forward:hover {
    transform: translateX(8px)
}

.dotm_button_hover_effects_wobble_backward:hover {
    transform: translateX(-8px)
}