<?php

/**
 * ChildModule::render_callback()
 *
 * @package MEE\Modules\ChildModule
 * @since ??
 */

namespace MEE\Modules\ProfileOptimaizer\ProfileOptimaizerTrait;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

// phpcs:disable ET.Sniffs.ValidVariableName.UsedPropertyNotSnakeCase -- WP use snakeCase in \WP_Block_Parser_Block

use ET\Builder\FrontEnd\BlockParser\BlockParserStore;
use ET\Builder\Packages\Module\Module;
use ET\Builder\Packages\Module\Options\Element\ElementComponents;
use MEE\Modules\ProfileOptimaizer\ProfileOptimaizer;
use ET\Builder\Framework\Utility\HTMLUtility;

trait RenderCallbackTrait
{
	use ModuleClassnamesTrait;
	use ModuleStylesTrait;
	use ModuleScriptDataTrait;
	/**
	 * Parent module render callback which outputs server side rendered HTML on the Front-End.
	 *
	 * @since ??
	 *
	 * @param array          $attrs Block attributes that were saved by VB.
	 * @param string         $content          Block content.
	 * @param \WP_Block      $block            Parsed block object that being rendered.
	 * @param ModuleElements $elements         ModuleElements instance.
	 *
	 * @return string HTML rendered of Parent module.
	 */
	public static function render_callback($attrs, $content, $block, $elements)
	{
		$children_ids = $block->parsed_block['innerBlocks'] ? array_map(
			function ($inner_block) {
				return $inner_block['id'];
			},
			$block->parsed_block['innerBlocks']
		) : [];

		$parent       = BlockParserStore::get_parent($block->parsed_block['id'], $block->parsed_block['storeInstance']);
		$parent_attrs = $parent->attrs ?? [];
		
		
		$imageLeftPosition = isset($attrs['imageLeftPosition']['innerContent']['desktop']['value']) ? $attrs['imageLeftPosition']['innerContent']['desktop']['value'] : 'left-top';
		$imageTopPosition = isset($attrs['imageTopPosition']['innerContent']['desktop']['value']) ? $attrs['imageTopPosition']['innerContent']['desktop']['value'] : 'top-left';
		$imagePosition = isset($attrs['imagePosition']['innerContent']['desktop']['value']) ? $attrs['imagePosition']['innerContent']['desktop']['value'] : 'person-top';
		$imageHover = isset($attrs['image']['decoration']['scaleType']['desktop']['value']) ? $attrs['image']['decoration']['scaleType']['desktop']['value'] : '';
		$socialItemAlign = isset($attrs['socialItemAlign']['innerContent']['desktop']['value']) ? $attrs['socialItemAlign']['innerContent']['desktop']['value'] : '';
		$layout = $attrs['layout_style']['innerContent']['desktop']['value'] ?? '';
		$personTop = "";
		$personLeft = "";
		if ("person-left" === $imagePosition) {
			$personLeft = $imageLeftPosition;
		} else {
			$personTop = $imageTopPosition;
		}

		$social_item_alignment = $socialItemAlign
			? "dotm-profile-social-" . $socialItemAlign
			: "";





		$image = $elements->render(
			[
				'attrName' => 'image',
			]
		);

		$overlay = HTMLUtility::render(
			[
				'tag' => 'div',
				'attributes' => [
					'class' => 'dotm-profile-overlay',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
			]
		);
		$imgContainerInner = HTMLUtility::render(
			[
				'tag' => 'div',
				'attributes' => [
					'class' => 'dotm-profile-img-inner',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children' => $image . $overlay,
			]
		);

		$imgContainer = HTMLUtility::render(
			[
				'tag' => 'div',
				'attributes' => [
					'class' => "dotm-profile-img dotm-profile-{$personTop} dotm-profile-{$imageHover} ",
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children' => $imgContainerInner,
			]
		);

		$profile_name = $elements->render(
			[
				'attrName' => 'name',

			]
		);
		$profile_role = $elements->render(
			[
				'attrName' => 'role',
			]
		);

		$profile_description = $elements->render(
			[
				'attrName' => 'description',
			]
		);

		$network_items = HTMLUtility::render(
			[
				'tag'               => 'ul',
				'attributes'        => [
					'class' => "dotm-profile-social {$social_item_alignment}",
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $content,
			]
		);

		$contentContainer = HTMLUtility::render(
			[
				'tag' => 'div',
				'attributes' => [
					'class' => "dotm-profile-des dotm-profile-des-{$layout}",
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children' => $profile_name . $profile_role . $profile_description . $network_items,
			]
		);

		$panrentContainer = HTMLUtility::render(
			[
				'tag' => 'div',
				'attributes' => [
					'class' => "dotm-profile-wrap dotm-profile-{$personLeft}",
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children' => $imgContainer . $contentContainer,
			]
		);




		return Module::render(
			[
				// FE only.
				'orderIndex'          => $block->parsed_block['orderIndex'],
				'storeInstance'       => $block->parsed_block['storeInstance'],

				// VB equivalent.
				'id'                  => $block->parsed_block['id'],
				'name'                => $block->block_type->name,
				'moduleCategory'      => $block->block_type->category,
				'attrs'               => $attrs,
				'elements'            => $elements,
				'classnamesFunction'  => [ProfileOptimaizer::class, 'module_classnames'],
				'scriptDataComponent' => [ProfileOptimaizer::class, 'module_script_data'],
				'stylesComponent'     => [ProfileOptimaizer::class, 'module_styles'],
				'parentAttrs'         => $parent_attrs,
				'parentId'            => $parent->id ?? '',
				'parentName'          => $parent->blockName ?? '',
				'children'            => ElementComponents::component(
					[
						'attrs'         => $attrs['module']['decoration'] ?? [],
						'id'            => $block->parsed_block['id'],

						// FE only.
						'orderIndex'    => $block->parsed_block['orderIndex'],
						'storeInstance' => $block->parsed_block['storeInstance'],
					]
				)  . $panrentContainer,
				'childrenIds'         => $children_ids,
			]
		);
	}
}
