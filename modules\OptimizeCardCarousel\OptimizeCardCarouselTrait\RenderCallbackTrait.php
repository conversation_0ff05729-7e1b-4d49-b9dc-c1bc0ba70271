<?php
/**
 * ChildModule::render_callback()
 *
 * @package MEE\Modules\ChildModule
 * @since ??
 */

namespace MEE\Modules\OptimizeCardCarousel\OptimizeCardCarouselTrait;

if ( ! defined( 'ABSPATH' ) ) {
	die( 'Direct access forbidden.' );
}

// phpcs:disable ET.Sniffs.ValidVariableName.UsedPropertyNotSnakeCase -- WP use snakeCase in \WP_Block_Parser_Block

use ET\Builder\FrontEnd\BlockParser\BlockParserStore;
use ET\Builder\Packages\Module\Module;
use ET\Builder\Packages\Module\Options\Element\ElementComponents;
use MEE\Modules\OptimizeCardCarousel\OptimizeCardCarousel;
use ET\Builder\Framework\Utility\HTMLUtility;

trait RenderCallbackTrait {
	use ModuleClassnamesTrait;
	use ModuleStylesTrait;
	use ModuleScriptDataTrait;
	/**
	 * OptimizeCardCarousel render callback which outputs server side rendered HTML on the Front-End.
	 *
	 * @since ??
	 *
	 * @param array          $attrs Block attributes that were saved by VB.
	 * @param string         $content          Block content.
	 * @param \WP_Block      $block            Parsed block object that being rendered.
	 * @param ModuleElements $elements         ModuleElements instance.
	 *
	 * @return string HTML rendered of Parent module.
	 */
	public static function render_callback( $attrs, $content, $block, $elements ) {
		$children_ids = $block->parsed_block['innerBlocks'] ? array_map(
			function( $inner_block ) {
				return $inner_block['id'];
			},
			$block->parsed_block['innerBlocks']
		) : [];

		$parent       = BlockParserStore::get_parent( $block->parsed_block['id'], $block->parsed_block['storeInstance'] );
		$parent_attrs = $parent->attrs ?? [];

		$use_autoPlay = $attrs['carousel']['advanced']['use_autoPlay']['desktop']['value'] ?? '';
		$autoPlay = $use_autoPlay === 'on';
		$slide_to_show = $attrs['carousel']['advanced']['slide_to_show']['desktop']['value'] ?? '';
		$use_fade = $attrs['carousel']['advanced']['use_fade']['desktop']['value'] ?? '';
		$isFade = $use_fade === 'on';
		$slide_to_scroll = intval($attrs['carousel']['advanced']['slide_to_scroll']['desktop']['value'] ?? '');
		$pauseOnHover = $attrs['carousel']['advanced']['pauseOnHover']['desktop']['value'] ?? '';
		$isPause = $pauseOnHover === 'on';
		$autoPlay_speed = $attrs['carousel']['advanced']['autoPlay_speed']['desktop']['value'] ?? '';
		$slide_speed = $use_autoPlay === 'on' ? ($attrs['carousel']['advanced']['slide_speed']['desktop']['value'] ?? '') : 500;
		$show_arrows = $attrs['arrows']['advanced']['show_arrows']['desktop']['value'] ?? '';
		$isArrows = $show_arrows === 'on';
		$show_dots = $attrs['dots']['advanced']['show_dots']['desktop']['value'] ?? '';
		$isDots = $show_dots === 'on';
		$use_centerMode = $attrs['carousel']['advanced']['centerMode']['desktop']['value'] ?? '';
		$centerMode = $use_centerMode === 'on';
		$layouts = $attrs['carousel']['advanced']['centerLayout']['desktop']['value'] ?? '';
		$design_class = $use_centerMode === 'on' ? "dotm_card_carousel_{$layouts}" : '';


		// card slider options
        $options = [
            'centerMode' => $centerMode,
            'centerPadding' => "10%",
            'infinite' => true,
            'pauseOnFocus' => false,
            'slidesToShow' => $slide_to_show,
            'autoplay' => $autoPlay,
            'autoplaySpeed' => $autoPlay_speed,
            'dots' => $isDots,
            'arrows' => $isArrows,
            'slidesToScroll' => (int)$slide_to_scroll,
            'fade' => $isFade,
            'pauseOnHover' => $isPause,
            'speed' => $slide_speed,
            'vertical' => false,
            'responsive' => $centerMode ? [
                [
                    'breakpoint' => 9999,
                    'settings' => [
                        'centerPadding' => count($children_ids) >= 5 ? '15%' : '0px',
                        'slidesToShow' => count($children_ids) >= 5 ? 3 : $slide_to_show
                    ]
                ],
                [
                    'breakpoint' => 768,
                    'settings' => [
                        'centerPadding' => '10%',
                        'slidesToShow' => 1
                    ]
                ]
            ] : null
        ];

		$container = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => "center slider $design_class",
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $content,
			]
		);

		return Module::render(
			[
				// FE only.
				'orderIndex'          => $block->parsed_block['orderIndex'],
				'storeInstance'       => $block->parsed_block['storeInstance'],

				// VB equivalent.
				'id'                  => $block->parsed_block['id'],
				'name'                => $block->block_type->name,
				'moduleCategory'      => $block->block_type->category,
				'attrs'               => $attrs,
				'elements'            => $elements,
				'classnamesFunction'  => [ OptimizeCardCarousel::class, 'module_classnames' ],
				'scriptDataComponent' => [ OptimizeCardCarousel::class, 'module_script_data' ],
				'stylesComponent'     => [ OptimizeCardCarousel::class, 'module_styles' ],
				'parentAttrs'         => $parent_attrs,
				'parentId'            => $parent->id ?? '',
				'parentName'          => $parent->blockName ?? '',
				'children'            => [
					ElementComponents::component(
						[
							'attrs'         => $attrs['module']['decoration'] ?? [],
							'id'            => $block->parsed_block['id'],
	
							// FE only.
							'orderIndex'    => $block->parsed_block['orderIndex'],
							'storeInstance' => $block->parsed_block['storeInstance'],
						]
					) . $container,
					'childrenIds'         => $children_ids,

					HTMLUtility::render(
                        [
                            'tag' => 'script',
                            'attributes' => [],
                            'childrenSanitizer' => 'et_core_esc_previously',
                            'children' => "document.addEventListener('DOMContentLoaded', function() {
                                const option = " . json_encode($options) . ";
                                $('.center').slick(option);
                            });",
                        ]
                    ),
				]
			]
		);
	}
}
