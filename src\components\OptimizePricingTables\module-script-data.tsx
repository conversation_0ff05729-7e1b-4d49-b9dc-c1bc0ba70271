import React, {
  Fragment,
  ReactElement,
} from 'react';

import {
  ModuleScriptDataProps,
} from '@divi/module';
import { OptimizePricingTablesAttrs } from './types';


/**
 * OptimizePricingTables's script data component.
 *
 * @since ??
 *
 * @param {ModuleScriptDataProps<OptimizePricingTablesAttrs>} props React component props.
 *
 * @returns {ReactElement}
 */
export const ModuleScriptData = ({
  elements,
}: ModuleScriptDataProps<OptimizePricingTablesAttrs>): ReactElement => (
  <Fragment>
    {elements.scriptData({
      attrName: 'module',
    })}
  </Fragment>
);

