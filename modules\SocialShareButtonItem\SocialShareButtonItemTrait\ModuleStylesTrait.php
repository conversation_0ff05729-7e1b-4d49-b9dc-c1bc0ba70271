<?php

/**
 * SocialShareButtonItem::module_styles().
 *
 * @package Builder\Packages\ModuleLibrary
 * @since ??
 */

namespace MEE\Modules\SocialShareButtonItem\SocialShareButtonItemTrait;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

use ET\Builder\FrontEnd\Module\Style;
use ET\Builder\Packages\Module\Options\Text\TextStyle;
use ET\Builder\Packages\Module\Options\Css\CssStyle;
use ET\Builder\Packages\Module\Layout\Components\StyleCommon\CommonStyle;
use ET\Builder\Packages\ModuleLibrary\ModuleRegistration;
use MEE\Modules\SocialShareButtonItem\SocialShareButtonItem;

trait ModuleStylesTrait
{

	use CustomCssTrait;
	use StyleDeclarationTrait;

	/**
	 * SocialShareButtonItem's style components.
	 *
	 * This function is equivalent of JS function ModuleStyles located in
	 * src/components/SocialShareButtonItem/styles.tsx.
	 *
	 * @param array $args {
	 *     An array of arguments.
	 *
	 * @type string $id Module ID. In VB, the ID of module is UUIDV4. In FE, the ID is order index.
	 * @type string $name Module name.
	 * @type string $attrs Module attributes.
	 * @type string $parentAttrs Parent attrs.
	 * @type string $orderClass Selector class name.
	 * @type string $parentOrderClass Parent selector class name.
	 * @type string $wrapperOrderClass Wrapper selector class name.
	 * @type string $settings Custom settings.
	 * @type string $state Attributes state.
	 * @type string $mode Style mode.
	 * @type ModuleElements $elements ModuleElements instance.
	 * }
	 * @since ??
	 */
	public static function module_styles($args)
	{
		$attrs        = $args['attrs'] ?? [];
		$order_class  = $args['orderClass'];
		$elements     = $args['elements'];
		$settings     = $args['settings'] ?? [];
		$parent_attrs = $args['parentAttrs'] ?? [];

		$parent_default_attributes = ModuleRegistration::get_default_attrs('dotm/social-share-buttons');
		$parent_attrs_with_default = array_replace_recursive($parent_default_attributes, $parent_attrs);

		$button_type = $attrs['button']['advanced']['type']['desktop']['value'] ?? 'facebook';
		$buttonItemSelector = "{$order_class} .dotm_social_button_item";
		$iconSelector = "{$order_class} .dotm_social_button_icon span";
		$buttonIconLabelSelector = "{$order_class} .dotm_social_button_item span, {$order_class} .dotm_social_button_icon span";
		$dynamicBtnSelector = "{$order_class} .dotm_social_button_{$button_type} .dotm_social_button_icon span";

		$color_type = $attrs['button']['advanced']['color_type']['desktop']['value']
			?? $parent_attrs['button']['advanced']['color_type']['desktop']['value'] ?? null;
		$btn_size = $parent_attrs['button']['advanced']['btnSize']['desktop']['value'] ?? '';
		$parent_btn_style = $parent_attrs['general']['advanced']['buttonStyle']['desktop']['value'] ?? 'minimal';
		$bg_color = $attrs['button']['advanced']['BGColor']['desktop']['value']
			?? $parent_attrs['button']['advanced']['BGColor']['desktop']['value'] ?? null;
		$label_color = $attrs['button']['advanced']['labelColor']['desktop']['value']
			?? $parent_attrs['button']['advanced']['labelColor']['desktop']['value'] ?? null;
		$parent_label_color = $parent_attrs['customLabel']['decoration']['font']['font']['desktop']['value']['color'] ?? null;

		Style::add(
			[
				'id'            => $args['id'],
				'name'          => $args['name'],
				'orderIndex'    => $args['orderIndex'],
				'storeInstance' => $args['storeInstance'],
				'styles'        => [
					// Module.
					$elements->style(
						[
							'attrName'   => 'module',
							'styleProps' => [
								'disabledOn'     => [
									'disabledModuleVisibility' => $settings['disabledModuleVisibility'] ?? null,
								],
								'advancedStyles' => [
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $dynamicBtnSelector,
											'attr'     => $attrs['button']['advanced']['type'] ?? [],
											'property' => ($parent_btn_style === 'framed') ? match ($button_type) {
												'facebook' => 'color: #3b5998;',
												'twitter' => 'color: #00aced;',
												'linkedin' => 'color: #007bb6;',
												'pinterest' => 'color: #cb2027;',
												'reddit' => 'color: #ff4500;',
												'vk' => 'color: #45668e;',
												'tumblr' => 'color: #32506d;',
												'digg' => 'color: #005be2;',
												'skype' => 'color: #12a5f4;',
												'stumbleupon' => 'color: #eb4924;',
												'mix' => 'color: #f3782b;',
												'telegram' => 'color: #2ca5e0;',
												'pocket' => 'color: #ef3f56;',
												'xing' => 'color: #026466;',
												'whatsapp' => 'color: #25d366;',
												'email' => 'color: #ea4335;',
												'print' => 'color: #aaa;',
												'instagram' => 'color: #ea2c59;',
												'flickr' => 'color: #ff0084;',
												'dribbble' => 'color: #ea4c8d;',
												'youtube' => 'color: #a82400;',
												'vimeo' => 'color: #45bbff;',
												default => ''
											} : '',
										]
									]
								]
							],
						]
					),

					// Button.
					$elements->style(
						[
							'attrName'   => 'button',
							'styleProps' => [
								'advancedStyles' => [
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $buttonItemSelector,
											'attr'                => $attrs['button']['advanced']['BGColor'] ?? $parent_attrs_with_default['button']['advanced']['BGColor'] ?? [],
											'property'           => $color_type === 'custom' ? "background-color: {$bg_color} !important;" : '',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $buttonIconLabelSelector,
											'attr'                => $attrs['button']['advanced']['labelColor'] ?? $parent_attrs_with_default['button']['advanced']['labelColor'] ?? [],
											'property'           => $color_type === 'custom' && !$parent_label_color ? "color: {$label_color} !important;" : '',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $buttonItemSelector,
											'attr'                => $parent_attrs_with_default['button']['advanced']['btnSize'] ?? [],
											'property'           => "font-size: calc({$btn_size} * 10); height: 4.5em;",
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $iconSelector,
											'attr'                => $parent_attrs_with_default['button']['advanced']['iconSize'] ?? [],
											'property'           => "font-size",
										]
									],
								]
							]
						]
					),

					/*
					 * We need to add CssStyle at the very bottom of other
					 * components so that custom css can override module styles
					 * till we find a more elegant solution.
					 */
					CssStyle::style(
						[
							'selector'  => $args['orderClass'],
							'attr'      => $attrs['css'] ?? [],
							'cssFields' => self::custom_css(),
						]
					),

					// ATTENTION: The code is intentionally added and commented in FE only as an example of expected value format.
					// If you have custom style processing, the style output should be passed as an `array` of style declarations
					// to the `styles` property of the `Style::add` method. For example:
					// [
					// 	[
					// 		'atRules'     => false,
					// 		'selector'    => $icon_selector,
					// 		'declaration' => 'color: red;'
					// 	],
					// 	[
					// 		'atRules'     => '@media only screen and (max-width: 767px)',
					// 		'selector'    => $icon_selector,
					// 		'declaration' => 'color: green;'
					// 	],
					// ],

					// The code below is an example of how to use the `CssStyle::style` method to generate CSS style.
					CssStyle::style(
						[
							'selector'  => $args['orderClass'],
							'attr'      => $attrs['css'] ?? [],
							'cssFields' => self::custom_css(),
						]
					),
				],
			]
		);
	}
}
