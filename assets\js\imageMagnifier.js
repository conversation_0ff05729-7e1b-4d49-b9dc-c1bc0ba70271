const ImageMagnifier = (props={}) => {

    const {containerId, zoom_lavel} = props;

        const container = document.getElementById(containerId);
        if (!container) return;

        const glass = container.querySelector('.dotm_image_magnifier_glass');
        const img = container.querySelector('.dotm_image_magnifier_image');

        if (!glass || !img) return;

        // Set magnification level
        const zoom = zoom_lavel;

        // When the image is loaded, set up event listeners
        img.onload = function () {
            setupMagnifier();
        };

        // Setup the magnifier immediately if the image is already loaded
        if (img.complete) {
            setupMagnifier();
        }

        function setupMagnifier() {
            // Create event listeners for mouse movements
            container.addEventListener('mousemove', moveMagnifier);
            container.addEventListener('mouseenter', showGlass);
            container.addEventListener('mouseleave', hideGlass);

            // Get the position of the container relative to the viewport
            function getOffset(el) {
                const rect = el.getBoundingClientRect();
                return {
                    left: rect.left + window.scrollX,
                    top: rect.top + window.scrollY
                };
            }

            function showGlass() {
                glass.style.display = 'block';
            }

            function hideGlass() {
                glass.style.display = 'none';
            }

            function moveMagnifier(e) {
                // Prevent any default action
                e.preventDefault();

                // Get cursor position
                const offset = getOffset(container);
                let mouseX = e.pageX - offset.left;
                let mouseY = e.pageY - offset.top;

                // Make sure cursor position is within bounds
                const imgWidth = img.width;
                const imgHeight = img.height;

                mouseX = Math.min(Math.max(mouseX, 0), imgWidth);
                mouseY = Math.min(Math.max(mouseY, 0), imgHeight);

                // Calculate the position of the magnifier glass
                const glassRadius = glass.offsetWidth / 2;

                // Position the glass
                glass.style.left = (mouseX - glassRadius) + 'px';
                glass.style.top = (mouseY - glassRadius) + 'px';

                // Calculate the position for the background image
                const bgPosX = -((mouseX * zoom) - glassRadius) + 'px';
                const bgPosY = -((mouseY * zoom) - glassRadius) + 'px';

                // Set the background properties for the glass
                glass.style.backgroundImage = `url('${img.src}')`;
                glass.style.backgroundSize = (imgWidth * zoom) + 'px ' + (imgHeight * zoom) + 'px';
                glass.style.backgroundPosition = bgPosX + ' ' + bgPosY;
            }
        }

}

