<?php

/**
 * OptimizeFAQ::module_styles().
 *
 * @package MEE\Modules\OptimizeFAQ
 * @since ??
 */

namespace MEE\Modules\OptimizeFAQ\OptimizeFAQTrait;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

use ET\Builder\FrontEnd\Module\Style;
use ET\Builder\Packages\Module\Layout\Components\StyleCommon\CommonStyle;
use ET\Builder\Packages\Module\Options\Css\CssStyle;
use MEE\Modules\OptimizeFAQItem\OptimizeFAQItem;

trait ModuleStylesTrait
{

	use CustomCssTrait;

	/**
	 * OptimizeFAQItem's style components.
	 *
	 * This function is equivalent of JS function ModuleStyles located in
	 * src/components/OptimizeFAQ/styles.tsx.
	 *
	 * @param array $args {
	 *     An array of arguments.
	 *
	 * @type string $id Module ID. In VB, the ID of module is UUIDV4. In FE, the ID is order index.
	 * @type string $name Module name.
	 * @type string $attrs Module attributes.
	 * @type string $parentAttrs Parent attrs.
	 * @type string $orderClass Selector class name.
	 * @type string $parentOrderClass Parent selector class name.
	 * @type string $wrapperOrderClass Wrapper selector class name.
	 * @type string $settings Custom settings.
	 * @type string $state Attributes state.
	 * @type string $mode Style mode.
	 * @type ModuleElements $elements ModuleElements instance.
	 * }
	 * @since ??
	 */
	public static function module_styles($args)
	{
		$attrs        = $args['attrs'] ?? [];
		$parent_attrs = $args['parentAttrs'] ?? [];
		$order_class  = $args['orderClass'];
		$elements     = $args['elements'];
		$settings     = $args['settings'] ?? [];

		$title_selector = "{$order_class} .dotm_optimize_faq_question";
		$active_selector = "{$order_class} .active";
		$container_selector = "{$order_class} .faq-container";
		$icon_selector = "{$order_class} .dotm_optimize_faq_question::after";
		$open_icon_selector = "{$order_class} .dotm_optimize_faq_question.active::after";
		$placement_selector = "{$order_class} .dotm_optimize_faq_question";
		$hover_selector = "{$order_class} .dotm_optimize_faq_question:hover";

		$gap_between = $attrs['faq']['advanced']['gap']['desktop']['value'] ?? "";
		$use_title_icon_placement = $attrs['faq']['advanced']['title_icon_placement']['desktop']['value'] ?? '';

		Style::add(
			[
				'id'            => $args['id'],
				'name'          => $args['name'],
				'orderIndex'    => $args['orderIndex'],
				'storeInstance' => $args['storeInstance'],
				'styles'        => [
					// Module.
					$elements->style(
						[
							'attrName'   => 'module',
							'styleProps' => [
								'disabledOn' => [
									'disabledModuleVisibility' => $settings['disabledModuleVisibility'] ?? null,
								],
								'advancedStyles' => [
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $title_selector,
											'attr'     => $attrs['title']['decoration']['bg_color'] ?? [],
											'property' => 'background',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $active_selector,
											'attr'     => $attrs['title']['decoration']['active_bg_color'] ?? [],
											'property' => 'background',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $hover_selector,
											'attr'     => $attrs['title']['decoration']['hover_bg_color'] ?? [],
											'property' => 'background',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $container_selector,
											'attr'     => $attrs['faq']['decoration']['item_gap'] ?? [],
											'property' => 'gap',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $icon_selector,
											'attr'                => $attrs['icon']['innerContent'] ?? [],
											'declarationFunction' => [OptimizeFAQItem::class, 'icon_font_declaration'],
										]
									],
									// [
									// 	'componentName' => 'divi/common',
									// 	'props'         => [
									// 		'selector' => $open_icon_selector,
									// 		'attr'                => $attrs['open_icon']['innerContent'] ?? [],
									// 		'declarationFunction' => [OptimizeFAQItem::class, 'icon_font_declaration'],
									// 	]
									// ],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $icon_selector,
											'attr'                => $attrs['icon']['decoration']['bgColor'] ?? [],
											'property'     => 'background',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $placement_selector,
											'attr'                => $attrs['faq']['advanced']['title_icon_placement'] ?? [],
											'property'     => $use_title_icon_placement === 'on' ? "flex-direction: row-reverse; justify-content: flex-end; gap: {$gap_between};" : '',
										]
									],
								]
							],
						]
					),

					// faq_lavel.
					$elements->style(
						[
							'attrName' => 'faq_lavel',
						]
					),

					// Title.
					$elements->style(
						[
							'attrName' => 'title',
						]
					),


					// Content.
					$elements->style(
						[
							'attrName' => 'content',
						]
					),

					// Icon.
					$elements->style(
						[
							'attrName' => 'icon',
						]
					),

					// FAQ.
					$elements->style(
						[
							'attrName' => 'faq',
						]
					),

					/*
					 * We need to add CssStyle at the very bottom of other
					 * components so that custom css can override module styles
					 * till we find a more elegant solution.
					 */
					CssStyle::style(
						[
							'selector'  => $args['orderClass'],
							'attr'      => $attrs['css'] ?? [],
							'cssFields' => self::custom_css(),
						]
					),

					// ATTENTION: The code is intentionally added and commented in FE only as an example of expected value format.
					// If you have custom style processing, the style output should be passed as an `array` of style declarations
					// to the `styles` property of the `Style::add` method. For example:
					// [
					// 	[
					// 		'atRules'     => false,
					// 		'selector'    => $icon_selector,
					// 		'declaration' => 'color: red;'
					// 	],
					// 	[
					// 		'atRules'     => '@media only screen and (max-width: 767px)',
					// 		'selector'    => $icon_selector,
					// 		'declaration' => 'color: green;'
					// 	],
					// ],

					// The code below is an example of how to use the `CssStyle::style` method to generate CSS style.
					CssStyle::style(
						[
							'selector'  => $args['orderClass'],
							'attr'      => $attrs['css'] ?? [],
							'cssFields' => self::custom_css(),
						]
					),
				],
			]
		);
	}
}
