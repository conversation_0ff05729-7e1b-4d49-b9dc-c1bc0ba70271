<?php

/**
 * Module: OptimizeCardCarouselItem class.
 *
 * @package MEE\Modules\OptimizeCardCarouselItem
 * @since ??
 */

namespace MEE\Modules\OptimizeCardCarouselItem;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

use ET\Builder\Framework\DependencyManagement\Interfaces\DependencyInterface;
use ET\Builder\Packages\ModuleLibrary\ModuleRegistration;

/**
 * `OptimizeCardCarouselItem` is consisted of functions used for Child Module such as Front-End rendering, REST API Endpoints etc.
 *
 * This is a dependency class and can be used as a dependency for `DependencyTree`.
 *
 * @since ??
 */
class OptimizeCardCarouselItem implements DependencyInterface
{
	use OptimizeCardCarouselItemTrait\RenderCallbackTrait;

	/**
	 * Loads `OptimizeCardCarouselItem` and registers Front-End render callback and REST API Endpoints.
	 *
	 * @since ??
	 *
	 * @return void
	 */
	public function load()
	{
		$module_json_folder_path = DIVIOPTIMIZER_MODULES_JSON_PATH . 'OptimizeCardCarouselItem/';

		add_action(
			'init',
			function () use ($module_json_folder_path) {
				ModuleRegistration::register_module(
					$module_json_folder_path,
					[
						'render_callback' => [OptimizeCardCarouselItem::class, 'render_callback'],
					]
				);
			}
		);
	}
}
