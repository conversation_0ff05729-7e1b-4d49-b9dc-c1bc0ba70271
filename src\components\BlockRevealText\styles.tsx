// External dependencies.
import React, {ReactElement} from 'react';

// Divi dependencies.
import {
  StyleContainer,
  StylesProps,
  CssStyle,
  TextStyle,
} from '@divi/module';

// Local dependencies.
import {BlockRevealTextAttrs} from './types';
import {cssFields} from './custom-css';

/**
 * Static Module's style components.
 *
 * @since ??
 */
export const ModuleStyles = ({
    attrs,
    elements,
    settings,
    orderClass,
    mode,
    state,
    noStyleTag,
  }: StylesProps<BlockRevealTextAttrs>): ReactElement => {
  const revealSelector = `${orderClass} .dotm_block_text_reveal_reveal`;
  const wrapperSelector = `${orderClass} .dotm_block_text_reveal_wrapper`;

  return (
    <StyleContainer mode={mode} state={state} noStyleTag={noStyleTag}>
      {/* Module */}
      {elements.style({
        attrName: 'module',
        styleProps: {
          disabledOn: {
            disabledModuleVisibility: settings?.disabledModuleVisibility,
          },
          advancedStyles: [
            {
              componentName: "divi/common",
              props: {
                selector:revealSelector ,
                attr: attrs?.revealAnimation?.advanced?.revealColor,
                property: "background-color",
              }
            },
            {
              componentName: "divi/common",
              props: {
                selector:wrapperSelector ,
                attr: attrs?.revealAnimation?.advanced?.display_type,
                property: "display",
              }
            }
          ]
        },
      })}

      {/* revealText */}
      {elements.style({
        attrName: 'revealText',
      })}

      {/* revealAnimation */}
      {elements.style({
        attrName: 'revealAnimation',
      })}


      {/*
       * We need to add CssStyle at the very bottom of other components
       * so that custom css can override module styles till we find a
       * more elegant solution.
       */}
      <CssStyle
        selector={orderClass}
        attr={attrs.css}
        cssFields={cssFields}
      />

    </StyleContainer>
  );
};
