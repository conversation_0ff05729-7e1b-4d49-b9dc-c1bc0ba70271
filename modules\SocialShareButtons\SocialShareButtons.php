<?php

/**
 * Module: SocialShareButtons class.
 *
 * @package MEE\Modules\SocialShareButtons
 * @since ??
 */

namespace MEE\Modules\SocialShareButtons;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}


use ET\Builder\Framework\DependencyManagement\Interfaces\DependencyInterface;
use ET\Builder\Packages\ModuleLibrary\ModuleRegistration;

/**
 * `SocialShareButtons` is consisted of functions used for Parent Module such as Front-End rendering, REST API Endpoints etc.
 *
 * This is a dependency class and can be used as a dependency for `DependencyTree`.
 *
 * @since ??
 */
class SocialShareButtons implements DependencyInterface
{
	use SocialShareButtonsTrait\RenderCallbackTrait;

	/**
	 * Loads `SocialShareButtons` and registers Front-End render callback and REST API Endpoints.
	 *
	 * @since ??
	 *
	 * @return void
	 */
	public function load()
	{
		$module_json_folder_path = DIVIOPTIMIZER_MODULES_JSON_PATH . 'SocialShareButtons/';

		add_action(
			'init',
			function () use ($module_json_folder_path) {
				ModuleRegistration::register_module(
					$module_json_folder_path,
					[
						'render_callback' => [SocialShareButtons::class, 'render_callback'],
					]
				);
			}
		);
	}
}
