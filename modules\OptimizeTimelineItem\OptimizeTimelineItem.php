<?php

/**
 * Module: OptimizeTimelineItem class.
 *
 * @package MEE\Modules\OptimizeTimelineItem
 * @since ??
 */

namespace MEE\Modules\OptimizeTimelineItem;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

use ET\Builder\Framework\DependencyManagement\Interfaces\DependencyInterface;
use ET\Builder\Packages\ModuleLibrary\ModuleRegistration;

/**
 * `OptimizeTimelineItem` is consisted of functions used for OptimizeTimelineItem such as Front-End rendering, REST API Endpoints etc.
 *
 * This is a dependency class and can be used as a dependency for `DependencyTree`.
 *
 * @since ??
 */
class OptimizeTimelineItem implements DependencyInterface
{
	use OptimizeTimelineItemTrait\RenderCallbackTrait;

	/**
	 * Loads `OptimizeTimelineItem` and registers Front-End render callback and REST API Endpoints.
	 *
	 * @since ??
	 *
	 * @return void
	 */
	public function load()
	{
		$module_json_folder_path = DIVIOPTIMIZER_MODULES_JSON_PATH . 'OptimizeTimelineItem/';

		add_action(
			'init',
			function () use ($module_json_folder_path) {
				ModuleRegistration::register_module(
					$module_json_folder_path,
					[
						'render_callback' => [OptimizeTimelineItem::class, 'render_callback'],
					]
				);
			}
		);
	}
}
