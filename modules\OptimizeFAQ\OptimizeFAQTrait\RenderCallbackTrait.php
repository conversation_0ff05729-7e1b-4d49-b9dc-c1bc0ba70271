<?php

/**
 * OptimizeFAQItem::render_callback()
 *
 * @package MEE\Modules\OptimizeFAQItem
 * @since ??
 */

namespace MEE\Modules\OptimizeFAQ\OptimizeFAQTrait;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

// phpcs:disable ET.Sniffs.ValidVariableName.UsedPropertyNotSnakeCase -- WP use snakeCase in \WP_Block_Parser_Block

use ET\Builder\Framework\Utility\HTMLUtility;
use ET\Builder\FrontEnd\BlockParser\BlockParserStore;
use ET\Builder\Packages\Module\Module;
use ET\Builder\Packages\Module\Options\Element\ElementComponents;
use MEE\Modules\OptimizeFAQ\OptimizeFAQ;

trait RenderCallbackTrait
{
	use ModuleClassnamesTrait;
	use ModuleStylesTrait;
	use ModuleScriptDataTrait;
	/**
	 * Parent module render callback which outputs server side rendered HTML on the Front-End.
	 *
	 * @since ??
	 *
	 * @param array          $attrs Block attributes that were saved by VB.
	 * @param string         $content          Block content.
	 * @param \WP_Block      $block            Parsed block object that being rendered.
	 * @param ModuleElements $elements         ModuleElements instance.
	 *
	 * @return string HTML rendered of Parent module.
	 */
	public static function render_callback($attrs, $content, $block, $elements)
	{
		$children_ids = $block->parsed_block['innerBlocks'] ? array_map(
			function ($inner_block) {
				return $inner_block['id'];
			},
			$block->parsed_block['innerBlocks']
		) : [];

		$parent       = BlockParserStore::get_parent($block->parsed_block['id'], $block->parsed_block['storeInstance']);
		$parent_attrs = $parent->attrs ?? [];

		$use_opened = $attrs['faq']['advanced']['opened']['desktop']['value'] ?? '';

		$options = [
			'use_opened' => $use_opened,
		];

		// faq_lavel.
		$faq_lavel = $elements->render(
			[
				'attrName'      => 'faq_lavel',
				'hoverSelector' => '{{parentSelector}}',
			]
		);


		// main container.
		$main_container = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'faq-container',
					// 'id'    => 'faq-container',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $faq_lavel . $content,
			]
		);


		return Module::render(
			[
				// FE only.
				'orderIndex'          => $block->parsed_block['orderIndex'],
				'storeInstance'       => $block->parsed_block['storeInstance'],

				// VB equivalent.
				'id'                  => $block->parsed_block['id'],
				'name'                => $block->block_type->name,
				'moduleCategory'      => $block->block_type->category,
				'attrs'               => $attrs,
				'elements'            => $elements,
				'classnamesFunction'  => [OptimizeFAQ::class, 'module_classnames'],
				'scriptDataComponent' => [OptimizeFAQ::class, 'module_script_data'],
				'stylesComponent'     => [OptimizeFAQ::class, 'module_styles'],
				'parentAttrs'         => $parent_attrs,
				'parentId'            => $parent->id ?? '',
				'parentName'          => $parent->blockName ?? '',
				'children'            => [
					ElementComponents::component(
						[
							'attrs'         => $attrs['module']['decoration'] ?? [],
							'id'            => $block->parsed_block['id'],

							// FE only.
							'orderIndex'    => $block->parsed_block['orderIndex'],
							'storeInstance' => $block->parsed_block['storeInstance'],
						]
					) . $main_container,
					'childrenIds'         => $children_ids,
					HTMLUtility::render(
						[
							'tag' => 'script',
							'attributes' => [],
							'childrenSanitizer' => 'et_core_esc_previously',
							'children' => "document.addEventListener('DOMContentLoaded', function() {
                                const options = " . json_encode($options) . ";
                                faq(options);
                            });",
						]
					),
				]

			]
		);
	}
}
