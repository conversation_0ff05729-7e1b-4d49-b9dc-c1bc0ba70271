<?php
/**
 * TwitterButton::render_callback()
 *
 * @package MEE\Modules\TwitterButton
 * @since ??
 */

namespace MEE\Modules\TwitterButton\TwitterButtonTrait;

if ( ! defined( 'ABSPATH' ) ) {
	die( 'Direct access forbidden.' );
}

// phpcs:disable ET.Sniffs.ValidVariableName.UsedPropertyNotSnakeCase -- WP use snakeCase in \WP_Block_Parser_Block

use ET\Builder\Packages\Module\Module;
use ET\Builder\Framework\Utility\HTMLUtility;
use ET\Builder\FrontEnd\BlockParser\BlockParserStore;
use ET\Builder\Packages\Module\Options\Element\ElementComponents;
use MEE\Modules\TwitterButton\TwitterButton;

trait RenderCallbackTrait {

	/**
	 * Static module render callback which outputs server side rendered HTML on the Front-End.
	 *
	 * @since ??
	 * @param array          $attrs    Block attributes that were saved by VB.
	 * @param string         $content  Block content.
	 * @param WP_Block       $block    Parsed block object that being rendered.
	 * @param ModuleElements $elements ModuleElements instance.
	 *
	 * @return string HTML rendered of Static module.
	 */
	public static function render_callback( $attrs, $content, $block, $elements ) {
		  // Read attribute values with default fallbacks
		$tweetText = isset( $attrs['twitter']['advanced']['tweetText']['desktop']['value'] ) ? $attrs['twitter']['advanced']['tweetText']['desktop']['value'] : 'Tweet';
		$tweetUrl  = isset( $attrs['twitter']['advanced']['tweetUrl']['desktop']['value'] ) ? $attrs['twitter']['advanced']['tweetUrl']['desktop']['value'] : 'https://twitter.com/optimizly';
		$viaHandle = isset( $attrs['twitter']['advanced']['viaHandle']['desktop']['value'] ) ? $attrs['twitter']['advanced']['viaHandle']['desktop']['value'] : 'optimizly';
		$size      = isset( $attrs['twitter']['advanced']['size']['desktop']['value'] ) ? $attrs['twitter']['advanced']['size']['desktop']['value'] : 's'; 

		$iframeSrc = HTMLUtility::render(
			[
							'tag'               => 'iframe',
							'attributes'        => [
								'src' => 'https://platform.twitter.com/widgets/tweet_button.html?url=' . $tweetUrl . '&via=' . $viaHandle . '&text=' . $tweetText . '&size=' . $size,
								// 'width' => '72',
								// 'height' => '28',
								'title' => 'Tweet Button',
								'frameborder' => '0',
								'allowtransparency' => 'true',
								'allowfullscreen' => 'true',
								'scrolling' => 'no',
								// 'style' => 'min-width: 72px; min-height: 28px;',
							],
							'childrenSanitizer' => 'et_core_esc_previously',
							'children'          =>"",
			]
						);

		$parent       = BlockParserStore::get_parent( $block->parsed_block['id'], $block->parsed_block['storeInstance'] );
		$parent_attrs = $parent->attrs ?? [];

		return Module::render(
			[
				// FE only.
				'orderIndex'          => $block->parsed_block['orderIndex'],
				'storeInstance'       => $block->parsed_block['storeInstance'],

				// VB equivalent.
				'attrs'               => $attrs,
				'elements'            => $elements,
				'id'                  => $block->parsed_block['id'],
				'name'                => $block->block_type->name,
				'moduleCategory'      => $block->block_type->category,
				'classnamesFunction'  => [ TwitterButton::class, 'module_classnames' ],
				'stylesComponent'     => [ TwitterButton::class, 'module_styles' ],
				'scriptDataComponent' => [ TwitterButton::class, 'module_script_data' ],
				'parentAttrs'         => $parent_attrs,
				'parentId'            => $parent->id ?? '',
				'parentName'          => $parent->blockName ?? '',
				'children'            => [
					ElementComponents::component(
						[
							'attrs'         => $attrs['module']['decoration'] ?? [],
							'id'            => $block->parsed_block['id'],

							// FE only.
							'orderIndex'    => $block->parsed_block['orderIndex'],
							'storeInstance' => $block->parsed_block['storeInstance'],
						]
					),
					HTMLUtility::render(
						[
							'tag'               => 'div',
							'attributes'        => [
								'class' => 'dotm_twitter_button_module',
							],
							'childrenSanitizer' => 'et_core_esc_previously',
							'children'          =>"$iframeSrc",
						]
					),
				],
			]
		);
	}
}
