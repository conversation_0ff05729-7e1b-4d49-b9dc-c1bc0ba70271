<?php

/**
 * Module: OptimizeFlipBox class.
 *
 * @package MEE\Modules\OptimizeFlipBox
 * @since ??
 */

namespace MEE\Modules\OptimizeFlipBox;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

use ET\Builder\Framework\DependencyManagement\Interfaces\DependencyInterface;
use ET\Builder\Packages\ModuleLibrary\ModuleRegistration;


/**
 * `OptimizeFlipBox` is consisted of functions used for OptimizeFlipBox such as Front-End rendering, REST API Endpoints etc.
 *
 * This is a dependency class and can be used as a dependency for `DependencyTree`.
 *
 * @since ??
 */
class OptimizeFlipBox implements DependencyInterface
{
	use OptimizeFlipBoxTrait\RenderCallbackTrait;
	use OptimizeFlipBoxTrait\ModuleClassnamesTrait;
	use OptimizeFlipBoxTrait\ModuleStylesTrait;
	use OptimizeFlipBoxTrait\ModuleScriptDataTrait;

	/**
	 * Loads `OptimizeFlipBox` and registers Front-End render callback and REST API Endpoints.
	 *
	 * @since ??
	 *
	 * @return void
	 */
	public function load()
	{
		$module_json_folder_path = DIVIOPTIMIZER_MODULES_JSON_PATH . 'OptimizeFlipBox/';

		add_action(
			'init',
			function () use ($module_json_folder_path) {
				ModuleRegistration::register_module(
					$module_json_folder_path,
					[
						'render_callback' => [OptimizeFlipBox::class, 'render_callback'],
					]
				);
			}
		);
	}
}
