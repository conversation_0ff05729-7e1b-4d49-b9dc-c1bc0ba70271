<?php

/**
 * Module: ImageLogoCarousel class.
 *
 * @package MEE\Modules\ImageLogoCarousel
 * @since ??
 */

namespace MEE\Modules\ImageLogoCarousel;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}


use ET\Builder\Framework\DependencyManagement\Interfaces\DependencyInterface;
use ET\Builder\Packages\ModuleLibrary\ModuleRegistration;

/**
 * `ImageLogoCarousel` is consisted of functions used for ImageLogoCarousel such as Front-End rendering, REST API Endpoints etc.
 *
 * This is a dependency class and can be used as a dependency for `DependencyTree`.
 *
 * @since ??
 */
class ImageLogoCarousel implements DependencyInterface
{
	use ImageLogoCarouselTrait\RenderCallbackTrait;

	/**
	 * Loads `ImageLogoCarousel` and registers Front-End render callback and REST API Endpoints.
	 *
	 * @since ??
	 *
	 * @return void
	 */
	public function load()
	{
		$module_json_folder_path = DIVIOPTIMIZER_MODULES_JSON_PATH . 'ImageLogoCarousel/';

		add_action(
			'init',
			function () use ($module_json_folder_path) {
				ModuleRegistration::register_module(
					$module_json_folder_path,
					[
						'render_callback' => [ImageLogoCarousel::class, 'render_callback'],
					]
				);
			}
		);
	}
}
