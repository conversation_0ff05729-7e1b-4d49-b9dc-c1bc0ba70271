<?php

/**
 * Testimonials::module_styles().
 *
 * @package MEE\Modules\Testimonials
 * @since ??
 */

namespace MEE\Modules\Testimonials\TestimonialsTrait;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

use ET\Builder\FrontEnd\Module\Style;
use ET\Builder\Packages\Module\Options\Text\TextStyle;
use ET\Builder\Packages\Module\Options\Css\CssStyle;
use MEE\Modules\Testimonials\Testimonials;

trait ModuleStylesTrait
{

	use CustomCssTrait;
	use StyleDeclarationTrait;

	/**
	 * Static Module's style components.
	 *
	 * This function is equivalent of JS function ModuleStyles located in
	 * src/components/Testimonials/styles.tsx.
	 *
	 * @since ??
	 *
	 * @param array $args {
	 *     An array of arguments.
	 *
	 *      @type string $id                Module ID. In VB, the ID of module is UUIDV4. In FE, the ID is order index.
	 *      @type string $name              Module name.
	 *      @type string $attrs             Module attributes.
	 *      @type string $parentAttrs       Parent attrs.
	 *      @type string $orderClass        Selector class name.
	 *      @type string $parentOrderClass  Parent selector class name.
	 *      @type string $wrapperOrderClass Wrapper selector class name.
	 *      @type string $settings          Custom settings.
	 *      @type string $state             Attributes state.
	 *      @type string $mode              Style mode.
	 *      @type ModuleElements $elements  ModuleElements instance.
	 * }
	 */
	public static function module_styles($args)
	{
		$attrs    = $args['attrs'] ?? [];
		$elements = $args['elements'];
		$settings = $args['settings'] ?? [];
		$orderClass = $args['orderClass'];

		$textSelector = $orderClass . ' .example_static_module__content-container';
		$iconContainerSelector = $orderClass . ' .dotm_testimonials_icon_container';
		$imageContainerSelector = $orderClass . ' .dotm_testimonials_image_container';
		$profileContainerSelector = $orderClass . ' .dotm_testimonials_profile_container';
		$filledStarsSelector = $orderClass . ' .dotm_optimize_rating_star.dotm_optimize_rating_star_filled';
		$starsSelector = $orderClass . ' .dotm_optimize_rating_stars';
		$containerSelector = $orderClass . ' .dotm_optimize_rating_container';
		$icon_type = $attrs['rating']['advanced']['icon_type']['desktop']['value'] ?? '';

		Style::add(
			[
				'id'            => $args['id'],
				'name'          => $args['name'],
				'orderIndex'    => $args['orderIndex'],
				'storeInstance' => $args['storeInstance'],
				'styles'        => [
					// Module.
					$elements->style(
						[
							'attrName'   => 'module',
							'styleProps' => [
								'disabledOn'     => [
									'disabledModuleVisibility' => $settings['disabledModuleVisibility'] ?? null,
								],
								'advancedStyles' => [
									[
										'componentName' => 'divi/text',
										'props'         => [
											'selector' => "{$args['orderClass']} .example_static_module__content-container",
											'attr'     => $attrs['module']['advanced']['text'] ?? [],
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $filledStarsSelector,
											'attr'     => $attrs['rating']['advanced']['star_active_color'] ?? [],
											'property' => "color"
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $starsSelector,
											'attr'     => $attrs['rating']['advanced']['star_color'] ?? [],
											'property' => "color"
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $starsSelector,
											'attr'     => $attrs['rating']['advanced']['star_size'] ?? [],
											'property' => "font-size"
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $starsSelector,
											'attr'     => $attrs['rating']['advanced']['star_gap'] ?? [],
											'property' => "letter-spacing"
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $starsSelector,
											'attr'     => $attrs['rating']['advanced']['icon_type'] ?? [],
											'property' => $icon_type === "normal" ? "font-weight: 900;" : '',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $filledStarsSelector,
											'attr'     => $attrs['rating']['advanced']['icon_type'] ?? [],
											'property' => $icon_type === "filled" ? "font-weight: 900;" : '',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $containerSelector,
											'attr'     => $attrs['rating']['advanced']['alignment'] ?? [],
											'property' => "justify-content",
										]
									]

								]
							],
						]
					),

					// Image.
					$elements->style(
						[
							'attrName' => 'image',
							'styleProps' => [
								'disabledOn'     => [
									'disabledModuleVisibility' => $settings['disabledModuleVisibility'] ?? null,
								],
								'advancedStyles' => [
									[
										'componentName' => 'divi/common',
										'props'         => [
											'attr'     => $attrs['image']['decoration']['image_width'] ?? [],
											'property' => "width"
										]
									],
									[
										'componentName' => 'divi/common',
										'selector' => $imageContainerSelector,
										'props'         => [
											'attr'     => $attrs['image']['decoration']['image_align'] ?? [],
											'property' => "text-align"
										]
									],

								]
							],
						]
					),

					// Content.
					$elements->style(
						[
							'attrName' => 'content',
						]
					),

					// rating.
					$elements->style(
						[
							'attrName' => 'rating',
						]
					),

					// profileContainer.
					$elements->style(
						[
							'attrName' => 'profileContainer',
							'styleProps' => [
								'disabledOn'     => [
									'disabledModuleVisibility' => $settings['disabledModuleVisibility'] ?? null,
								],
								'advancedStyles' => [
									[
										'componentName' => 'divi/common',
										'props'         => [
											'attr'     => $attrs['profile']['decoration']['profile_image_hr_align'] ?? [],
											'property' => "align-items"
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'attr'     => $attrs['profile']['decoration']['profile_image_vr_align'] ?? [],
											'property' => "justify-content"
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'attr'     => $attrs['profile']['decoration']['profile_gap'] ?? [],
											'property' => "gap"
										]
									],

								]
							],
						]
					),

					// profile.
					$elements->style(
						[
							'attrName' => 'profile',
							'styleProps' => [
								'disabledOn'     => [
									'disabledModuleVisibility' => $settings['disabledModuleVisibility'] ?? null,
								],
								'advancedStyles' => [
									[
										'componentName' => 'divi/common',
										'props'         => [
											'attr'     => $attrs['profile']['decoration']['profile_image_width'] ?? [],
											'property' => "width"
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'attr'     => $attrs['profile']['decoration']['profile_image_height'] ?? [],
											'property' => "height"
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $imageContainerSelector,
											'attr'     => $attrs['profile']['decoration']['image_align'] ?? [],
											'property' => "text-align"
										]
									],

								]
							],
						]
					),

					// name.
					$elements->style(
						[
							'attrName' => 'name',
						]
					),

					// label.
					$elements->style(
						[
							'attrName' => 'label',
						]
					),

					// Icon Style
					$elements->style(
						[
							'attrName'   => 'icon',
							'styleProps' => [
								'advancedStyles' => [
									[
										'componentName' => 'divi/common',
										'props'         => [
											'attr'                => $attrs['icon']['innerContent'] ?? [],
											'declarationFunction' => [Testimonials::class, 'icon_font_declaration'],
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'attr'     => $attrs['icon']['advanced']['color'] ?? [],
											'property' => 'color',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'attr'     => $attrs['icon']['advanced']['size'] ?? [],
											'property' => 'font-size',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $iconContainerSelector,
											'attr'     => $attrs['icon']['decoration']['icon_align'] ?? [],
											'property' => 'text-align',
										]
									],
								]
							]
						]
					),

					

					/*
					 * We need to add CssStyle at the very bottom of other
					 * components so that custom css can override module styles
					 * till we find a more elegant solution.
					 */
					CssStyle::style(
						[
							'selector'  => $args['orderClass'],
							'attr'      => $attrs['css'] ?? [],
							'cssFields' => Testimonials::custom_css(),
						]
					),

					// ATTENTION: The code is intentionally added and commented in FE only as an example of expected value format.
					// If you have custom style processing, the style output should be passed as an `array` of style declarations
					// to the `styles` property of the `Style::add` method. For example:
					// [
					// 	[
					// 		'atRules'     => false,
					// 		'selector'    => "{$args['orderClass']} .example_static_module__content-container",
					// 		'declaration' => 'color: red;'
					// 	],
					// 	[
					// 		'atRules'     => '@media only screen and (max-width: 767px)',
					// 		'selector'    => "{$args['orderClass']} .example_static_module__content-container",
					// 		'declaration' => 'color: green;'
					// 	],
					// ],

					// The code below is an example of how to use the `CssStyle::style` method to generate CSS style.
					CssStyle::style(
						[
							'selector'  => $args['orderClass'],
							'attr'      => $attrs['css'] ?? [],
							'cssFields' => Testimonials::custom_css(),
						]
					),
				],
			]
		);
	}
}
