<?php
/**
 * ImageCarouselItem::render_callback()
 *
 * @package MEE\Modules\ImageCarouselItem
 * @since ??
 */

namespace MEE\Modules\ImageCarouselItem\ImageCarouselItemTrait;

if ( ! defined( 'ABSPATH' ) ) {
	die( 'Direct access forbidden.' );
}

// phpcs:disable ET.Sniffs.ValidVariableName.UsedPropertyNotSnakeCase -- WP use snakeCase in \WP_Block_Parser_Block

use ET\Builder\Packages\Module\Module;
use ET\Builder\Framework\Utility\HTMLUtility;
use ET\Builder\FrontEnd\BlockParser\BlockParserStore;
use ET\Builder\Packages\IconLibrary\IconFont\Utils;
use ET\Builder\Packages\Module\Options\Element\ElementComponents;
use ET\Builder\Packages\ModuleLibrary\ModuleRegistration;
use MEE\Modules\ImageCarouselItem\ImageCarouselItem;

trait RenderCallbackTrait {
	use ModuleClassnamesTrait;
	use ModuleStylesTrait;

	/**
	 * ImageCarouselItem render callback which outputs server side rendered HTML on the Front-End.
	 *
	 * @since ??
	 *
	 * @param array          $attrs Block attributes that were saved by VB.
	 * @param string         $content          Block content.
	 * @param WP_Block       $block            Parsed block object that being rendered.
	 * @param ModuleElements $elements         ModuleElements instance.
	 *
	 * @return string HTML rendered of ImageCarouselItem.
	 */
	public static function render_callback( $attrs, $content, $block, $elements ) {
		$parent = BlockParserStore::get_parent( $block->parsed_block['id'], $block->parsed_block['storeInstance'] );

		$parent_attrs = $parent->attrs ?? [];

		$parent_default_attributes = ModuleRegistration::get_default_attrs( 'dotm/image-carousel-item' );
		$parent_attrs_with_default = array_replace_recursive( $parent_default_attributes, $parent_attrs );

		// Icon.
		$icon_value = $attrs['icon']['innerContent']['desktop']['value'] ?? $parent_attrs_with_default['icon']['innerContent']['desktop']['value'] ?? [];

		$button_link = $attrs['button']['advanced']['link']['desktop']['value']['url'] ?? '';
		$button_target = $attrs['button']['advanced']['link']['desktop']['value']['target'] ?? '';
		$parentAlignment = $parent_attrs_with_default['contents']['decoration']['alignment']['desktop']['value'] ?? 'dotm_image_carousel_contents_center_center';
		$contentAlignment = $attrs['contents']['decoration']['alignment']['desktop']['value'] ?? $parentAlignment;

		

		// image.
		$image = $elements->render(
			[
				'attrName'      => 'image',
				'hoverSelector' => '{{parentSelector}}',
			]
		);

		$overlay = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'dotm_image_carousel_image_overlay',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => '',
			]
		);

		// Title.
		$title = $elements->render(
			[
				'attrName'      => 'title',
				'hoverSelector' => '{{parentSelector}}',
			]
		);

		// Content.
		$content = $elements->render(
			[
				'attrName'      => 'content',
				'hoverSelector' => '{{parentSelector}}',
			]
		);

		$button = $elements->render(
			[
				'attrName'      => 'button',
				'hoverSelector' => '{{parentSelector}}',
				'attributes'    => [
					'class' => 'dotm_image_carousel_button',
					'href'  => $button_link,
					'target' => $button_target === 'on' ? '_blank' : '_self',
				]
			]
		);

		$button_container = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => "dotm_image_carousel_button_container",
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $button,
			]
		);

		$contents = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => "dotm_image_carousel_contents $contentAlignment",
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $title . $content . $button_container,
			]
		);

		// Content container.
		$slide_item = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'dotm_image_carousel_slide',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $image . $overlay . $contents,
			]
		);

		return Module::render(
			[
				// FE only.
				'orderIndex'         => $block->parsed_block['orderIndex'],
				'storeInstance'      => $block->parsed_block['storeInstance'],

				// VB equivalent.
				'id'                 => $block->parsed_block['id'],
				'name'               => $block->block_type->name,
				'moduleCategory'     => $block->block_type->category,
				'attrs'              => $attrs,
				'elements'           => $elements,
				'classnamesFunction' => [ ImageCarouselItem::class, 'module_classnames' ],
				'stylesComponent'    => [ ImageCarouselItem::class, 'module_styles' ],
				'parentAttrs'        => $parent_attrs,
				'parentId'           => $parent->id ?? '',
				'parentName'         => $parent->blockName ?? '',
				'children'           => ElementComponents::component(
					[
						'attrs'         => $attrs['module']['decoration'] ?? [],
						'id'            => $block->parsed_block['id'],

						// FE only.
						'orderIndex'    => $block->parsed_block['orderIndex'],
						'storeInstance' => $block->parsed_block['storeInstance'],
					]
				) . $slide_item . 'Hello Child content',
			]
		);
	}
}
