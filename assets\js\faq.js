function faq(options = {}) {

    const {use_opened} = options;

    // Select all FAQ questions
    const faqQuestions = document.querySelectorAll('.dotm_optimize_faq_question');

    // Add click event to FAQ questions
    faqQuestions.forEach(question => {
        question.addEventListener('click', () => {
            if (use_opened === "on") {
                // In Advanced mode, toggle only the clicked question
                question.classList.toggle('active');
                const answer = question.nextElementSibling;
                if (answer.style.maxHeight) {
                    answer.style.maxHeight = null; // Collapse
                } else {
                    answer.style.maxHeight = (answer.scrollHeight + 10) + "px"; // Expand
                }
            } else {
                // In Normal mode, close others and open the clicked one
                faqQuestions.forEach(q => {
                    if (q !== question) {
                        q.classList.remove('active');
                        q.nextElementSibling.style.maxHeight = null; // Collapse others
                    }
                });

                question.classList.toggle('active');
                const answer = question.nextElementSibling;
                if (answer.style.maxHeight) {
                    answer.style.maxHeight = null; // Collapse
                } else {
                    answer.style.maxHeight = (answer.scrollHeight + 10) + "px"; // Expand
                }
            }
        });
    });
}