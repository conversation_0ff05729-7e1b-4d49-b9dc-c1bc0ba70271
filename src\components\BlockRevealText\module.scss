.dotm_block_text_reveal_wrapper {
    position: relative;
    overflow: hidden;
    opacity: 0;
    // transform: translateY(30px);
    transition: opacity 1s ease, transform 1s ease;
    pointer-events: none;
}

.dotm_block_text_reveal_wrapper.reveal {
    opacity: 1;
    transform: translateY(0);
    pointer-events: auto;
}

.dotm_block_text_reveal_text {
    padding-bottom: 0px;
}
.dotm_block_text_reveal_reveal {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 100%;
    width: 100%;
    transition: transform 1.5s ease;
    transition-delay: .5s;
    z-index: 2;
    // background-color: red;
}

.dotm_optimize_image_reveal_left_to_right {
    transform: translateX(0);
}

.reveal .dotm_block_text_reveal_reveal {
    transform: translateX(100%);
}


.dotm_optimize_image_reveal_right_to_left {
    transform: translateX(0);
}

.reveal .dotm_optimize_image_reveal_right_to_left {
    transform: translateX(-100%);
}

.dotm_optimize_image_reveal_top_to_bottom {
    transform: translateY(0);
}

.reveal .dotm_optimize_image_reveal_top_to_bottom {
    transform: translateY(100%);
}

.dotm_optimize_image_reveal_bottom_to_top {
    transform: translateY(0);
}

.reveal .dotm_optimize_image_reveal_bottom_to_top {
    transform: translateY(-100%);
}