<?php

/**
 * Module: TextDivider class.
 *
 * @package MEE\Modules\TextDivider
 * @since ??
 */

namespace MEE\Modules\TextDivider;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

use ET\Builder\Framework\DependencyManagement\Interfaces\DependencyInterface;
use ET\Builder\Packages\ModuleLibrary\ModuleRegistration;


/**
 * `TextDivider` is consisted of functions used for Static Module such as Front-End rendering, REST API Endpoints etc.
 *
 * This is a dependency class and can be used as a dependency for `DependencyTree`.
 *
 * @since ??
 */
class TextDivider implements DependencyInterface
{
	use TextDividerTrait\RenderCallbackTrait;
	use TextDividerTrait\ModuleClassnamesTrait;
	use TextDividerTrait\ModuleStylesTrait;
	use TextDividerTrait\ModuleScriptDataTrait;

	/**
	 * Loads `TextDivider` and registers Front-End render callback and REST API Endpoints.
	 *
	 * @since ??
	 *
	 * @return void
	 */
	public function load()
	{
		$module_json_folder_path = DIVIOPTIMIZER_MODULES_JSON_PATH . 'TextDivider/';

		add_action(
			'init',
			function () use ($module_json_folder_path) {
				ModuleRegistration::register_module(
					$module_json_folder_path,
					[
						'render_callback' => [TextDivider::class, 'render_callback'],
					]
				);
			}
		);
	}
}
