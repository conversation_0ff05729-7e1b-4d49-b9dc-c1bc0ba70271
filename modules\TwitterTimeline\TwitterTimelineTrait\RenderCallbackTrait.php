<?php
/**
 * TwitterTimeline::render_callback()
 *
 * @package MEE\Modules\TwitterTimeline
 * @since ??
 */

namespace MEE\Modules\TwitterTimeline\TwitterTimelineTrait;

if ( ! defined( 'ABSPATH' ) ) {
	die( 'Direct access forbidden.' );
}

// phpcs:disable ET.Sniffs.ValidVariableName.UsedPropertyNotSnakeCase -- WP use snakeCase in \WP_Block_Parser_Block

use ET\Builder\Packages\Module\Module;
use ET\Builder\Framework\Utility\HTMLUtility;
use ET\Builder\FrontEnd\BlockParser\BlockParserStore;
use ET\Builder\Packages\Module\Options\Element\ElementComponents;
use MEE\Modules\TwitterTimeline\TwitterTimeline;

trait RenderCallbackTrait {

	/**
	 * Static module render callback which outputs server side rendered HTML on the Front-End.
	 *
	 * @since ??
	 * @param array          $attrs    Block attributes that were saved by VB.
	 * @param string         $content  Block content.
	 * @param WP_Block       $block    Parsed block object that being rendered.
	 * @param ModuleElements $elements ModuleElements instance.
	 *
	 * @return string HTML rendered of Static module.
	 */
	public static function render_callback( $attrs, $content, $block, $elements ) {
		// Title.
		$twitterusername = $attrs['userName']["innerContent"]['desktop']['value'];

		// Content container.
		$content_container = HTMLUtility::render(
			[
				'tag'               => 'a',
				'attributes'        => [
					'class' => 'twitter_timeline',
					'href'  => "https://twitter.com/$twitterusername}?ref_src=twsrc%5Etfw",

				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => "Tweets by @ $twitterusername ",
			]
		);

		$parent       = BlockParserStore::get_parent( $block->parsed_block['id'], $block->parsed_block['storeInstance'] );
		$parent_attrs = $parent->attrs ?? [];

		return Module::render(
			[
				// FE only.
				'orderIndex'          => $block->parsed_block['orderIndex'],
				'storeInstance'       => $block->parsed_block['storeInstance'],

				// VB equivalent.
				'attrs'               => $attrs,
				'elements'            => $elements,
				'id'                  => $block->parsed_block['id'],
				'name'                => $block->block_type->name,
				'moduleCategory'      => $block->block_type->category,
				'classnamesFunction'  => [ TwitterTimeline::class, 'module_classnames' ],
				'stylesComponent'     => [ TwitterTimeline::class, 'module_styles' ],
				'scriptDataComponent' => [ TwitterTimeline::class, 'module_script_data' ],
				'parentAttrs'         => $parent_attrs,
				'parentId'            => $parent->id ?? '',
				'parentName'          => $parent->blockName ?? '',
				'children'            => [
					ElementComponents::component(
						[
							'attrs'         => $attrs['module']['decoration'] ?? [],
							'id'            => $block->parsed_block['id'],

							// FE only.
							'orderIndex'    => $block->parsed_block['orderIndex'],
							'storeInstance' => $block->parsed_block['storeInstance'],
						]
					),
					HTMLUtility::render(
						[
							'tag'               => 'div',
							'attributes'        => [
								'class' => 'dtom-twitter-timeline-wrapper',
							],
							'childrenSanitizer' => 'et_core_esc_previously',
							'children'          => $content_container,
						]
					),
				],
			]
		);
	}
}
