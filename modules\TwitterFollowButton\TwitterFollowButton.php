<?php

/**
 * Module: Static Module class.
 *
 * @package MEE\Modules\TwitterFollowButton
 * @since ??
 */

namespace MEE\Modules\TwitterFollowButton;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

use ET\Builder\Framework\DependencyManagement\Interfaces\DependencyInterface;
use ET\Builder\Packages\ModuleLibrary\ModuleRegistration;


/**
 * `TwitterFollowButton` is consisted of functions used for Static Module such as Front-End rendering, REST API Endpoints etc.
 *
 * This is a dependency class and can be used as a dependency for `DependencyTree`.
 *
 * @since ??
 */
class TwitterFollowButton implements DependencyInterface
{
	use TwitterFollowButtonTrait\RenderCallbackTrait;
	use TwitterFollowButtonTrait\ModuleClassnamesTrait;
	use TwitterFollowButtonTrait\ModuleStylesTrait;
	use TwitterFollowButtonTrait\ModuleScriptDataTrait;

	/**
	 * <PERSON>ads `TwitterFollowButton` and registers Front-End render callback and REST API Endpoints.
	 *
	 * @since ??
	 *
	 * @return void
	 */
	public function load()
	{
		$module_json_folder_path = DIVIOPTIMIZER_MODULES_JSON_PATH . 'TwitterFollowButton/';

		add_action(
			'init',
			function () use ($module_json_folder_path) {
				ModuleRegistration::register_module(
					$module_json_folder_path,
					[
						'render_callback' => [TwitterFollowButton::class, 'render_callback'],
					]
				);
			}
		);
	}
}
