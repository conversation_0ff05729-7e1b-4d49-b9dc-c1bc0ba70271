<?php

/**
 * SocialShareButtonItem::render_callback()
 *
 * @package MEE\Modules\SocialShareButtonItem
 * @since ??
 */

namespace MEE\Modules\SocialShareButtonItem\SocialShareButtonItemTrait;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

// phpcs:disable ET.Sniffs.ValidVariableName.UsedPropertyNotSnakeCase -- WP use snakeCase in \WP_Block_Parser_Block

use ET\Builder\Packages\Module\Module;
use ET\Builder\Framework\Utility\HTMLUtility;
use ET\Builder\FrontEnd\BlockParser\BlockParserStore;
use ET\Builder\Packages\IconLibrary\IconFont\Utils;
use ET\Builder\Packages\Module\Options\Element\ElementComponents;
use ET\Builder\Packages\ModuleLibrary\ModuleRegistration;
use MEE\Modules\SocialShareButtonItem\SocialShareButtonItem;

trait RenderCallbackTrait
{
	use ModuleClassnamesTrait;
	use ModuleStylesTrait;

	/**
	 * SocialShareButtonItem render callback which outputs server side rendered HTML on the Front-End.
	 *
	 * @since ??
	 *
	 * @param array          $attrs Block attributes that were saved by VB.
	 * @param string         $content          Block content.
	 * @param WP_Block       $block            Parsed block object that being rendered.
	 * @param ModuleElements $elements         ModuleElements instance.
	 *
	 * @return string HTML rendered of SocialShareButtonItem.
	 */
	public static function render_callback($attrs, $content, $block, $elements)
	{
		$parent = BlockParserStore::get_parent($block->parsed_block['id'], $block->parsed_block['storeInstance']);

		$parent_attrs = $parent->attrs ?? [];

		$parent_default_attributes = ModuleRegistration::get_default_attrs('dotm/social-share-buttons');
		$parent_attrs_with_default = array_replace_recursive($parent_default_attributes, $parent_attrs);

		$current_url = esc_url(get_permalink());
		$title = esc_attr(get_the_title());
		$summary = esc_attr(wp_trim_words(get_the_excerpt(), 20));


		$parentBtn_view = $parent_attrs_with_default['general']['advanced']['buttonView']['desktop']['value'] ?? 'text-icon';
		$buttonEffect = $parent_attrs_with_default['general']['advanced']['effect']['desktop']['value'] ?? 'none';
		$iconContent = $attrs['icon']['innerContent']['desktop']['value'] ?? '';
		$button_type = $attrs['button']['advanced']['type']['desktop']['value'] ?? 'facebook';
		$useImageIcon = $attrs['button']['advanced']['useImageIcon']['desktop']['value'] ?? 'off';
		$imageValue = $attrs['image']['innerContent']['desktop']['value']['src'] ?? '';
		$customLable = $attrs['customLabel']['innerContent']['desktop']['value'] ?? '';
		$customUrl = $parent_attrs_with_default['general']['advanced']['customUrl']['desktop']['value'] ?? '';
		$shareUrl = $parent_attrs_with_default['general']['advanced']['shareUrl']['desktop']['value'] ?? 'current';

		$button_url = '';

		if ($shareUrl === 'custom') {
			$button_url = $customUrl;
		} else {
			$button_url = $current_url;
		}

		$iconName = '';

		switch ($button_type) {
			case 'facebook':
				$iconName = '&#xe0aa;';
				break;
			case 'twitter':
				$iconName = '&#xe094;';
				break;
			case 'linkedin':
				$iconName = '&#xe0cb;';
				break;
			case 'reddit':
				$iconName = '&#xf1a1;';
				break;
			case 'vk':
				$iconName = '&#xf189;';
				break;
			case 'tumblr':
				$iconName = '&#xe097;';
				break;
			case 'digg':
				$iconName = '&#xf1a6;';
				break;
			case 'skype':
				$iconName = '&#xe0a2;';
				break;
			case 'stumbleupon':
				$iconName = '&#xf1a3;';
				break;
			case 'mix':
				$iconName = '&#xf3cb;';
				break;
			case 'telegram':
				$iconName = '&#xf2c6;';
				break;
			case 'pocket':
				$iconName = '&#xf265;';
				break;
			case 'xing':
				$iconName = '&#xf168;';
				break;
			case 'whatsapp':
				$iconName = '&#xf232;';
				break;
			case 'email':
				$iconName = '&#xf0e0;';
				break;
			case 'print':
				$iconName = '&#xe0fa;';
				break;
			case 'instagram':
				$iconName = '&#xe09a;';
				break;
			case 'flickr':
				$iconName = '&#xe0bd;';
				break;
			case 'dribbble':
				$iconName = '&#xe0b2;';
				break;
			case 'youtube':
				$iconName = '&#xf167;';
				break;
			case 'vimeo':
				$iconName = '&#xf40a;';
				break;
			case 'pinterest':
				$iconName = '&#xe0ac;';
				break;
			default:
				$iconName = '&#xe0aa;';
				break;
		}

		$iconText = '';

		switch ($button_type) {
			case 'facebook':
				$iconText = 'Facebook';
				break;
			case 'twitter':
				$iconText = 'Twitter';
				break;
			case 'linkedin':
				$iconText = 'LinkedIn';
				break;
			case 'reddit':
				$iconText = 'Reddit';
				break;
			case 'vk':
				$iconText = 'Vk';
				break;
			case 'tumblr':
				$iconText = 'Tumblr';
				break;
			case 'digg':
				$iconText = 'Digg';
				break;
			case 'skype':
				$iconText = 'Skype';
				break;
			case 'stumbleupon':
				$iconText = 'Stumbleupon';
				break;
			case 'mix':
				$iconText = 'Mix';
				break;
			case 'telegram':
				$iconText = 'Telegram';
				break;
			case 'pocket':
				$iconText = 'Pocket';
				break;
			case 'xing':
				$iconText = 'Xing';
				break;
			case 'whatsapp':
				$iconText = 'WhatsApp';
				break;
			case 'email':
				$iconText = 'Email';
				break;
			case 'print':
				$iconText = 'Print';
				break;
			case 'instagram':
				$iconText = 'Instagram';
				break;
			case 'flickr':
				$iconText = 'Flickr';
				break;
			case 'dribbble':
				$iconText = 'Dribbble';
				break;
			case 'youtube':
				$iconText = 'Youtube';
				break;
			case 'vimeo':
				$iconText = 'Vimeo';
				break;
			case 'pinterest':
				$iconText = 'Pinterest';
				break;
			default:
				$iconText = 'Facebook';
				break;
		}

		$social_link = '';

		switch ($button_type) {
			case 'facebook':
				$social_link = "https://www.facebook.com/sharer/sharer.php?u={$button_url}";
				break;
			case 'twitter':
				$social_link = "https://twitter.com/intent/tweet?url={$button_url}&text={$title}";
				break;
			case 'linkedin':
				$social_link = "https://www.linkedin.com/shareArticle?mini=true&url={$button_url}&title={$title}&summary={$summary}";
				break;
			case 'reddit':
				$social_link = "https://reddit.com/submit?url={$button_url}&title={$title}";
				break;
			case 'vk':
				$social_link = "https://vk.com/share.php?url={$button_url}&title={$title}";
				break;
			case 'tumblr':
				$social_link = "https://www.tumblr.com/share/link?url={$button_url}&name={$title}";
				break;
			case 'digg':
				$social_link = "https://digg.com/submit?url={$button_url}&title={$title}";
				break;
			case 'skype':
				$social_link = "https://web.skype.com/share?url={$button_url}&text={$title}";
				break;
			case 'stumbleupon':
				$social_link = "https://www.stumbleupon.com/submit?url={$button_url}&title={$title}";
				break;
			case 'mix':
				$social_link = "https://mix.com/add?url={$button_url}";
				break;
			case 'telegram':
				$social_link = "https://t.me/share/url?url={$button_url}&text={$title}";
				break;
			case 'pocket':
				$social_link = "https://getpocket.com/save?url={$button_url}&title={$title}";
				break;
			case 'xing':
				$social_link = "https://www.xing.com/spi/shares/new?url={$button_url}";
				break;
			case 'whatsapp':
				$social_link = "https://api.whatsapp.com/send?text={$title}%20{$button_url}";
				break;
			case 'email':
				$social_link = "mailto:?subject={$title}&body={$button_url}";
				break;
			case 'print':
				$social_link = "javascript:window.print()";
				break;
			case 'instagram':
				$social_link = "https://www.instagram.com/share?url={$button_url}";
				break;
			case 'flickr':
				$social_link = "https://www.flickr.com/sharing?url={$button_url}";
				break;
			case 'dribbble':
				$social_link = "https://dribbble.com/shots?url={$button_url}";
				break;
			case 'youtube':
				$social_link = "https://www.youtube.com/share?url={$button_url}";
				break;
			case 'vimeo':
				$social_link = "https://vimeo.com/share?url={$button_url}";
				break;
			case 'pinterest':
				$social_link = "https://pinterest.com/pin/create/button/?url={$button_url}&description={$title}";
				break;
			default:
				$social_link = "https://www.facebook.com/sharer/sharer.php?u={$button_url}";
				break;
		}


		$dynamic_text = $elements->render(
			[
				'attrName'      => 'customLabel',
				'hoverSelector' => '{{parentSelector}}',
			]
		);


		$static_text = HTMLUtility::render(
			[
				'tag'               => 'span',
				'attributes'        => [
					'class' => "dotm_optimize_button_item_custom_label",
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $iconText,
			]
		);

		$button_text_container = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => "dotm_social_button_text",
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => (!$customLable ? $static_text : '')  . ($customLable ? $dynamic_text : ''),
			]
		);

		$image = $elements->render(
			[
				'attrName'      => 'image',
				'hoverSelector' => '{{parentSelector}}',
			]
		);



		$dynamic_icon       = HTMLUtility::render(
			[
				'tag'               => 'span',
				'attributes'        => [
					'class' => 'dotm_social_button_custom_icon',
				],
				'childrenSanitizer' => 'esc_html',
				'children'          => Utils::process_font_icon($iconContent),
			]
		);


		$static_icon = HTMLUtility::render(
			[
				'tag'               => 'span',
				'attributes'        => [],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $iconName,
			]
		);

		$button_icon_container = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => "dotm_social_button_icon",
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => ($useImageIcon === 'off' ? $static_icon : '')  . ($useImageIcon === 'on' && $iconContent ? $dynamic_icon : '')  . ($useImageIcon === 'on' && $imageValue ? $image : ''),
			]
		);

		// item container.
		$item_container = HTMLUtility::render(
			[
				'tag'               => 'a',
				'attributes'        => [
					'class' => "dotm_social_button_item dotm_social_button_{$button_type} {$buttonEffect}",
					'href' => $social_link,
					'target' => '_blank',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $button_icon_container . $button_text_container,
			]
		);

		// item2 container.
		$item2_container = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => "dotm_social_button_item dotm_social_button_{$button_type} {$buttonEffect}",
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $button_text_container,
			]
		);

		// item3 container.
		$item3_container = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => "dotm_social_button_item dotm_social_button_{$button_type} {$buttonEffect}",
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $button_icon_container,
			]
		);

		return Module::render(
			[
				// FE only.
				'orderIndex'         => $block->parsed_block['orderIndex'],
				'storeInstance'      => $block->parsed_block['storeInstance'],

				// VB equivalent.
				'id'                 => $block->parsed_block['id'],
				'name'               => $block->block_type->name,
				'moduleCategory'     => $block->block_type->category,
				'attrs'              => $attrs,
				'elements'           => $elements,
				'classnamesFunction' => [SocialShareButtonItem::class, 'module_classnames'],
				'stylesComponent'    => [SocialShareButtonItem::class, 'module_styles'],
				'parentAttrs'        => $parent_attrs,
				'parentId'           => $parent->id ?? '',
				'parentName'         => $parent->blockName ?? '',
				'children'           => ElementComponents::component(
					[
						'attrs'         => $attrs['module']['decoration'] ?? [],
						'id'            => $block->parsed_block['id'],

						// FE only.
						'orderIndex'    => $block->parsed_block['orderIndex'],
						'storeInstance' => $block->parsed_block['storeInstance'],
					]
				) . ($parentBtn_view === 'text-icon' ? $item_container : ($parentBtn_view === 'text-only' ? $item2_container : ($parentBtn_view === 'icon-only' ? $item3_container : ''))),
			]
		);
	}
}
