// WordPress dependencies.
import { __ } from '@wordpress/i18n';

import metadata from './module.json';


const customCssFields = metadata.customCssFields as Record<'contentContainer' | 'title' | 'content' | 'image', { subName: string, selector?: string, selectorSuffix: string, label: string }>;

customCssFields.contentContainer.label = __('Content Container', 'divi-optimaizer-modules');
customCssFields.title.label            = __('Title', 'divi-optimaizer-modules');
customCssFields.content.label          = __('Content', 'divi-optimaizer-modules');
customCssFields.image.label            = __('Image', 'divi-optimaizer-modules');

export const cssFields = { ...customCssFields };
