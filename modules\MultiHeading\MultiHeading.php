<?php

/**
 * Module: Advanced Heading class.
 *
 * @package MEE\Modules\MultiHeading
 * @since ??
 */

namespace MEE\Modules\MultiHeading;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

use ET\Builder\Framework\DependencyManagement\Interfaces\DependencyInterface;
use ET\Builder\Packages\ModuleLibrary\ModuleRegistration;


/**
 * `MultiHeading` is consisted of functions used for Advanced Heading such as Front-End rendering, REST API Endpoints etc.
 *
 * This is a dependency class and can be used as a dependency for `DependencyTree`.
 *
 * @since ??
 */
class MultiHeading implements DependencyInterface
{
	use MultiHeadingTrait\RenderCallbackTrait;
	use MultiHeadingTrait\ModuleClassnamesTrait;
	use MultiHeadingTrait\ModuleStylesTrait;
	use MultiHeadingTrait\ModuleScriptDataTrait;

	/**
	 * Loads `MultiHeading` and registers Front-End render callback and REST API Endpoints.
	 *
	 * @since ??
	 *
	 * @return void
	 */
	public function load()
	{
		$module_json_folder_path = DIVIOPTIMIZER_MODULES_JSON_PATH . 'multi-heading/';

		add_action(
			'init',
			function () use ($module_json_folder_path) {
				ModuleRegistration::register_module(
					$module_json_folder_path,
					[
						'render_callback' => [MultiHeading::class, 'render_callback'],
					]
				);
			}
		);
	}
}
