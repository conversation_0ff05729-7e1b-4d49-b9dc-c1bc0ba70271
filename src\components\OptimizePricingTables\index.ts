// Divi dependencies.
import {
  type Metadata,
  type ModuleLibrary,
} from '@divi/types';

// Local dependencies.
import metadata from './module.json';
import { OptimizePricingTablesEdit } from './edit';
import { SettingsContent } from './settings-content';
import { SettingsDesign } from './settings-design';
import { SettingsAdvanced } from './settings-advanced';
import { OptimizePricingTablesAttrs } from './types';

// Styles.
import './module.scss';

/**
 * Call To Action module.
 *
 * @since ??
 */
export const OptimizePricingTables: ModuleLibrary.Module.RegisterDefinition<OptimizePricingTablesAttrs> = {
  // Imported json has no inferred type hence type-cast is necessary.
  metadata: metadata as Metadata.Values<OptimizePricingTablesAttrs>,
  childrenName: ['dotm/optimize-pricing-tables-item'],
  template:     [
    ['dotm/optimize-pricing-tables-item', {}],
    ['dotm/optimize-pricing-tables-item', {}],
  ],
  settings:   {
    content:  SettingsContent,
    design:   SettingsDesign,
    advanced: SettingsAdvanced,
  },
  renderers: {
    edit: OptimizePricingTablesEdit,
  },
};
