<?php

/**
 * Module: ImageMagnifier class.
 *
 * @package MEE\Modules\ImageMagnifier
 * @since ??
 */

namespace MEE\Modules\ImageMagnifier;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

use ET\Builder\Framework\DependencyManagement\Interfaces\DependencyInterface;
use ET\Builder\Packages\ModuleLibrary\ModuleRegistration;


/**
 * `ImageMagnifier` is consisted of functions used for ImageMagnifier such as Front-End rendering, REST API Endpoints etc.
 *
 * This is a dependency class and can be used as a dependency for `DependencyTree`.
 *
 * @since ??
 */
class ImageMagnifier implements DependencyInterface
{
	use ImageMagnifierTrait\RenderCallbackTrait;
	use ImageMagnifierTrait\ModuleClassnamesTrait;
	use ImageMagnifierTrait\ModuleStylesTrait;
	use ImageMagnifierTrait\ModuleScriptDataTrait;

	/**
	 * Loads `ImageMagnifier` and registers Front-End render callback and REST API Endpoints.
	 *
	 * @since ??
	 *
	 * @return void
	 */
	public function load()
	{
		$module_json_folder_path = DIVIOPTIMIZER_MODULES_JSON_PATH . 'ImageMagnifier/';

		add_action(
			'init',
			function () use ($module_json_folder_path) {
				ModuleRegistration::register_module(
					$module_json_folder_path,
					[
						'render_callback' => [ImageMagnifier::class, 'render_callback'],
					]
				);
			}
		);
	}
}
