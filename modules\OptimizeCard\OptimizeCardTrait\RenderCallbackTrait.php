<?php

/**
 * OptimizeCard::render_callback()
 *
 * @package MEE\Modules\OptimizeCard
 * @since ??
 */

namespace MEE\Modules\OptimizeCard\OptimizeCardTrait;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

// phpcs:disable ET.Sniffs.ValidVariableName.UsedPropertyNotSnakeCase -- WP use snakeCase in \WP_Block_Parser_Block

use ET\Builder\Packages\Module\Module;
use ET\Builder\Framework\Utility\HTMLUtility;
use ET\Builder\FrontEnd\BlockParser\BlockParserStore;
use ET\Builder\Packages\Module\Options\Element\ElementComponents;
use MEE\Modules\OptimizeCard\OptimizeCard;
use ET\Builder\Packages\IconLibrary\IconFont\Utils;

trait RenderCallbackTrait
{

	/**
	 * OptimizeCard render callback which outputs server side rendered HTML on the Front-End.
	 *
	 * @since ??
	 * @param array          $attrs    Block attributes that were saved by VB.
	 * @param string         $content  Block content.
	 * @param WP_Block       $block    Parsed block object that being rendered.
	 * @param ModuleElements $elements ModuleElements instance.
	 *
	 * @return string HTML rendered of Static module.
	 */
	public static function render_callback($attrs, $content, $block, $elements)
	{
		$icon = $attrs['icon']['innerContent']['desktop']['value'] ?? [];
		$use_btn = $attrs['btn']['advanced']['use']['desktop']['value'] ?? '';
		$use_icon = $attrs['icon']['advanced']['useIcon']['desktop']['value'] ?? 'off';
		$use_url = $attrs['btn']['advanced']['link']['desktop']['value']['url'] ?? '';
		$use_target = $attrs['btn']['advanced']['link']['desktop']['value']['target'] ?? '';
		$use_sub_title = $attrs['sub_title']['advanced']['use']['desktop']['value'] ?? '';
		$use_image_label = $attrs['image']['advanced']['use_image_label']['desktop']['value'] ?? '';
		$use_hover_effect = $attrs['image']['advanced']['use_hover_effect']['desktop']['value'] ?? '';
		$hover_effect = $use_hover_effect === 'on' ? $attrs['image']['decoration']['hover_effect']['desktop']['value'] ?? '' : '';
		$use_mask = $attrs['image']['advanced']['use_mask']['desktop']['value'] ?? '';
		$shape_name = $use_mask === 'on' ? $attrs['image']['decoration']['shape_name']['desktop']['value'] ?? '' : '';
		$card_effect = $attrs['contentContainer']['decoration']['card_effect']['desktop']['value'] ?? '';


		// image_label.
		$image_label = $elements->render(
			[
				'attrName' => 'image_label',
			]
		);

		// Image.
		$image = $elements->render(
			[
				'attrName' => 'image',
			]
		);



		// Image container.
		$image_container = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => "dotm_optimize_card_image_container {$hover_effect} {$shape_name}",
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => ($use_image_label === 'on' ? $image_label : '') . $image,
			]
		);

		// Image wrapper.
		$image_wrapper = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'dotm_optimize_card_inner_image_wrapper',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $image_container,
			]
		);

		$icon = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => "dotm_optimize_card_icon",
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => Utils::process_font_icon($icon),
			]
		);

		$icon_container = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => "dotm_optimize_card_icon_container",
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $icon,
			]
		);

		// Title.
		$title = $elements->render(
			[
				'attrName' => 'title',
			]
		);

		// sub_title.
		$sub_title = $elements->render(
			[
				'attrName' => 'sub_title',
			]
		);



		// Content.
		$content = $elements->render(
			[
				'attrName' => 'content',
			]
		);

		// btn.
		$btn = $elements->render(
			[
				'attrName' => 'btn',
				'attributes' => [
					'class' => 'dotm_optimize_card_btn',
					'href' => $use_url,
					'target' => $use_target === 'on' ? '_blank' : '_self',
				]
			]
		);

		// Button container.
		$button_container = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'dotm_optimize_card_btn_container',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $btn,
			]
		);

		// Content container.
		$content_container = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'dotm_optimize_card_content_container',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $title . ($use_sub_title === 'on' ? $sub_title : '') . $content . ($use_btn === 'on' ? $button_container : ''),
			]
		);

		$parent       = BlockParserStore::get_parent($block->parsed_block['id'], $block->parsed_block['storeInstance']);
		$parent_attrs = $parent->attrs ?? [];

		return Module::render(
			[
				// FE only.
				'orderIndex'          => $block->parsed_block['orderIndex'],
				'storeInstance'       => $block->parsed_block['storeInstance'],

				// VB equivalent.
				'attrs'               => $attrs,
				'elements'            => $elements,
				'id'                  => $block->parsed_block['id'],
				'name'                => $block->block_type->name,
				'moduleCategory'      => $block->block_type->category,
				'classnamesFunction'  => [OptimizeCard::class, 'module_classnames'],
				'stylesComponent'     => [OptimizeCard::class, 'module_styles'],
				'scriptDataComponent' => [OptimizeCard::class, 'module_script_data'],
				'parentAttrs'         => $parent_attrs,
				'parentId'            => $parent->id ?? '',
				'parentName'          => $parent->blockName ?? '',
				'children'            => [
					ElementComponents::component(
						[
							'attrs'         => $attrs['module']['decoration'] ?? [],
							'id'            => $block->parsed_block['id'],

							// FE only.
							'orderIndex'    => $block->parsed_block['orderIndex'],
							'storeInstance' => $block->parsed_block['storeInstance'],
						]
					),
					HTMLUtility::render(
						[
							'tag'               => 'div',
							'attributes'        => [
								'class' => "dotm_optimize_card_container dotm_optimize_card_{$card_effect}",
							],
							'childrenSanitizer' => 'et_core_esc_previously',
							'children'          => ($use_icon === 'off' ? $image_wrapper : '') . ($use_icon === 'on' ? $icon_container : '') . $content_container,
						]
					),
				],
			]
		);
	}
}
