// Divi dependencies.
import {placeholderContent as placeholder} from '@divi/module';

// Local dependencies.
import {BlockRevealTextAttrs} from './types';
import { getPlaceholderTitle, getPlaceholderBody, getPlaceholderImage } from '../../utils/placeholder-fallbacks';


export const placeholderContent: BlockRevealTextAttrs = {
  revealText: {
    innerContent: {
      desktop: {
        value: getPlaceholderTitle(placeholder, 'Your Title Goes Here'),
      },
    }
  },
  content: {
    innerContent: {
      desktop: {
        value: getPlaceholderBody(placeholder, 'Your content goes here.'),
      },
    }
  },
  image: {
    innerContent: {
      desktop: {
        value: {
          src: getPlaceholderImage(placeholder, 'landscape'),
        },
      },
    },
  },
};
