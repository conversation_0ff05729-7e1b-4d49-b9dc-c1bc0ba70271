// Divi dependencies.
import { placeholderContent as placeholder } from "@divi/module";

// Local dependencies.
import { OptimizePricingTablesItemAttrs } from "./types";
import {
  getPlaceholderTitle,
  getPlaceholderBody,
  getPlaceholderImage,
} from "../../utils/placeholder-fallbacks";

export const placeholderContent: OptimizePricingTablesItemAttrs = {
  title: {
    innerContent: {
      desktop: {
        value: getPlaceholderTitle(placeholder, "Table Title"),
      },
    },
  },
  subTitle: {
    innerContent: {
      desktop: {
        value: "Subtitle gores here",
      },
    },
  },
  price: {
    innerContent: {
      desktop: {
        value: "50",
      },
    },
  },
  frequency: {
    innerContent: {
      desktop: {
        value: "per year",
      },
    },
  },
  content: {
    innerContent: {
      desktop: {
        value: getPlaceholderBody(
          placeholder,
          "Hi everyone, Divi Optimizer has 55+ modules that come in handy when you want to create a responsive WordPress Website."
        ),
      },
    },
  },
};
