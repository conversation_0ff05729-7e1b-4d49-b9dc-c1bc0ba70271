<?php

/**
 * Module: Static Module class.
 *
 * @package MEE\Modules\MaskText
 * @since ??
 */

namespace MEE\Modules\MaskText;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

use ET\Builder\Framework\DependencyManagement\Interfaces\DependencyInterface;
use ET\Builder\Packages\ModuleLibrary\ModuleRegistration;


/**
 * `MaskText` is consisted of functions used for Static Module such as Front-End rendering, REST API Endpoints etc.
 *
 * This is a dependency class and can be used as a dependency for `DependencyTree`.
 *
 * @since ??
 */
class MaskText implements DependencyInterface
{
	use MaskTextTrait\RenderCallbackTrait;
	use MaskTextTrait\ModuleClassnamesTrait;
	use MaskTextTrait\ModuleStylesTrait;
	use MaskTextTrait\ModuleScriptDataTrait;

	/**
	 * Loads `MaskText` and registers Front-End render callback and REST API Endpoints.
	 *
	 * @since ??
	 *
	 * @return void
	 */
	public function load()
	{
		$module_json_folder_path = DIVIOPTIMIZER_MODULES_JSON_PATH . 'MaskText/';	
		add_action(
			'init',
			function () use ($module_json_folder_path) {
				ModuleRegistration::register_module(
					$module_json_folder_path,
					[
						'render_callback' => [MaskText::class, 'render_callback'],
					]
				);
			}
		);
	}
}
