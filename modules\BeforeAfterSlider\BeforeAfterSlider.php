<?php

/**
 * Module: BeforeAfterSlider class.
 *
 * @package MEE\Modules\BeforeAfterSlider
 * @since ??
 */

namespace MEE\Modules\BeforeAfterSlider;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

use ET\Builder\Framework\DependencyManagement\Interfaces\DependencyInterface;
use ET\Builder\Packages\ModuleLibrary\ModuleRegistration;


/**
 * `BeforeAfterSlider` is consisted of functions used for Static Module such as Front-End rendering, REST API Endpoints etc.
 *
 * This is a dependency class and can be used as a dependency for `DependencyTree`.
 *
 * @since ??
 */
class BeforeAfterSlider implements DependencyInterface
{
	use BeforeAfterSliderTrait\RenderCallbackTrait;
	use BeforeAfterSliderTrait\ModuleClassnamesTrait;
	use BeforeAfterSliderTrait\ModuleStylesTrait;
	use BeforeAfterSliderTrait\ModuleScriptDataTrait;

	/**
	 * Loads `BeforeAfterSlider` and registers Front-End render callback and REST API Endpoints.
	 *
	 * @since ??
	 *
	 * @return void
	 */
	public function load()
	{
		$module_json_folder_path = DIVIOPTIMIZER_MODULES_JSON_PATH . 'BeforeAfterSlider/';

		add_action(
			'init',
			function () use ($module_json_folder_path) {
				ModuleRegistration::register_module(
					$module_json_folder_path,
					[
						'render_callback' => [BeforeAfterSlider::class, 'render_callback'],
					]
				);
			}
		);
	}
}
