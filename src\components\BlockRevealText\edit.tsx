// External Dependencies.
import React, { ReactElement, useEffect } from 'react';
import classnames from 'classnames';

// Divi Dependencies.
import { ModuleContainer } from '@divi/module';

// Local Dependencies.
import { BlockRevealTextEditProps } from './types';
import { ModuleStyles } from './styles';
import { moduleClassnames } from './module-classnames';
import { ModuleScriptData } from './module-script-data';
import {revealText} from './script/textReveal'

/**
 * Static Module edit component of visual builder.
 *
 * @since ??
 *
 * @param {BlockRevealTextEditProps} props React component props.
 *
 * @returns {ReactElement}
 */
export const BlockRevealTextEdit = (props: BlockRevealTextEditProps): ReactElement => {
  const {
    attrs,
    elements,
    id,
    name,
  } = props;

  console.log(attrs);
  const revealDirection = attrs?.revealAnimation?.advanced?.type?.desktop?.value ?? '';
  const animationDelay = attrs?.revealAnimation?.advanced?.revealDelay?.desktop?.value ?? '';

  useEffect(() => {
    revealText({
      animationDelay
    });
    }, [animationDelay]);

  return (
    <ModuleContainer
      attrs={attrs}
      elements={elements}
      id={id}
      name={name}
      stylesComponent={ModuleStyles}
      classnamesFunction={moduleClassnames}
      scriptDataComponent={ModuleScriptData}
    >
      {elements.styleComponents({
        attrName: 'module',
      })}
      <div className="dotm_block_text_reveal_container">
        <div className="dotm_block_text_reveal_wrapper">
          {elements.render({
            attrName: 'revealText',
          })}
          <div className={`dotm_block_text_reveal_reveal dotm_optimize_image_reveal_${revealDirection}`} ></div>
        </div>
      </div>
    </ModuleContainer>
  );
};
