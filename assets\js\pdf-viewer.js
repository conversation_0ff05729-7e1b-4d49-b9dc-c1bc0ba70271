// export default class PDFViewer {
//      constructor(pdfContainer) {
//        this.pdfDoc = null;
//        this.currentPage = 1;
//        this.zoomLevel = 1.0;
//        this.rotation = 0;
//        this.pageCanvases = new Map();
//        this.pageScales = new Map();
//        this.containerWidth = 0;
//        this.pdfContainer = pdfContainer;
   
//        this.initializeElements();
//        this.addEventListeners();
//      }
   
//      initializeElements() {
//        this.prevBtn = document.getElementById('prev-page');
//        this.nextBtn = document.getElementById('next-page');
//        this.currentPageInput = document.getElementById('current-page');
//        this.pageCount = document.getElementById('page-count');
//        this.zoomIn = document.getElementById('zoom-in');
//        this.zoomOut = document.getElementById('zoom-out');
//        this.zoomText = document.getElementById('zoom-level');
//        this.rotateBtn = document.getElementById('rotate-btn');
//        this.printBtn = document.getElementById('print-btn');
//        this.downloadBtn = document.getElementById('download-btn');
//        this.loading = document.querySelector('.loading');
//      }
   
//      addEventListeners() {
//        this.prevBtn.addEventListener('click', () => this.changePage(-1));
//        this.nextBtn.addEventListener('click', () => this.changePage(1));
//        this.currentPageInput.addEventListener('change', () => this.goToPage());
//        this.zoomIn.addEventListener('click', () => this.zoom(0.1));
//        this.zoomOut.addEventListener('click', () => this.zoom(-0.1));
//        this.rotateBtn.addEventListener('click', () => this.rotate());
//        this.printBtn.addEventListener('click', () => this.printPDF());
//        this.downloadBtn.addEventListener('click', () => this.downloadPDF());
//      }

     
   
//      async loadPDF(url) {
//        try {
//          this.loading.style.display = 'block';
//          const loadingTask = pdfjsLib.getDocument(url);
   
//          this.pdfDoc = await loadingTask.promise;
//          console.log('PDF loaded successfully.',this.pdfDoc);
//          this.pageCount.textContent = this.pdfDoc.numPages;
   
//          this.pdfContainer.innerHTML = '';
//          this.containerWidth = this.pdfContainer.clientWidth - 40;
   
//          for (let i = 1; i <= this.pdfDoc.numPages; i++) {
//            const pageDiv = document.createElement('div');
//            pageDiv.className = 'pdf-page';
//            pageDiv.id = `page-${i}`;
   
//            const canvas = document.createElement('canvas');
//            canvas.id = `canvas-${i}`;
//            pageDiv.appendChild(canvas);
//            this.pdfContainer.appendChild(pageDiv);
//            this.pageCanvases.set(i, canvas);
//          }
   
//          this.renderPage(1);
//        } catch (error) {
//          console.error('Error loading PDF:', error);
//          alert('Error loading PDF document. Please try again.');
//        } finally {
//          this.loading.style.display = 'none';
//        }
//      }
   
//      async renderPage(pageNumber) {
//        if (!this.pdfDoc) return;
   
//        try {
//          const page = await this.pdfDoc.getPage(pageNumber);
//          const canvas = this.pageCanvases.get(pageNumber);
//          const context = canvas.getContext('2d');
   
//          const originalViewport = page.getViewport({ scale: 1, rotation: this.rotation });
//          const baseScale = (this.containerWidth * 0.9) / originalViewport.width;
//          const finalScale = baseScale * this.zoomLevel;
   
//          const viewport = page.getViewport({
//            scale: finalScale,
//            rotation: this.rotation
//          });
   
//          canvas.height = viewport.height;
//          canvas.width = viewport.width;
   
//          context.clearRect(0, 0, canvas.width, canvas.height);
   
//          await page.render({
//            canvasContext: context,
//            viewport: viewport
//          }).promise;
//        } catch (error) {
//          console.error(`Error rendering page ${pageNumber}:`, error);
//        }
//      }
   
//      changePage(offset) {
//        const newPage = this.currentPage + offset;
//        if (newPage >= 1 && newPage <= this.pdfDoc.numPages) {
//          this.currentPage = newPage;
//          this.currentPageInput.value = this.currentPage;
//          this.renderPage(this.currentPage);
//        }
//      }
   
//      goToPage() {
//        const page = parseInt(this.currentPageInput.value);
//        if (page >= 1 && page <= this.pdfDoc.numPages) {
//          this.currentPage = page;
//          this.renderPage(this.currentPage);
//        }
//      }
   
//      zoom(delta) {
//        const newZoom = Math.max(0.1, Math.min(3.0, this.zoomLevel + delta));
//        if (newZoom !== this.zoomLevel) {
//          this.zoomLevel = newZoom;
//          this.zoomText.textContent = `${Math.round(this.zoomLevel * 100)}%`;
//          this.renderPage(this.currentPage);
//        }
//      }
   
//      rotate() {
//        this.rotation = (this.rotation + 90) % 360;
//        this.renderPage(this.currentPage);
//      }
   
//      printPDF() {
//        window.print();
//      }
   
//      async downloadPDF() {
//        if (this.pdfDoc) {
//          try {
//            const blob = await fetch(this.pdfDoc.url).then(res => res.blob());
//            const url = window.URL.createObjectURL(blob);
//            const a = document.createElement('a');
//            a.href = url;
//            a.download = 'document.pdf';
//            document.body.appendChild(a);
//            a.click();
//            document.body.removeChild(a);
//            window.URL.revokeObjectURL(url);
//          } catch (error) {
//            console.error('Error downloading PDF:', error);
//            alert('Error downloading PDF. Please try again.');
//          }
//        }
//      }
//    }
   


// function pdfViewer(url) {

//     alert('Error loading PDF document. Please try again.');
//     console.log('PDF URL:', url);
//     let pdfDoc = null,
//         pageNum = 1,
//         pageRendering = false,
//         pageNumPending = null,
//         scale = 1,
//         canvas = document.getElementById('pdf-canvas'),
//         ctx = canvas.getContext('2d');
  
//     // Load the PDF document
//     pdfjsLib.getDocument(url).promise.then(doc => {
//       pdfDoc = doc;
//       document.getElementById('page-count').textContent = pdfDoc.numPages;
//       renderPage(pageNum);
  
//       // Enable scrolling to change pages
//       const pdfContainer = document.getElementById('pdf-container');
//       pdfContainer.addEventListener('scroll', handleScroll);
//     }).catch(err => {
//       console.error('Error loading PDF:', err);
//     });
  
//     // Render a specific page
//     function renderPage(num) {
//       pageRendering = true;
//       pdfDoc.getPage(num).then(page => {
//         const viewport = page.getViewport({ scale: scale });
//         canvas.height = viewport.height;
//         canvas.width = viewport.width;
  
//         const renderContext = {
//           canvasContext: ctx,
//           viewport: viewport
//         };
//         page.render(renderContext).promise.then(() => {
//           pageRendering = false;
//           if (pageNumPending !== null) {
//             renderPage(pageNumPending);
//             pageNumPending = null;
//           }
//         });
  
//         document.getElementById('page-num').textContent = num;
//         document.getElementById('zoom-level').textContent = `${Math.round(scale * 100)}%`;
//       }).catch(err => {
//         console.error('Error rendering page:', err);
//       });
//     }
  
//     // Queue a page render if another page is currently rendering
//     function queueRenderPage(num) {
//       if (pageRendering) {
//         pageNumPending = num;
//       } else {
//         renderPage(num);
//       }
//     }
  
//     // Handle scroll events to change pages
//     function handleScroll() {
//       const pdfContainer = document.getElementById('pdf-container');
//       const { scrollTop, scrollHeight, clientHeight } = pdfContainer;
//       const scrollThreshold = 50; // Threshold in pixels to trigger page change
  
//       // Scroll to bottom: Go to next page
//       if (scrollTop + clientHeight >= scrollHeight - scrollThreshold && pageNum < pdfDoc.numPages) {
//         pageNum++;
//         queueRenderPage(pageNum);
//         pdfContainer.scrollTo(0, 0); // Reset scroll position to top
//       }
//       // Scroll to top: Go to previous page
//       else if (scrollTop <= scrollThreshold && pageNum > 1) {
//         pageNum--;
//         queueRenderPage(pageNum);
//         pdfContainer.scrollTo(0, 0); // Reset scroll position to top
//       }
//     }
  
//     // Event listeners for toolbar buttons
//     document.getElementById('prev-page').addEventListener('click', () => {
//       if (pageNum <= 1) return;
//       pageNum--;
//       queueRenderPage(pageNum);
//       document.getElementById('pdf-container').scrollTo(0, 0);
//     });
  
//     document.getElementById('next-page').addEventListener('click', () => {
//       if (pageNum >= pdfDoc.numPages) return;
//       pageNum++;
//       queueRenderPage(pageNum);
//       document.getElementById('pdf-container').scrollTo(0, 0);
//     });
  
//     document.getElementById('zoom-in').addEventListener('click', () => {
//       scale += 0.1;
//       queueRenderPage(pageNum);
//     });
  
//     document.getElementById('zoom-out').addEventListener('click', () => {
//       if (scale <= 0.1) return;
//       scale -= 0.1;
//       queueRenderPage(pageNum);
//     });
  
//     document.getElementById('download-pdf').addEventListener('click', () => {
//       pdfjsLib.getDocument(url).promise.then(pdf => {
//         pdf.getData().then(data => {
//           const blob = new Blob([data], { type: 'application/pdf' });
//           const link = document.createElement('a');
//           link.href = URL.createObjectURL(blob);
//           link.download = 'downloaded.pdf';
//           link.click();
//         }).catch(err => {
//           console.error('Error getting PDF data:', err);
//         });
//       }).catch(err => {
//         console.error('Error downloading PDF:', err);
//       });
//     });
//   }


function pdfViewer(url) {
  let pdfDoc = null,
      pageNum = 1,
      pageRendering = false,
      pageNumPending = null,
      scale = 1,
      rotation = 0,
      canvas = document.getElementById('pdf-canvas'),
      ctx = canvas.getContext('2d');

  // Error handling
  const showError = (message) => {
      alert(message);
      console.error(message);
  };

  // Load PDF document
  pdfjsLib.getDocument(url).promise.then(doc => {
      pdfDoc = doc;
      document.getElementById('page-count').textContent = pdfDoc.numPages;
      renderPage(pageNum);

      // Handle scroll events
      const pdfContainer = document.getElementById('pdf-container');
      pdfContainer.addEventListener('scroll', handleScroll);

  }).catch(err => {
      showError('Error loading PDF document');
  });

  function renderPage(num) {
      pageRendering = true;
      pdfDoc.getPage(num).then(page => {
          const viewport = page.getViewport({ 
              scale: scale,
              rotation: rotation
          });
          
          canvas.height = viewport.height;
          canvas.width = viewport.width;

          const renderContext = {
              canvasContext: ctx,
              viewport: viewport
          };

          page.render(renderContext).promise.then(() => {
              pageRendering = false;
              if (pageNumPending !== null) {
                  renderPage(pageNumPending);
                  pageNumPending = null;
              }
          });

          // Update UI
          document.getElementById('page-num').textContent = num;
          document.getElementById('zoom-level').textContent = `${Math.round(scale * 100)}%`;

      }).catch(err => {
          showError('Error rendering page');
      });
  }

  function queueRenderPage(num) {
      if (pageRendering) {
          pageNumPending = num;
      } else {
          renderPage(num);
      }
  }

  // Scroll handling
  function handleScroll() {
      const container = document.getElementById('pdf-container');
      const { scrollTop, scrollHeight, clientHeight } = container;
      const threshold = 50;

      if (scrollTop + clientHeight >= scrollHeight - threshold && pageNum < pdfDoc.numPages) {
          pageNum++;
          queueRenderPage(pageNum);
          container.scrollTo(0, 0);
      } else if (scrollTop <= threshold && pageNum > 1) {
          pageNum--;
          queueRenderPage(pageNum);
          container.scrollTo(0, 0);
      }
  }

  // Event Listeners
  document.getElementById('prev-page').addEventListener('click', () => {
      if (pageNum <= 1) return;
      pageNum--;
      queueRenderPage(pageNum);
      document.getElementById('pdf-container').scrollTo(0, 0);
  });

  document.getElementById('next-page').addEventListener('click', () => {
      if (pageNum >= pdfDoc.numPages) return;
      pageNum++;
      queueRenderPage(pageNum);
      document.getElementById('pdf-container').scrollTo(0, 0);
  });

  document.getElementById('zoom-in').addEventListener('click', () => {
      scale += 0.1;
      queueRenderPage(pageNum);
  });

  document.getElementById('zoom-out').addEventListener('click', () => {
      if (scale <= 0.2) return;
      scale -= 0.1;
      queueRenderPage(pageNum);
  });

  document.getElementById('rotate').addEventListener('click', () => {
      rotation = (rotation + 90) % 360;
      queueRenderPage(pageNum);
  });

  // Download functionality (your working version)
  document.getElementById('download-pdf').addEventListener('click', () => {
      pdfjsLib.getDocument(url).promise.then(pdf => {
          pdf.getData().then(data => {
              const blob = new Blob([data], { type: 'application/pdf' });
              const link = document.createElement('a');
              link.href = URL.createObjectURL(blob);
              link.download = 'document.pdf';
              document.body.appendChild(link);
              link.click();
              document.body.removeChild(link);
              URL.revokeObjectURL(link.href);
          }).catch(err => {
              showError('Error generating PDF data');
          });
      }).catch(err => {
          showError('Error loading PDF for download');
      });
  });

  // Print functionality
  document.getElementById('print-pdf').addEventListener('click', () => {
      const printWindow = window.open(url);
      if (printWindow) {
          printWindow.onload = () => {
              printWindow.print();
          };
      } else {
          showError('Please allow popups for printing');
      }
  });
}