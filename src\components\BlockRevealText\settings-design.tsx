// External dependencies.
import React, { ReactElement } from 'react';

// WordPress dependencies.
import { __ } from '@wordpress/i18n';

// Divi dependencies.
import {
  AnimationGroup,
  BorderGroup,
  BoxShadowGroup,
  FiltersGroup,
  FontGroup,
  FontBodyGroup,
  SizingGroup,
  SpacingGroup,
  TextGroup,
  TransformGroup,
} from '@divi/module';
import {
  type Module,
} from '@divi/types';
import { GroupContainer } from '@divi/modal';

// Local dependencies.
import {BlockRevealTextAttrs} from "./types";

export const SettingsDesign = ({
   defaultSettingsAttrs,
 }: Module.Settings.Panel.Props<BlockRevealTextAttrs>): ReactElement => (
  <React.Fragment>
    <FontGroup 
      attrName='revealText.decoration.font'
      defaultGroupAttr={defaultSettingsAttrs?.revealText?.decoration?.font?.asMutable({ deep: true }) ?? {}}
      groupLabel='Reveal Text'
      fields={{
        headingLevel: {
          render: false,
        },
        "textAlign": {
          render: false,
        },
      }}

    />
    <TextGroup
      groupLabel='Alignment'
      defaultGroupAttr={defaultSettingsAttrs?.module?.advanced?.text}
      fields={{
        textShadowGroup: {
          render: false,
        },
        color: {
          render: false,
        },
      }}
    />
    <SizingGroup  />
    <SpacingGroup attrName='revealAnimation.decoration.spacing' />
    <BorderGroup attrName='revealAnimation.decoration.border' />
    <BoxShadowGroup attrName='revealAnimation.decoration.boxShadow' />
    {/* <FiltersGroup /> */}
    <TransformGroup />
    <AnimationGroup />
  </React.Fragment>
);
