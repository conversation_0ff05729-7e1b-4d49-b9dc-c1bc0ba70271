<?php

/**
 * SocialShareButtons::module_styles().
 *
 * @package MEE\Modules\SocialShareButtons
 * @since ??
 */

namespace MEE\Modules\SocialShareButtons\SocialShareButtonsTrait;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

use ET\Builder\FrontEnd\Module\Style;
use ET\Builder\Packages\Module\Layout\Components\StyleCommon\CommonStyle;
use ET\Builder\Packages\Module\Options\Css\CssStyle;
use MEE\Modules\SocialShareButtonItem\SocialShareButtonItem;

trait ModuleStylesTrait
{

	use CustomCssTrait;

	/**
	 * Child Module's style components.
	 *
	 * This function is equivalent of JS function ModuleStyles located in
	 * src/components/SocialShareButtons/styles.tsx.
	 *
	 * @param array $args {
	 *     An array of arguments.
	 *
	 * @type string $id Module ID. In VB, the ID of module is UUIDV4. In FE, the ID is order index.
	 * @type string $name Module name.
	 * @type string $attrs Module attributes.
	 * @type string $parentAttrs Parent attrs.
	 * @type string $orderClass Selector class name.
	 * @type string $parentOrderClass Parent selector class name.
	 * @type string $wrapperOrderClass Wrapper selector class name.
	 * @type string $settings Custom settings.
	 * @type string $state Attributes state.
	 * @type string $mode Style mode.
	 * @type ModuleElements $elements ModuleElements instance.
	 * }
	 * @since ??
	 */
	public static function module_styles($args)
	{
		$attrs        = $args['attrs'] ?? [];
		$parent_attrs = $args['parentAttrs'] ?? [];
		$order_class  = $args['orderClass'];
		$elements     = $args['elements'];
		$settings     = $args['settings'] ?? [];
		$orderClass = $args['orderClass'];

		$btnContainerSelector = "{$orderClass} .dotm_social_share_buttons_wrapper";
		$btnTextSelector = "{$orderClass} .dotm_social_button_text";
		$btnsItemSelector = "{$orderClass} .dotm_social_button_item";
		$buttonIconLabelSelector = "{$orderClass} .dotm_social_button_item span, {$orderClass} .dotm_social_button_icon span";

		$buttonView = $attrs['general']['advanced']['buttonView']['desktop']['value'] ?? '';
		$btnStyle = $attrs['general']['advanced']['buttonStyle']['desktop']['value'] ?? 'gradient ';
		$buttonCorners = $attrs['general']['advanced']['buttonCorners']['desktop']['value'] ?? 'square ';
		$buttonColumn = $attrs['general']['advanced']['columns']['desktop']['value'] ?? 'auto';
		$buttonColGap = $attrs['general']['advanced']['colGap']['desktop']['value'] ?? '';
		$buttonRowGap = $attrs['general']['advanced']['rowGap']['desktop']['value'] ?? '';
		$color_type = $attrs['button']['advanced']['color_type']['desktop']['value'] ?? '';
		$childLabelColor = $attrs['customLabel']['decoration']['font']['font']['desktop']['value']['color'] ?? '';

		Style::add(
			[
				'id'            => $args['id'],
				'name'          => $args['name'],
				'orderIndex'    => $args['orderIndex'],
				'storeInstance' => $args['storeInstance'],
				'styles'        => [
					// Module.
					$elements->style(
						[
							'attrName'   => 'module',
							'styleProps' => [
								'disabledOn' => [
									'disabledModuleVisibility' => $settings['disabledModuleVisibility'] ?? null,
								],
								'advancedStyles' => [
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $btnTextSelector,
											'attr'     => $attrs['general']['advanced']['buttonStyle'],
											'property' => ($btnStyle === 'gradient'
												? "background-image: linear-gradient(90deg, rgba(0, 0, 0, .12), transparent); color: #fff;"
												: ($btnStyle === 'boxed-icon'
													? 'background-color: #fff; color: #000;'
													: ($btnStyle === 'minimal'
														? 'background-color: #fff; color: #000;'
														: ($btnStyle === 'flat' && $buttonView !== "text-only"
															? 'padding-left: 0px;'
															: ($btnStyle === 'flat'
																? 'color: #fff;'
																: ($btnStyle === 'framed' && $buttonView !== "text-only"
																	? 'padding-left: 0px;'
																	: ''
																)
															)
														)
													)
												)
											)
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $btnsItemSelector,
											'attr'     => $attrs['general']['advanced']['buttonStyle'],
											'property' => ($btnStyle === 'boxed-icon'
												? "border-style: solid; border-width: 2px;"
												: ($btnStyle === 'framed'
													? 'background-color: #fff; border-style: solid; border-width: 2px;'
													: ''
												)
											)
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $btnsItemSelector,
											'attr'     => $attrs['general']['advanced']['buttonCorners'],
											'property' => ($buttonCorners === 'circle'
												? "border-radius: 100px;"
												: ($buttonCorners === 'rounded'
													? 'border-radius: 5px;'
													: ''
												)
											)
										]
										],
										[
											'componentName' => 'divi/common',
											'props'         => [
												'selector' => $btnContainerSelector,
												'attr'     => $attrs['general']['advanced']['columns'],
												'property' => ($buttonColumn === 'auto'
													? "display: inline-flex; flex-wrap: wrap; gap: 10px; row-gap: 10px;"
													: "display: grid;     grid-template-columns: repeat({$buttonColumn}, 1fr); grid-row-gap: {$buttonRowGap};  grid-column-gap: {$buttonColGap};"
												)
											]
										]
								]
							],
						]
					),

					// customLabel.
					$elements->style(
						[
							'attrName' => 'customLabel',
						]
					),

					// button.
					$elements->style(
						[
							'attrName'   => 'button',
							'styleProps' => [
								'advancedStyles' => [
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $btnsItemSelector,
											'attr'                => $attrs['button']['advanced']['BGColor'] ?? [],
											'property'           => $color_type === 'custom' ? "background-color" : '',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $buttonIconLabelSelector,
											'attr'                => $attrs['button']['advanced']['labelColor'] ?? [],
											'property'           => $color_type === 'custom' && !$childLabelColor ? "color" : '',
										]
									],
									
								]
							]
						]
					),

					/*
					 * We need to add CssStyle at the very bottom of other
					 * components so that custom css can override module styles
					 * till we find a more elegant solution.
					 */
					CssStyle::style(
						[
							'selector'  => $args['orderClass'],
							'attr'      => $attrs['css'] ?? [],
							'cssFields' => self::custom_css(),
						]
					),

					// ATTENTION: The code is intentionally added and commented in FE only as an example of expected value format.
					// If you have custom style processing, the style output should be passed as an `array` of style declarations
					// to the `styles` property of the `Style::add` method. For example:
					// [
					// 	[
					// 		'atRules'     => false,
					// 		'selector'    => $icon_selector,
					// 		'declaration' => 'color: red;'
					// 	],
					// 	[
					// 		'atRules'     => '@media only screen and (max-width: 767px)',
					// 		'selector'    => $icon_selector,
					// 		'declaration' => 'color: green;'
					// 	],
					// ],

					// The code below is an example of how to use the `CssStyle::style` method to generate CSS style.
					CssStyle::style(
						[
							'selector'  => $args['orderClass'],
							'attr'      => $attrs['css'] ?? [],
							'cssFields' => self::custom_css(),
						]
					),
				],
			]
		);
	}
}
