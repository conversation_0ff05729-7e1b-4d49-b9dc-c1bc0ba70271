<?php
/**
 * TextDivider::render_callback()
 *
 * @package MEE\Modules\TextDivider
 * @since ??
 */

namespace MEE\Modules\TextDivider\TextDividerTrait;

if ( ! defined( 'ABSPATH' ) ) {
	die( 'Direct access forbidden.' );
}

// phpcs:disable ET.Sniffs.ValidVariableName.UsedPropertyNotSnakeCase -- WP use snakeCase in \WP_Block_Parser_Block

use ET\Builder\Packages\Module\Module;
use ET\Builder\Framework\Utility\HTMLUtility;
use ET\Builder\FrontEnd\BlockParser\BlockParserStore;
use ET\Builder\Packages\Module\Options\Element\ElementComponents;
use MEE\Modules\TextDivider\TextDivider;
use ET\Builder\Packages\IconLibrary\IconFont\Utils;
trait RenderCallbackTrait {

	/**
	 * TextDivider render callback which outputs server side rendered HTML on the Front-End.
	 *
	 * @since ??
	 * @param array          $attrs    Block attributes that were saved by VB.
	 * @param string         $content  Block content.
	 * @param WP_Block       $block    Parsed block object that being rendered.
	 * @param ModuleElements $elements ModuleElements instance.
	 *
	 * @return string HTML rendered of TextDivider.
	 */
	public static function render_callback( $attrs, $content, $block, $elements ) {

		$dividerTextPosition = $attrs['title']['advanced']['position']['desktop']['value'] ??  '';
		$use_image = $attrs['image']['advanced']['use']['desktop']['value'] ?? '';
		$use_title = $attrs['title']['advanced']['use']['desktop']['value'] ?? '';
		$use_icon = $attrs['icon']['advanced']['use']['desktop']['value'] ?? '';

		$icon = $attrs['icon']['innerContent']['desktop']['value'] ?? [];


		// Title.
		$title = $elements->render(
			[
				'attrName' => 'title',
			]
		);

		// image.
		$image = $elements->render(
			[
				'attrName' => 'image',
			]
		);

		$image_wrapper       = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'dotm_text_divider_image_wrapper',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $image,
			]
		);

		$icon       = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'dotm_text_divider_icon',
				],
				'childrenSanitizer' => 'esc_html',
				'children'          => Utils::process_font_icon( $icon ),
			]
		);

		$icon_wrapper       = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'dotm_text_divider_icon_wrapper',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $icon,
			]
		);



		$before_divider = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'dotm_text_divider_before',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => '',
			]
		);

		$after_divider = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'dotm_text_divider_after',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => '',
			]
		);

		$parent       = BlockParserStore::get_parent( $block->parsed_block['id'], $block->parsed_block['storeInstance'] );
		$parent_attrs = $parent->attrs ?? [];

		return Module::render(
			[
				// FE only.
				'orderIndex'          => $block->parsed_block['orderIndex'],
				'storeInstance'       => $block->parsed_block['storeInstance'],

				// VB equivalent.
				'attrs'               => $attrs,
				'elements'            => $elements,
				'id'                  => $block->parsed_block['id'],
				'name'                => $block->block_type->name,
				'moduleCategory'      => $block->block_type->category,
				'classnamesFunction'  => [ TextDivider::class, 'module_classnames' ],
				'stylesComponent'     => [ TextDivider::class, 'module_styles' ],
				'scriptDataComponent' => [ TextDivider::class, 'module_script_data' ],
				'parentAttrs'         => $parent_attrs,
				'parentId'            => $parent->id ?? '',
				'parentName'          => $parent->blockName ?? '',
				'children'            => [
					ElementComponents::component(
						[
							'attrs'         => $attrs['module']['decoration'] ?? [],
							'id'            => $block->parsed_block['id'],

							// FE only.
							'orderIndex'    => $block->parsed_block['orderIndex'],
							'storeInstance' => $block->parsed_block['storeInstance'],
						]
					),
					HTMLUtility::render(
						[
							'tag'               => 'div',
							'attributes'        => [
								'class' => 'dotm_text_divider',
							],
							'childrenSanitizer' => 'et_core_esc_previously',
							'children'          => ($dividerTextPosition !== 'flex-start' ? $before_divider : '' )  . ($use_title === 'on' ? $title : '') . ($use_image === 'on' ? $image_wrapper : '') . ($use_icon === 'on' ? $icon_wrapper : '')  . ($dividerTextPosition !== 'flex-end' ? $after_divider : '' ),
						]
					),
				],
			]
		);
	}
}
