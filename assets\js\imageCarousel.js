function createCarousel(containerId, options = {}) {
  console.log(containerId, 'container id')

  const { autoStart, hide_arrows, hide_indicators } = options;

  console.log(hide_arrows, 'hide_arrows')

  console.log(autoStart, 'autostart')

  const state = {
    currentIndex: 0,
    interval: null,
    isPlaying: false,
    transitionEffect: options.transitionEffect || 'fade'
  };

  const container = document.getElementById(containerId);

  const elements = {
    slides: Array.from(container?.querySelectorAll('.dotm_image_carousel_slide')),
    indicators: Array.from(container?.querySelectorAll('.dotm_image_carousel_indicator')),
    prevBtn: container?.querySelector('.dotm_image_carousel_prev'),
    nextBtn: container?.querySelector('.dotm_image_carousel_next'),
    toggleBtn: container?.querySelector('.dotm_image_carousel_toggle')
  };


  function getPreviousIndex() {
    return (state.currentIndex - 1 + elements.slides.length) % elements.slides.length;
  }

  function updateSlides() {
    elements.slides.forEach((slide, index) => {
      slide.classList.remove('active', 'previous');
      slide.classList.add(state.transitionEffect);

      if (index === state.currentIndex) {
        slide.classList.add('active');
      } else if (state.transitionEffect === 'slide' && index === getPreviousIndex()) {
        slide.classList.add('previous');
      }
    });

    elements.indicators.forEach((indicator, index) => {
      indicator.classList.toggle('active', index === state.currentIndex);
    });
  }

  function next() {
    state.currentIndex = (state.currentIndex + 1) % elements.slides.length;
    updateSlides();
  }

  function prev() {
    state.currentIndex = (state.currentIndex - 1 + elements.slides.length) % elements.slides.length;
    updateSlides();
  }

  function goToSlide(index) {
    if (index === state.currentIndex) return;
    state.currentIndex = index;
    updateSlides();
  }

  function toggleAutoPlay() {
    state.isPlaying = !state.isPlaying;

    if (state.isPlaying) {
      state.interval = setInterval(next, options.interval);
    } else {
      clearInterval(state.interval);
    }
  }

  function setTransitionEffect(effect) {
    state.transitionEffect = effect;
    updateSlides();
  }

  // function attachIndicatorListeners() {
  //   console.log("check one")
  //   elements.indicators.forEach((indicator, index) => {
  //     indicator.addEventListener('click', () => goToSlide(index));
  //   });

  //   // **Ensure first indicator is active on load**
  //   if (elements.indicators.length > 0) {
  //     elements.indicators[0].classList.add('active');
  //   }
  // }

  function init() {
    if (hide_arrows === 'off') {
      elements.prevBtn.addEventListener('click', prev);
      elements.nextBtn.addEventListener('click', next);
    }

    elements.indicators.forEach((indicator, index) => {
      indicator.addEventListener('click', () => goToSlide(index));
    });

    updateSlides();

    // Auto-start if specified in options
    if (options.autoStart) {
      toggleAutoPlay();
    }


  }

  init();



  return {
    next,
    prev,
    goToSlide,
    setTransitionEffect
  };
}
