<?php

/**
 * Module: Static Module class.
 *
 * @package MEE\Modules\ShuffleLetter
 * @since ??
 */

namespace MEE\Modules\ShuffleLetter;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

use ET\Builder\Framework\DependencyManagement\Interfaces\DependencyInterface;
use ET\Builder\Packages\ModuleLibrary\ModuleRegistration;


/**
 * `ShuffleLetter` is consisted of functions used for Static Module such as Front-End rendering, REST API Endpoints etc.
 *
 * This is a dependency class and can be used as a dependency for `DependencyTree`.
 *
 * @since ??
 */
class ShuffleLetter implements DependencyInterface
{
	use ShuffleLetterTrait\RenderCallbackTrait;
	use ShuffleLetterTrait\ModuleClassnamesTrait;
	use ShuffleLetterTrait\ModuleStylesTrait;
	use ShuffleLetterTrait\ModuleScriptDataTrait;

	/**
	 * Loads `ShuffleLetter` and registers Front-End render callback and REST API Endpoints.
	 *
	 * @since ??
	 *
	 * @return void
	 */
	public function load()
	{
		$module_json_folder_path = DIVIOPTIMIZER_MODULES_JSON_PATH . 'ShuffleLetter/';

		add_action(
			'init',
			function () use ($module_json_folder_path) {
				ModuleRegistration::register_module(
					$module_json_folder_path,
					[
						'render_callback' => [ShuffleLetter::class, 'render_callback'],
					]
				);
			}
		);
	}
}
