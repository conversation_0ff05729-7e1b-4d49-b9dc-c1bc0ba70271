<?php

/**
 * OptimizeCTA::render_callback()
 *
 * @package MEE\Modules\OptimizeCTA
 * @since ??
 */

namespace MEE\Modules\OptimizeCTA\OptimizeCTATrait;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

// phpcs:disable ET.Sniffs.ValidVariableName.UsedPropertyNotSnakeCase -- WP use snakeCase in \WP_Block_Parser_Block

use ET\Builder\Packages\Module\Module;
use ET\Builder\Framework\Utility\HTMLUtility;
use ET\Builder\FrontEnd\BlockParser\BlockParserStore;
use ET\Builder\Packages\Module\Options\Element\ElementComponents;
use MEE\Modules\OptimizeCTA\OptimizeCTA;

trait RenderCallbackTrait
{

	/**
	 * OptimizeCTA render callback which outputs server side rendered HTML on the Front-End.
	 *
	 * @since ??
	 * @param array          $attrs    Block attributes that were saved by VB.
	 * @param string         $content  Block content.
	 * @param WP_Block       $block    Parsed block object that being rendered.
	 * @param ModuleElements $elements ModuleElements instance.
	 *
	 * @return string HTML rendered of OptimizeCTA.
	 */
	public static function render_callback($attrs, $content, $block, $elements)
	{

		$button_url = $attrs['button']['advanced']['link']['desktop']['value']['url'] ?? '';
		$button_target = $attrs['button']['advanced']['link']['desktop']['value']['target'] ?? '';
		$extra_button_url = $attrs['extra_button']['advanced']['link']['desktop']['value']['url'] ?? '';
		$extra_button_target = $attrs['extra_button']['advanced']['link']['desktop']['value']['target'] ?? '';
		$use_extra_btn = $attrs['extra_button']['advanced']['use']['desktop']['value'] ?? '';

		// image.
		$image = $elements->render(
			[
				'attrName' => 'image',
			]
		);

		// Image container.
		$image_container = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'dotm_optimize_cta_image_container',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $image,
			]
		);

		// Title.
		$title = $elements->render(
			[
				'attrName' => 'title',
			]
		);

		// Content.
		$content = $elements->render(
			[
				'attrName' => 'content',
			]
		);

		// button.
		$button = $elements->render(
			[
				'attrName' => 'button',
				'attributes' => [
					'class'    => 'dotm_optimize_cta_content_container_btns_button',
					'href'   => $button_url,
					'target' => $button_target === 'on' ? '_blank' : '_self',
				],
			]
		);

		// extra_button.
		$extra_button = $elements->render(
			[
				'attrName' => 'extra_button',
				'attributes' => [
					'class'    => 'dotm_optimize_cta_content_container_btns_extra_button',
					'href'   => $extra_button_url,
					'target' => $extra_button_target === 'on' ? '_blank' : '_self',
				]
			]
		);

		// Button container.
		$button_container = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'dotm_optimize_cta_content_container_btns',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $button . ($use_extra_btn === 'on' ? $extra_button : ''),
			]
		);

		// Content container.
		$content_container = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'dotm_optimize_cta_content_container',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $title . $content . $button_container,
			]
		);

		$parent       = BlockParserStore::get_parent($block->parsed_block['id'], $block->parsed_block['storeInstance']);
		$parent_attrs = $parent->attrs ?? [];

		return Module::render(
			[
				// FE only.
				'orderIndex'          => $block->parsed_block['orderIndex'],
				'storeInstance'       => $block->parsed_block['storeInstance'],

				// VB equivalent.
				'attrs'               => $attrs,
				'elements'            => $elements,
				'id'                  => $block->parsed_block['id'],
				'name'                => $block->block_type->name,
				'moduleCategory'      => $block->block_type->category,
				'classnamesFunction'  => [OptimizeCTA::class, 'module_classnames'],
				'stylesComponent'     => [OptimizeCTA::class, 'module_styles'],
				'scriptDataComponent' => [OptimizeCTA::class, 'module_script_data'],
				'parentAttrs'         => $parent_attrs,
				'parentId'            => $parent->id ?? '',
				'parentName'          => $parent->blockName ?? '',
				'children'            => [
					ElementComponents::component(
						[
							'attrs'         => $attrs['module']['decoration'] ?? [],
							'id'            => $block->parsed_block['id'],

							// FE only.
							'orderIndex'    => $block->parsed_block['orderIndex'],
							'storeInstance' => $block->parsed_block['storeInstance'],
						]
					),
					HTMLUtility::render(
						[
							'tag'               => 'div',
							'attributes'        => [
								'class' => 'dotm_optimize_cta_container',
							],
							'childrenSanitizer' => 'et_core_esc_previously',
							'children'          => $image_container . $content_container,
						]
					),
				],
			]
		);
	}
}
