<?php
/**
 * OptimizeCardCarouselItem::render_callback()
 *
 * @package MEE\Modules\OptimizeCardCarouselItem
 * @since ??
 */

namespace MEE\Modules\OptimizeCardCarouselItem\OptimizeCardCarouselItemTrait;

if ( ! defined( 'ABSPATH' ) ) {
	die( 'Direct access forbidden.' );
}

// phpcs:disable ET.Sniffs.ValidVariableName.UsedPropertyNotSnakeCase -- WP use snakeCase in \WP_Block_Parser_Block

use ET\Builder\Packages\Module\Module;
use ET\Builder\Framework\Utility\HTMLUtility;
use ET\Builder\FrontEnd\BlockParser\BlockParserStore;
use ET\Builder\Packages\IconLibrary\IconFont\Utils;
use ET\Builder\Packages\Module\Options\Element\ElementComponents;
use ET\Builder\Packages\ModuleLibrary\ModuleRegistration;
use MEE\Modules\OptimizeCardCarouselItem\OptimizeCardCarouselItem;

trait RenderCallbackTrait {
	use ModuleClassnamesTrait;
	use ModuleStylesTrait;

	/**
	 * OptimizeCardCarouselItem render callback which outputs server side rendered HTML on the Front-End.
	 *
	 * @since ??
	 *
	 * @param array          $attrs Block attributes that were saved by VB.
	 * @param string         $content          Block content.
	 * @param WP_Block       $block            Parsed block object that being rendered.
	 * @param ModuleElements $elements         ModuleElements instance.
	 *
	 * @return string HTML rendered of Child module.
	 */
	public static function render_callback( $attrs, $content, $block, $elements ) {
		$parent = BlockParserStore::get_parent( $block->parsed_block['id'], $block->parsed_block['storeInstance'] );

		$parent_attrs = $parent->attrs ?? [];

		$parent_default_attributes = ModuleRegistration::get_default_attrs( 'dotm/optimize-card-carousel' );
		$parent_attrs_with_default = array_replace_recursive( $parent_default_attributes, $parent_attrs );

		// Icon.
		$icon_value = $attrs['icon']['innerContent']['desktop']['value'] ?? $parent_attrs_with_default['icon']['innerContent']['desktop']['value'] ?? [];
		$image_src = $attrs['image']['innerContent']['desktop']['value']['src'] ?? '';
		$hover_effect = $attrs['image']['advanced']['hoverEffect']['desktop']['value'] ?? '';
		$use_badge = $attrs['badge']['advanced']['use_badge']['desktop']['value'] ?? 'off';

		$button_url = $attrs['button']['advanced']['link']['desktop']['value']['url'] ?? '';
		$button_target = $attrs['button']['advanced']['link']['desktop']['value']['target'] ?? '';

		$icon       = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'dotm_card_carousel_icon',
				],
				'childrenSanitizer' => 'esc_html',
				'children'          => Utils::process_font_icon( $icon_value ),
			]
		);

		$icon_container       = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'dotm_card_carousel_icon_container',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $icon,
			]
		);

		
		$image = $elements->render(
			[
				'attrName'      => 'image',
				'hoverSelector' => '{{parentSelector}}',
			]
		);

		$image_wrapper       = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => "dotm_card_carousel_image_wrapper image_hover_{$hover_effect}",
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $image,
			]
		);

		$image_container       = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'dotm_card_carousel_image_container',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $image_wrapper,
			]
		);

		$badge = $elements->render(
			[
				'attrName'      => 'badge',
				'hoverSelector' => '{{parentSelector}}',
			]
		);

		// Title.
		$title = $elements->render(
			[
				'attrName'      => 'title',
				'hoverSelector' => '{{parentSelector}}',
			]
		);

		// Content.
		$content = $elements->render(
			[
				'attrName'      => 'content',
				'hoverSelector' => '{{parentSelector}}',
			]
		);

		$button = $elements->render(
			[
				'attrName'      => 'button',
				'hoverSelector' => '{{parentSelector}}',
				'attributes' => [
					'class'    => 'dotm_card_carousel_button',
					'href'   => $button_url,
					'target' => $button_target === 'on' ? '_blank' : '_self',
				],
			]
		);

		$button_container = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'dotm_card_carousel_button_container',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $button,
			]
		);

		// Content container.
		$content_container = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'dotm_card_carousel_content_container',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $title . $content . $button_container,
			]
		);


		$carousel_wrapper = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'dotm_card_carousel_wrapper',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => ($icon_value ? $icon_container : '') . ($image_src ? $image_container : '') . ($use_badge === 'on' ? $badge : '' )  . $content_container,
			]
		);

		return Module::render(
			[
				// FE only.
				'orderIndex'         => $block->parsed_block['orderIndex'],
				'storeInstance'      => $block->parsed_block['storeInstance'],

				// VB equivalent.
				'id'                 => $block->parsed_block['id'],
				'name'               => $block->block_type->name,
				'moduleCategory'     => $block->block_type->category,
				'attrs'              => $attrs,
				'elements'           => $elements,
				'classnamesFunction' => [ OptimizeCardCarouselItem::class, 'module_classnames' ],
				'stylesComponent'    => [ OptimizeCardCarouselItem::class, 'module_styles' ],
				'parentAttrs'        => $parent_attrs,
				'parentId'           => $parent->id ?? '',
				'parentName'         => $parent->blockName ?? '',
				'children'           => ElementComponents::component(
					[
						'attrs'         => $attrs['module']['decoration'] ?? [],
						'id'            => $block->parsed_block['id'],

						// FE only.
						'orderIndex'    => $block->parsed_block['orderIndex'],
						'storeInstance' => $block->parsed_block['storeInstance'],
					]
				) . $carousel_wrapper,
			]
		);
	}
}
