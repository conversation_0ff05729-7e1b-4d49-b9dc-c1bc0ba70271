<?php

/**
 * Module: OptimizeCountDown class.
 *
 * @package MEE\Modules\OptimizeCountDown
 * @since ??
 */

namespace MEE\Modules\OptimizeCountDown;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}


use ET\Builder\Framework\DependencyManagement\Interfaces\DependencyInterface;
use ET\Builder\Packages\ModuleLibrary\ModuleRegistration;

/**
 * `OptimizeCountDown` is consisted of functions used for OptimizeCountDown such as Front-End rendering, REST API Endpoints etc.
 *
 * This is a dependency class and can be used as a dependency for `DependencyTree`.
 *
 * @since ??
 */
class OptimizeCountDown implements DependencyInterface
{
	use OptimizeCountDownTrait\RenderCallbackTrait;

	/**
	 * Loads `OptimizeCountDown` and registers Front-End render callback and REST API Endpoints.
	 *
	 * @since ??
	 *
	 * @return void
	 */
	public function load()
	{
		$module_json_folder_path = DIVIOPTIMIZER_MODULES_JSON_PATH . 'OptimizeCountDown/';

		add_action(
			'init',
			function () use ($module_json_folder_path) {
				ModuleRegistration::register_module(
					$module_json_folder_path,
					[
						'render_callback' => [OptimizeCountDown::class, 'render_callback'],
					]
				);
			}
		);
	}
}
