<?php
/**
 * ImageHover::module_styles().
 *
 * @package MEE\Modules\ImageHover
 * @since ??
 */

namespace MEE\Modules\ImageHover\ImageHoverTrait;

if ( ! defined( 'ABSPATH' ) ) {
	die( 'Direct access forbidden.' );
}

use ET\Builder\FrontEnd\Module\Style;
use ET\Builder\Packages\Module\Options\Text\TextStyle;
use ET\Builder\Packages\Module\Options\Css\CssStyle;
use MEE\Modules\ImageHover\ImageHover;
use ET\Builder\Packages\Module\Layout\Components\StyleCommon\CommonStyle;

trait ModuleStylesTrait {

	use CustomCssTrait;

	/**
	 * Static Module's style components.
	 *
	 * This function is equivalent of JS function ModuleStyles located in
	 * src/components/static-module/styles.tsx.
	 *
	 * @since ??
	 *
	 * @param array $args {
	 *     An array of arguments.
	 *
	 *      @type string $id                Module ID. In VB, the ID of module is UUIDV4. In FE, the ID is order index.
	 *      @type string $name              Module name.
	 *      @type string $attrs             Module attributes.
	 *      @type string $parentAttrs       Parent attrs.
	 *      @type string $orderClass        Selector class name.
	 *      @type string $parentOrderClass  Parent selector class name.
	 *      @type string $wrapperOrderClass Wrapper selector class name.
	 *      @type string $settings          Custom settings.
	 *      @type string $state             Attributes state.
	 *      @type string $mode              Style mode.
	 *      @type ModuleElements $elements  ModuleElements instance.
	 * }
	 */
	public static function module_styles( $args ) {
		$attrs    = $args['attrs'] ?? [];
		$elements = $args['elements'];
		$settings = $args['settings'] ?? [];
		$orderClass = $args['orderClass'];

		$imageWraperSelector = "$orderClass .dotm_module__image";
		$image_size_selector = "{$orderClass} .dotm_module__image img";
		$holographicSelector = "{$orderClass} .holographic-horizontal::before";
		$holographicVerticalSelector = "{$orderClass} .holographic-vertical::before";
		$rippleEffectSelector = "{$orderClass} .ripple-effect::before";
		$effectBorder1BeforeSelector = "{$orderClass} .effect-border-1::before";
		$effectBorder1AfterSelector = "{$orderClass} .effect-border-1::after";
		$effectBorder2BeforeSelector = "{$orderClass} .effect-border-2::before";
		$effect13ColorSelector = "{$orderClass} .effect-13::after";
		$effect14ColorSelector = "{$orderClass} .magnetic-corner:hover img";
		$overlaySelector = "{$orderClass} .opacity-hover";


		$borderColor = $attrs['hover']['decoration']['effectBorder1']['topColor']['desktop']['value'] ??'';
		$borderWidth =$attrs['hover']['decoration']['effectBorder1']['borderWidth']['desktop']['value'] ?? '';

		
		$revealColor = $attrs['hover']['decoration']['effectBorder2']['revealColor']['desktop']['value'] ?? '';
		$revealPositionTop = $attrs['hover']['decoration']['effectBorder2']['positionTop']['desktop']['value'] ?? '';
		$revealPositionLeft = $attrs['hover']['decoration']['effectBorder2']['positionLeft']['desktop']['value'] ?? '';
		$revealRadius = $attrs['hover']['decoration']['effectBorder2']['revealRadius']['desktop']['value'] ?? '';
		$magneticCorner = $attrs['hover']['decoration']['magneticCorner']['perspective']['desktop']['value'] ?? '';
		$magneticCornerRotateX = $attrs['hover']['decoration']['magneticCorner']['rotationX']['desktop']['value'] ?? '';
		$magneticCornerRotateY = $attrs['hover']['decoration']['magneticCorner']['rotationY']['desktop']['value'] ?? '';

		Style::add(


			[
				'id'            => $args['id'],
				'name'          => $args['name'],
				'orderIndex'    => $args['orderIndex'],
				'storeInstance' => $args['storeInstance'],
				'styles'        => [
					// Module.
					$elements->style(
						[
							'attrName'   => 'module',
							'styleProps' => [
								'disabledOn'     => [
									'disabledModuleVisibility' => $settings['disabledModuleVisibility'] ?? null,
								],
								'advancedStyles' => [
									[
										'componentName' => 'divi/text',
										'props'         => [
											'selector' => "{$args['orderClass']} .example_static_module__content-container",
											'attr'     => $attrs['module']['advanced']['text'] ?? [],
										]
									]
								]
							],
						]
					),

					CommonStyle::style(
						[
							'selector' => $imageWraperSelector,
							'attr'     => $attrs['image']['decoration']['imagewidth'] ?? [],
							'property' => 'width',
						]
					),

					CommonStyle::style(
						[
							'selector' => $imageWraperSelector,
							'attr'     => $attrs['image']['decoration']['imageHeight'] ?? [],
							'property' => 'height',
						]
					),

					CommonStyle::style(
						[
							'selector' => $image_size_selector,
							'attr'     => $attrs['image']['decoration']['imageSize'] ?? [],
							'property' => 'object-fit',
						]
					),

					CommonStyle::style(
						[
							'selector' => $holographicSelector,
							'attr'     => $attrs['hover']['decoration']['holographicHorizontal']['transition'] ?? [],
							'property' => 'transition',
						]
					),

					
					CommonStyle::style(
						[
							'selector' => $holographicVerticalSelector,
							'attr'     => $attrs['hover']['decoration']['holographicVertical']['transition'] ?? [],
							'property' => 'transition',
						]
					),

					CommonStyle::style(
					[
						'selector' => $rippleEffectSelector,
						'attr'     => $attrs['hover']['decoration']['rippleEffect']['transition'] ?? [],
						'property' => 'transition',
					]
					),

					CommonStyle::style(
						[
							'selector' => $effectBorder1BeforeSelector,
							'attr'     => $attrs['hover']['decoration']['effectBorder1']['topColor'] ?? [],
							'property' => "border-top:$borderWidth solid $borderColor; border-left:$borderWidth solid $borderColor;",
						]
					),
					CommonStyle::style(
						[
							'selector' => $effectBorder1AfterSelector,
							'attr'     => $attrs['hover']['decoration']['effectBorder1']['topColor'] ?? [],
							'property' => "border-bottom:$borderWidth solid $borderColor; border-right:$borderWidth solid $borderColor;",
						]
					),

					CommonStyle::style(
						[
							'selector' => $effectBorder2BeforeSelector,
							'attr'     => $attrs['hover']['decoration']['effectBorder2']['revealColor'] ?? [],
							'property' => "top:$revealPositionTop; left:$revealPositionLeft; background:$revealColor; border-radius:$revealRadius;",
						]
					),

					CommonStyle::style(
						[
							'selector' => $effect13ColorSelector,
							'attr'     => $attrs['hover']['decoration']['effect13']['color'] ?? [],
							'property' => 'background',
						]
					),

					CommonStyle::style(
						[
							'selector' => $effect14ColorSelector,
							'attr'     => $attrs['hover']['decoration']['magneticCorner']['perspective'] ?? [],
							'property' => "transform: perspective($magneticCorner) rotateX($magneticCornerRotateX) rotateY($magneticCornerRotateY);",
						]
					),

					CommonStyle::style(
						[
							'selector' => $overlaySelector,
							'attr'     => $attrs['hover']['decoration']['opacityColor'] ?? [],
							'property' => 'background',
						]
						),


					// Image.
					$elements->style(
						[
							'attrName' => 'image',
						]
					),

				
					
					/*
					 * We need to add CssStyle at the very bottom of other
					 * components so that custom css can override module styles
					 * till we find a more elegant solution.
					 */
					CssStyle::style(
						[
							'selector'  => $args['orderClass'],
							'attr'      => $attrs['css'] ?? [],
							'cssFields' => ImageHover::custom_css(),
						]
					),

					// ATTENTION: The code is intentionally added and commented in FE only as an example of expected value format.
					// If you have custom style processing, the style output should be passed as an `array` of style declarations
					// to the `styles` property of the `Style::add` method. For example:
					// [
					// 	[
					// 		'atRules'     => false,
					// 		'selector'    => "{$args['orderClass']} .example_static_module__content-container",
					// 		'declaration' => 'color: red;'
					// 	],
					// 	[
					// 		'atRules'     => '@media only screen and (max-width: 767px)',
					// 		'selector'    => "{$args['orderClass']} .example_static_module__content-container",
					// 		'declaration' => 'color: green;'
					// 	],
					// ],

					// The code below is an example of how to use the `CssStyle::style` method to generate CSS style.
					CssStyle::style(
						[
							'selector'  => $args['orderClass'],
							'attr'      => $attrs['css'] ?? [],
							'cssFields' => ImageHover::custom_css(),
						]
					),
				],
			]
		);
	}
}
