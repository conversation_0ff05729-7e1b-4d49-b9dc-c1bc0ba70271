<?php

/**
 * OptimizeCard::module_styles().
 *
 * @package MEE\Modules\OptimizeCard
 * @since ??
 */

namespace MEE\Modules\OptimizeCard\OptimizeCardTrait;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

use ET\Builder\FrontEnd\Module\Style;
use ET\Builder\Packages\Module\Options\Text\TextStyle;
use ET\Builder\Packages\Module\Options\Css\CssStyle;
use MEE\Modules\OptimizeCard\OptimizeCard;

trait ModuleStylesTrait
{

	use CustomCssTrait;
	use StyleDeclarationTrait;

	/**
	 * OptimizeCard's style components.
	 *
	 * This function is equivalent of JS function ModuleStyles located in
	 * src/components/OptimizeCard/styles.tsx.
	 *
	 * @since ??
	 *
	 * @param array $args {
	 *     An array of arguments.
	 *
	 *      @type string $id                Module ID. In VB, the ID of module is UUIDV4. In FE, the ID is order index.
	 *      @type string $name              Module name.
	 *      @type string $attrs             Module attributes.
	 *      @type string $parentAttrs       Parent attrs.
	 *      @type string $orderClass        Selector class name.
	 *      @type string $parentOrderClass  Parent selector class name.
	 *      @type string $wrapperOrderClass Wrapper selector class name.
	 *      @type string $settings          Custom settings.
	 *      @type string $state             Attributes state.
	 *      @type string $mode              Style mode.
	 *      @type ModuleElements $elements  ModuleElements instance.
	 * }
	 */
	public static function module_styles($args)
	{
		$attrs    = $args['attrs'] ?? [];
		$elements = $args['elements'];
		$settings = $args['settings'] ?? [];
		$class_order = $args['orderClass'];

		$imageContainerSelector = "{$class_order} .dotm_optimize_card_inner_image_wrapper";
		$iconContainerSelector = "{$class_order} .dotm_optimize_card_icon_container";
		$imageSelector = "{$class_order} .dotm_optimize_card_image_container";
		$btnContainerSelector = "{$class_order} .dotm_optimize_card_btn_container";
		$imageLabelSelector = "{$class_order} .dotm_optimize_card_image_label";
		$containerSelector = "{$class_order} .dotm_optimize_card_container";

		$labelHrAlignment = $attrs['image_label']['decoration']['hr_alignment']['desktop']['value'] ?? '';
		$containerGap = $attrs['contentContainer']['decoration']['gap']['desktop']['value'] ?? '';
		$hrAlignment = $attrs['contentContainer']['decoration']['hr_alignment']['desktop']['value'] ?? '';

		Style::add(
			[
				'id'            => $args['id'],
				'name'          => $args['name'],
				'orderIndex'    => $args['orderIndex'],
				'storeInstance' => $args['storeInstance'],
				'styles'        => [
					// Module.
					$elements->style(
						[
							'attrName'   => 'module',
							'styleProps' => [
								'disabledOn'     => [
									'disabledModuleVisibility' => $settings['disabledModuleVisibility'] ?? null,
								],
								'advancedStyles' => [
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $imageSelector,
											'attr'     => $attrs['image']['decoration']['image_width'] ?? [],
											'property' => 'width',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $imageContainerSelector,
											'attr'     => $attrs['image']['decoration']['image_align'] ?? [],
											'property' => 'justify-content',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $imageContainerSelector,
											'attr'     => $attrs['contentContainer']['decoration']['imageContaienrWidth'] ?? [],
											'property' => 'width',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $btnContainerSelector,
											'attr'     => $attrs['btn']['decoration']['btn_align'] ?? [],
											'property' => 'text-align',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $imageLabelSelector,
											'attr'     => $attrs['image_label']['decoration']['vr_alignment'] ?? [],
											'property' => "top: {$labelHrAlignment}; left",
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $containerSelector,
											'attr'     => $attrs['contentContainer']['decoration']['content_direction'] ?? [],
											'property' => "column-gap: {$containerGap}; align-items: {$hrAlignment}; flex-direction",
										]
									]
								]
							],
						]
					),

					// container.
					$elements->style(
						[
							'attrName' => 'container',
						]
					),

					// Image.
					$elements->style(
						[
							'attrName' => 'image',
						]
					),



					// image_label.
					$elements->style(
						[
							'attrName' => 'image_label',
						]
					),



					// Title.
					$elements->style(
						[
							'attrName' => 'title',
						]
					),


					// contentContainer.
					$elements->style(
						[
							'attrName' => 'contentContainer',
							'styleProps' => [
								'disabledOn'     => [
									'disabledModuleVisibility' => $settings['disabledModuleVisibility'] ?? null,
								],
								'advancedStyles' => [
									[
										'componentName' => 'divi/common',
										'props'         => [
											'attr'     => $attrs['contentContainer']['decoration']['contentContaienrWidth'] ?? [],
											'property' => 'width',
										]
									],
								]
							],
						]
					),

					// sub_title.
					$elements->style(
						[
							'attrName' => 'sub_title',
						]
					),


					// Content.
					$elements->style(
						[
							'attrName' => 'content',
						]
					),

					// btn.
					$elements->style(
						[
							'attrName' => 'btn',
						]
					),

					// Icon Style
					$elements->style(
						[
							'attrName'   => 'icon',
							'styleProps' => [
								'advancedStyles' => [
									[
										'componentName' => 'divi/common',
										'props'         => [
											'attr'                => $attrs['icon']['innerContent'] ?? [],
											'declarationFunction' => [OptimizeCard::class, 'icon_font_declaration'],
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'attr'     => $attrs['icon']['advanced']['color'] ?? [],
											'property' => 'color',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'attr'     => $attrs['icon']['advanced']['size'] ?? [],
											'property' => 'font-size',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $iconContainerSelector,
											'attr'     => $attrs['icon']['decoration']['icon_align'] ?? [],
											'property' => 'text-align',
										]
									],
								]
							]
						]
					),


					/*
					 * We need to add CssStyle at the very bottom of other
					 * components so that custom css can override module styles
					 * till we find a more elegant solution.
					 */
					CssStyle::style(
						[
							'selector'  => $args['orderClass'],
							'attr'      => $attrs['css'] ?? [],
							'cssFields' => OptimizeCard::custom_css(),
						]
					),

					// ATTENTION: The code is intentionally added and commented in FE only as an example of expected value format.
					// If you have custom style processing, the style output should be passed as an `array` of style declarations
					// to the `styles` property of the `Style::add` method. For example:
					// [
					// 	[
					// 		'atRules'     => false,
					// 		'selector'    => "{$args['orderClass']} .example_static_module__content-container",
					// 		'declaration' => 'color: red;'
					// 	],
					// 	[
					// 		'atRules'     => '@media only screen and (max-width: 767px)',
					// 		'selector'    => "{$args['orderClass']} .example_static_module__content-container",
					// 		'declaration' => 'color: green;'
					// 	],
					// ],

					// The code below is an example of how to use the `CssStyle::style` method to generate CSS style.
					CssStyle::style(
						[
							'selector'  => $args['orderClass'],
							'attr'      => $attrs['css'] ?? [],
							'cssFields' => OptimizeCard::custom_css(),
						]
					),
				],
			]
		);
	}
}
