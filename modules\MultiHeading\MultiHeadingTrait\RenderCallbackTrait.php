<?php

/**
 * MultiHeading::render_callback()
 *
 * @package MEE\Modules\MultiHeading
 * @since ??
 */

namespace MEE\Modules\MultiHeading\MultiHeadingTrait;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

// phpcs:disable ET.Sniffs.ValidVariableName.UsedPropertyNotSnakeCase -- WP use snakeCase in \WP_Block_Parser_Block

use ET\Builder\Packages\Module\Module;
use ET\Builder\Framework\Utility\HTMLUtility;
use ET\Builder\FrontEnd\BlockParser\BlockParserStore;
use ET\Builder\Packages\Module\Options\Element\ElementComponents;
use MEE\Modules\MultiHeading\MultiHeading;



trait RenderCallbackTrait
{



	/**
	 * Advanced Heading render callback which outputs server side rendered HTML on the Front-End.
	 *
	 * @since ??
	 * @param array          $attrs    Block attributes that were saved by VB.
	 * @param string         $content  Block content.
	 * @param WP_Block       $block    Parsed block object that being rendered.
	 * @param ModuleElements $elements ModuleElements instance.
	 *
	 * @return string HTML rendered of Advanced Heading.
	 */


	public static function render_callback($attrs, $content, $block, $elements)
	{
		// Select all variables here.
		$containerDisplayType = $attrs['display']['advanced']['position']['desktop']['value'] ?? '';
		$containerDisplayDirection = $attrs['display']['advanced']['direction']['desktop']['value'] ?? '';
		$containerAlignment = $attrs['display']['advanced']['justifyContent']['desktop']['value'] ?? 'center';
		$containerAlignment2 = $attrs['display']['advanced']['alignItems']['desktop']['value'] ?? 'center';

		$title1DisplayType = $attrs['title1']['advanced']['display']['desktop']['value'] ?? '';
		$title2DisplayType = $attrs['title2']['advanced']['display']['desktop']['value'] ?? '';
		$title3DisplayType = $attrs['title3']['advanced']['display']['desktop']['value'] ?? '';
		$title3PositionType = $attrs['title3PositionType']['innerContent']['desktop']['value'] ?? '';
		$title3PositionTop = $attrs['title3PositionTop']['innerContent']['desktop']['value'] ?? '';

		//toggle variables
		$title3PositionSet = $attrs['title3PositionSet']['innerContent']['desktop']['value'] ?? '';

		//Container inline dynamic Style
		$ContainerStyle = "display:{$containerDisplayType}; flex-direction:{$containerDisplayDirection}; align-items:{$containerAlignment2}; justify-content:{$containerAlignment};";


		// title 1 dynamic style
		$title1Type = $attrs['title1']['advanced']['style']['desktop']['value'] ?? "";
		$title1img = $attrs['title1']['decoration']['background']['desktop']['value']['image']['url'] ?? "";
		$title1Color = $attrs['title1']['decoration']['background']['desktop']['value']['gradient']['enabled'] ?? " ";
		$title1Speed = $attrs['title1']['advanced']['animation']['desktop']['value'] ?? " ";
		$title1StrokColor = $attrs['title1']['advanced']['strock']['desktop']['value'] ?? "null";
		$title1StrokBorder = $attrs['title1']['advanced']['borderWidth']['desktop']['value'] ?? " ";


		// title2
		$title2Type = $attrs['title2']['advanced']['style']['desktop']['value'] ?? "";
		$title2img = $attrs['title2']['decoration']['background']['desktop']['value']['image']['url'] ?? "";
		$title2Color = $attrs['title2']['decoration']['background']['desktop']['value']['gradient']['enabled'] ?? " ";
		$title2Speed = $attrs['title2']['advanced']['animation']['desktop']['value'] ?? " ";
		$title2StrokColor = $attrs['title2']['advanced']['strock']['desktop']['value'] ?? "null";
		$title2StrokBorder = $attrs['title2']['advanced']['borderWidth']['desktop']['value'] ?? "null";


		// title3
		$title3Type = $attrs['title3']['advanced']['style']['desktop']['value'] ?? "";
		$title3img = $attrs['title3']['decoration']['background']['desktop']['value']['image']['url'] ?? "";
		$title3Color = $attrs['title3']['decoration']['background']['desktop']['value']['gradient']['enabled'] ?? " ";
		$title3Speed = $attrs['title3']['advanced']['animation']['desktop']['value'] ?? " ";
		$title3StrokColor = $attrs['title3']['advanced']['strock']['desktop']['value'] ?? "null";
		$title3StrokBorder = $attrs['title3']['advanced']['borderWidth']['desktop']['value'] ?? "null";

		//Titles inline dynamic Style
		$TitleStyle1 = "display:{$title1DisplayType}; " .
			($title1img !== "" || $title1Color === 'on' ? '-webkit-text-fill-color: transparent; text-fill-color: transparent;' : '') .
			($title1StrokColor !== "null" ? 'color: transparent;' : '') .
			($title1Type === "stock" || $title1StrokColor !== "null" || $title1StrokBorder !== " " ? '-webkit-text-stroke: ' . $title1StrokBorder . ' ' . $title1StrokColor . ';' : '') .
			($title1Speed !== " " ? 'animation-duration: ' . $title1Speed . ';' : '');

		$TitleStyle2 = "display:{$title2DisplayType}; " .
			($title2img !== "" || $title2Color === 'on' ? '-webkit-text-fill-color: transparent; text-fill-color: transparent;' : '') .
			($title2StrokColor !== "null" ? 'color: transparent;' : '') .
			($title2Type === "stock" || $title2StrokColor !== "null" || $title2StrokBorder !== "null" ? '-webkit-text-stroke: ' . $title2StrokBorder . ' ' . $title2StrokColor . ';' : '') .
			($title2Speed !== " " ? 'animation-duration: ' . $title2Speed . ';' : '');
			
		$TitleStyle3 = "display:{$title3DisplayType}; " .
			($title3PositionSet === 'on' ? "position:{$title3PositionType}; top:{$title3PositionTop};" : '') .
			($title3img !== "" || $title3Color === 'on' ? '-webkit-text-fill-color: transparent; text-fill-color: transparent;' : '') .
			($title3StrokColor !== "null" ? 'color: transparent;' : '') .
			($title3Type === "stock" || $title3StrokColor !== "null" || $title3StrokBorder !== "null" ? '-webkit-text-stroke: ' . $title3StrokBorder . ' ' . $title3StrokColor . ';' : '') .
			($title3Speed !== " " ? 'animation-duration: ' . $title3Speed . ';' : '');



		// Title 1.
		$title1 = $elements->render(
			[
				'attrName' => 'title1',
				'attributes' => [
					'class' => 'advanced_heading__title1',
					'style'   => $TitleStyle1,
				],


			]
		);
		// Title 2.
		$title2 = $elements->render(
			[
				'attrName' => 'title2',
				'attributes' => [
					'class' => 'advanced_heading__title2',
					'style'   => $TitleStyle2,
				],


			]
		);


		// Title 3.
		$title3 = $elements->render(
			[
				'attrName' => 'title3',
				'attributes' => [
					'class' => 'advanced_heading__title3',
					'style'   => $TitleStyle3,
				],



			]
		);



		$parent       = BlockParserStore::get_parent($block->parsed_block['id'], $block->parsed_block['storeInstance']);
		$parent_attrs = $parent->attrs ?? [];

		return Module::render(
			[
				// FE only.
				'orderIndex'          => $block->parsed_block['orderIndex'],
				'storeInstance'       => $block->parsed_block['storeInstance'],

				// VB equivalent.
				'attrs'               => $attrs,
				'elements'            => $elements,
				'id'                  => $block->parsed_block['id'],
				'name'                => $block->block_type->name,
				'moduleCategory'      => $block->block_type->category,
				'classnamesFunction'  => [MultiHeading::class, 'module_classnames'],
				'stylesComponent'     => [MultiHeading::class, 'module_styles'],
				'scriptDataComponent' => [MultiHeading::class, 'module_script_data'],
				'parentAttrs'         => $parent_attrs,
				'parentId'            => $parent->id ?? '',
				'parentName'          => $parent->blockName ?? '',
				'children'            => [
					ElementComponents::component(
						[
							'attrs'         => $attrs['module']['decoration'] ?? [],
							'id'            => $block->parsed_block['id'],

							// FE only.
							'orderIndex'    => $block->parsed_block['orderIndex'],
							'storeInstance' => $block->parsed_block['storeInstance'],
						]
					),
					// Content container.
					HTMLUtility::render(
						[
							'tag'               => 'div',
							'attributes'        => [
								'class' => 'advanced_heading__container',
							],
							'childrenSanitizer' => 'et_core_esc_previously',
							'children'          => $title1 . $title2 . $title3,
						]
					),
				],
			]
		);
	}
}
