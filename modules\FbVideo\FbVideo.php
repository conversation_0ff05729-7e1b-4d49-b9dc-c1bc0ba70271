<?php

/**
 * Module: Static Module class.
 *
 * @package MEE\Modules\FbVideo
 * @since ??
 */

namespace MEE\Modules\FbVideo;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

use ET\Builder\Framework\DependencyManagement\Interfaces\DependencyInterface;
use ET\Builder\Packages\ModuleLibrary\ModuleRegistration;


/**
 * `FbVideo` is consisted of functions used for Static Module such as Front-End rendering, REST API Endpoints etc.
 *
 * This is a dependency class and can be used as a dependency for `DependencyTree`.
 *
 * @since ??
 */
class FbVideo implements DependencyInterface
{
	use FbVideoTrait\RenderCallbackTrait;
	use FbVideoTrait\ModuleClassnamesTrait;
	use FbVideoTrait\ModuleStylesTrait;
	use FbVideoTrait\ModuleScriptDataTrait;

	/**
	 * Loads `FbVideo` and registers Front-End render callback and REST API Endpoints.
	 *
	 * @since ??
	 *
	 * @return void
	 */
	public function load()
	{
		$module_json_folder_path = DIVIOPTIMIZER_MODULES_JSON_PATH . 'FbVideo/';

		add_action(
			'init',
			function () use ($module_json_folder_path) {
				ModuleRegistration::register_module(
					$module_json_folder_path,
					[
						'render_callback' => [FbVideo::class, 'render_callback'],
					]
				);
			}
		);
	}
}
