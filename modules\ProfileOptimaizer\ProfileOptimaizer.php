<?php

/**
 * Module: Parent Module class.
 *
 * @package MEE\Modules\ProfileOptimaizer
 * @since ??
 */

namespace MEE\Modules\ProfileOptimaizer;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}


use ET\Builder\Framework\DependencyManagement\Interfaces\DependencyInterface;
use ET\Builder\Packages\ModuleLibrary\ModuleRegistration;

/**
 * `ProfileOptimaizer` is consisted of functions used for Parent Module such as Front-End rendering, REST API Endpoints etc.
 *
 * This is a dependency class and can be used as a dependency for `DependencyTree`.
 *
 * @since ??
 */
class ProfileOptimaizer implements DependencyInterface
{
	use ProfileOptimaizerTrait\RenderCallbackTrait;

	/**
	 * Loads `ProfileOptimaizer` and registers Front-End render callback and REST API Endpoints.
	 *
	 * @since ??
	 *
	 * @return void
	 */
	public function load()
	{
		$module_json_folder_path = DIVIOPTIMIZER_MODULES_JSON_PATH . 'ProfileOptimaizer/';

		add_action(
			'init',
			function () use ($module_json_folder_path) {
				ModuleRegistration::register_module(
					$module_json_folder_path,
					[
						'render_callback' => [ProfileOptimaizer::class, 'render_callback'],
					]
				);
			}
		);
	}
}
