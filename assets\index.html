<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Circle Profile Section</title>
  <style>
    .circle-profile-container {
      position: relative;
      width: 400px;
      height: 400px;
      margin: 50px auto;
    }

    .circle-profile {
      position: absolute;
      width: 100%;
      height: 100%;
      border-radius: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: rgba(0, 0, 0, 0.5);
      overflow: visible;
      border: 5px solid black; /* Added border */
    }

    .circle-profile img.center-image {
      width: 200px;
      height: 200px;
      border-radius: 50%;
      object-fit: cover;
      z-index: 1;
    }

    .circle-profile .center-content {
      position: absolute;
      text-align: center;
      color: white;
      z-index: 2;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }

    .center-content h2 {
      font-size: 2rem;
      margin: 0;
    }

    .center-content p {
      font-size: 1rem;
      margin: 10px 0;
    }

    .center-content button {
      padding: 8px 16px;
      font-size: 1rem;
      background-color: #ff5722;
      color: white;
      border: none;
      border-radius: 5px;
      cursor: pointer;
    }

    .orbit {
      position: absolute;
      width: 100%;
      height: 100%;
      top: 0;
      left: 0;
      transform-origin: center center;
      pointer-events: none;
    }

    .orbit.animate-smooth {
      animation: rotateOrbit 20s linear infinite;
    }

    @keyframes rotateOrbit {
      0% {
        transform: rotate(0deg);
      }
      100% {
        transform: rotate(360deg);
      }
    }

    .profile-item {
      position: absolute;
      width: 60px;
      height: 60px;
      border-radius: 50%;
      overflow: hidden;
      border: 3px solid white;
      cursor: pointer;
      pointer-events: all;
    }

    .profile-item img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  </style>
</head>
<body>
  <div style="text-align: center; margin-bottom: 20px;">
    <label>
      <input type="checkbox" id="toggleAnimation" onchange="toggleAnimation()" checked>
      Enable Auto Slide & Rotation
    </label>
    <label style="margin-left: 20px;">
      <select id="animationType" onchange="changeAnimationType()">
        <option value="smooth">Smooth Animation</option>
        <option value="interval">Interval Animation</option>
      </select>
    </label>
  </div>

  <div id="circleProfile1" class="circle-profile-container">
    <div class="circle-profile">
      <img src="https://images.pexels.com/photos/417074/pexels-photo-417074.jpeg?cs=srgb&dl=pexels-souvenirpixels-417074.jpg&fm=jpg" class="center-image" id="centerImage1" />
      <div class="center-content" id="centerText1">
        <h2>Mountain View</h2>
        <p>Beautiful scenic mountain landscape.</p>
        <button>Explore</button>
      </div>

      <div class="orbit animate-smooth" id="orbitContainer1">
        <!-- Orbit items positioned on circle border -->
        <div class="profile-item" style="top: 50%; left: 50%; transform: translate(-50%, -50%) translate(140px, 0);">
          <img src="https://images.pexels.com/photos/417074/pexels-photo-417074.jpeg?cs=srgb&dl=pexels-souvenirpixels-417074.jpg&fm=jpg" onclick="updateCenter('circleProfile1', this)" data-text="Mountain View">
        </div>
        <div class="profile-item" style="top: 50%; left: 50%; transform: translate(-50%, -50%) translate(70px, 121.2px);">
          <img src="https://www.shutterstock.com/shutterstock/photos/2198009967/display_1500/stock-photo-autumn-forest-path-orange-color-tree-red-brown-maple-leaves-in-fall-city-park-nature-scene-in-2198009967.jpg" onclick="updateCenter('circleProfile1', this)" data-text="Autumn Path">
        </div>
        <div class="profile-item" style="top: 50%; left: 50%; transform: translate(-50%, -50%) translate(-70px, 121.2px);">
          <img src="https://media.istockphoto.com/id/1403500817/photo/the-craggies-in-the-blue-ridge-mountains.jpg?s=612x612&w=0&k=20&c=N-pGA8OClRVDzRfj_9AqANnOaDS3devZWwrQNwZuDSk=" onclick="updateCenter('circleProfile1', this)" data-text="Blue Ridge">
        </div>
        <div class="profile-item" style="top: 50%; left: 50%; transform: translate(-50%, -50%) translate(-140px, 0);">
          <img src="https://img.freepik.com/premium-photo/morskie-oko-tatry_1045114-186.jpg?semt=ais_hybrid&w=740" onclick="updateCenter('circleProfile1', this)" data-text="Lake View">
        </div>
        <div class="profile-item" style="top: 50%; left: 50%; transform: translate(-50%, -50%) translate(-70px, -121.2px);">
          <img src="https://i0.wp.com/picjumbo.com/wp-content/uploads/beautiful-fall-nature-scenery-free-image.jpeg?w=600&quality=80" onclick="updateCenter('circleProfile1', this)" data-text="Fall Nature">
        </div>
        <div class="profile-item" style="top: 50%; left: 50%; transform: translate(-50%, -50%) translate(70px, -121.2px);">
          <img src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSHAeOc7RmFyNgziH9kKsDmcLWqFM6Ek1Yy3g&s" onclick="updateCenter('circleProfile1', this)" data-text="Forest Trail">
        </div>
      </div>
    </div>
  </div>

  <script>
    let autoSlideInterval;
    let intervalRotation;
    let intervalAngle = 0;

    function updateCenter(id, el) {
      const container = document.getElementById(id);
      const centerImage = container.querySelector('.center-image');
      const centerText = container.querySelector('.center-content');
      centerImage.src = el.src;
      const title = el.getAttribute('data-text');
      centerText.innerHTML = `
        <h2>${title}</h2>
        <p>Enjoy the beauty of ${title}.</p>
        <button>Explore</button>
      `;
    }

    function startAutoSlide() {
      const items = document.querySelectorAll('#orbitContainer1 img');
      let index = 0;
      autoSlideInterval = setInterval(() => {
        updateCenter('circleProfile1', items[index]);
        index = (index + 1) % items.length;
      }, 3000);
    }

    function stopAutoSlide() {
      clearInterval(autoSlideInterval);
    }

    function toggleAnimation() {
      const orbit = document.getElementById('orbitContainer1');
      const isChecked = document.getElementById('toggleAnimation').checked;
      if (isChecked) {
        startAutoSlide();
        changeAnimationType();
      } else {
        orbit.classList.remove('animate-smooth');
        clearInterval(intervalRotation);
        stopAutoSlide();
      }
    }

    function changeAnimationType() {
      const orbit = document.getElementById('orbitContainer1');
      const type = document.getElementById('animationType').value;
      orbit.classList.remove('animate-smooth');
      clearInterval(intervalRotation);
      orbit.style.transform = ''; // Reset

      if (type === 'smooth') {
        orbit.classList.add('animate-smooth');
      } else if (type === 'interval') {
        intervalAngle = 0;
        intervalRotation = setInterval(() => {
          orbit.style.transform = `rotate(${intervalAngle}deg)`;
          intervalAngle = (intervalAngle + 2) % 360;
        }, 1000);
      }
    }

    startAutoSlide();
  </script>
</body>
</html>
