<?php
/**
 * BlockRevealText::render_callback()
 *
 * @package MEE\Modules\BlockRevealText
 * @since ??
 */

namespace MEE\Modules\BlockRevealText\BlockRevealTextTrait;

if ( ! defined( 'ABSPATH' ) ) {
	die( 'Direct access forbidden.' );
}

// phpcs:disable ET.Sniffs.ValidVariableName.UsedPropertyNotSnakeCase -- WP use snakeCase in \WP_Block_Parser_Block

use ET\Builder\Packages\Module\Module;
use ET\Builder\Framework\Utility\HTMLUtility;
use ET\Builder\FrontEnd\BlockParser\BlockParserStore;
use ET\Builder\Packages\Module\Options\Element\ElementComponents;
use MEE\Modules\BlockRevealText\BlockRevealText;

trait RenderCallbackTrait {

	/**
	 * BlockRevealText render callback which outputs server side rendered HTML on the Front-End.
	 *
	 * @since ??
	 * @param array          $attrs    Block attributes that were saved by VB.
	 * @param string         $content  Block content.
	 * @param WP_Block       $block    Parsed block object that being rendered.
	 * @param ModuleElements $elements ModuleElements instance.
	 *
	 * @return string HTML rendered of Static module.
	 */
	public static function render_callback( $attrs, $content, $block, $elements ) {
		// Image.
		$revealDirection = $attrs['revealAnimation']['advanced']['type']['desktop']['value'] ?? '';
		$animationDelay = $attrs['revealAnimation']['advanced']['revealDelay']['desktop']['value'] ?? '';


		$props = [
            'animationDelay' => $animationDelay,
        ];
		

		// revealText.
		$revealText = $elements->render(
			[
				'attrName' => 'revealText',
			]
		);

		// reveal_container.
		$reveal_container = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => "dotm_block_text_reveal_reveal dotm_optimize_image_reveal_{$revealDirection}",
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => "",
			]
		);


		// container_wraper.
		$container_wraper = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'dotm_block_text_reveal_wrapper',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $revealText . $reveal_container,
			]
		);

		$parent       = BlockParserStore::get_parent( $block->parsed_block['id'], $block->parsed_block['storeInstance'] );
		$parent_attrs = $parent->attrs ?? [];

		return Module::render(
			[
				// FE only.
				'orderIndex'          => $block->parsed_block['orderIndex'],
				'storeInstance'       => $block->parsed_block['storeInstance'],

				// VB equivalent.
				'attrs'               => $attrs,
				'elements'            => $elements,
				'id'                  => $block->parsed_block['id'],
				'name'                => $block->block_type->name,
				'moduleCategory'      => $block->block_type->category,
				'classnamesFunction'  => [ BlockRevealText::class, 'module_classnames' ],
				'stylesComponent'     => [ BlockRevealText::class, 'module_styles' ],
				'scriptDataComponent' => [ BlockRevealText::class, 'module_script_data' ],
				'parentAttrs'         => $parent_attrs,
				'parentId'            => $parent->id ?? '',
				'parentName'          => $parent->blockName ?? '',
				'children'            => [
					ElementComponents::component(
						[
							'attrs'         => $attrs['module']['decoration'] ?? [],
							'id'            => $block->parsed_block['id'],

							// FE only.
							'orderIndex'    => $block->parsed_block['orderIndex'],
							'storeInstance' => $block->parsed_block['storeInstance'],
						]
					),
					HTMLUtility::render(
						[
							'tag'               => 'div',
							'attributes'        => [
								'class' => 'dotm_block_text_reveal_container',
							],
							'childrenSanitizer' => 'et_core_esc_previously',
							'children'          => $container_wraper,
						]
					),
					HTMLUtility::render(
                        [
                            'tag' => 'script',
                            'attributes' => [],
                            'childrenSanitizer' => 'et_core_esc_previously',
                            'children' => "document.addEventListener('DOMContentLoaded', function() {
                                const props = " . json_encode($props) . ";
                                revealText(props);
                            });",
                        ]
                    ),
				],
			]
		);
	}
}
