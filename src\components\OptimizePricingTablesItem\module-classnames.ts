import { ModuleClassnamesParams, textOptionsClassnames } from '@divi/module';
import { OptimizePricingTablesItemAttrs } from './types';


/**
 * Module classnames function for Divi 4 Module.
 *
 * @since ??
 *
 * @param {ModuleClassnamesParams<OptimizePricingTablesItemAttrs>} param0 Function parameters.
 */
export const moduleClassnames = ({
  classnamesInstance,
  attrs,
}: ModuleClassnamesParams<OptimizePricingTablesItemAttrs>): void => {
  // Text Options.
  classnamesInstance.add(textOptionsClassnames(attrs?.module?.advanced?.text ?? {}, {color: false}));
};
