<?php
/**
 * OptimizePricingTablesItem::render_callback()
 *
 * @package MEE\Modules\OptimizePricingTablesItem
 * @since ??
 */

namespace MEE\Modules\OptimizePricingTablesItem\OptimizePricingTablesItemTrait;

if ( ! defined( 'ABSPATH' ) ) {
	die( 'Direct access forbidden.' );
}

// phpcs:disable ET.Sniffs.ValidVariableName.UsedPropertyNotSnakeCase -- WP use snakeCase in \WP_Block_Parser_Block

use ET\Builder\Packages\Module\Module;
use ET\Builder\Framework\Utility\HTMLUtility;
use ET\Builder\FrontEnd\BlockParser\BlockParserStore;
use ET\Builder\Packages\IconLibrary\IconFont\Utils;
use ET\Builder\Packages\Module\Options\Element\ElementComponents;
use ET\Builder\Packages\ModuleLibrary\ModuleRegistration;
use MEE\Modules\OptimizePricingTablesItem\OptimizePricingTablesItem;

trait RenderCallbackTrait {
	use ModuleClassnamesTrait;
	use ModuleStylesTrait;

	/**
	 * OptimizePricingTablesItem render callback which outputs server side rendered HTML on the Front-End.
	 *
	 * @since ??
	 *
	 * @param array          $attrs Block attributes that were saved by VB.
	 * @param string         $content          Block content.
	 * @param WP_Block       $block            Parsed block object that being rendered.
	 * @param ModuleElements $elements         ModuleElements instance.
	 *
	 * @return string HTML rendered of OptimizePricingTablesItem.
	 */
	public static function render_callback( $attrs, $content, $block, $elements ) {
		$parent = BlockParserStore::get_parent( $block->parsed_block['id'], $block->parsed_block['storeInstance'] );

		$parent_attrs = $parent->attrs ?? [];

		$parent_default_attributes = ModuleRegistration::get_default_attrs( 'dotm/optimize-pricing-tables' );
		$parent_attrs_with_default = array_replace_recursive( $parent_default_attributes, $parent_attrs );

		// Icon.
		$icon_value = $attrs['icon']['innerContent']['desktop']['value'] ?? $parent_attrs_with_default['icon']['innerContent']['desktop']['value'] ?? [];

		$useImage = $attrs['image']['advanced']['useImage']['desktop']['value'] ?? "off";
		$useFeature = $attrs['featureText']['advanced']['useFeature']['desktop']['value'] ?? 'off';
		$btn_link = $attrs['button']['advanced']['link']['desktop']['value']['url'] ?? '';
		$btn_target = $attrs['button']['advanced']['link']['desktop']['value']['target'] ?? '';
		
		// Content.
		$content = $elements->render(
			[
				'attrName'      => 'content',
				'hoverSelector' => '{{parentSelector}}',
			]
		);

		

		

		// image.
		$image = $elements->render(
			[
				'attrName'      => 'image',
				'hoverSelector' => '{{parentSelector}}',
			]
		);

		$icon       = HTMLUtility::render(
			[
				'tag'               => 'span',
				'attributes'        => [
					'class' => 'dotm_optimize_pricing_tables_item_icon',
				],
				'childrenSanitizer' => 'esc_html',
				'children'          => Utils::process_font_icon( $icon_value ),
			]
		);

		// item_icon_container.
		$item_icon_container = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'dotm_optimize_pricing_tables_item_icon_container',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $icon,
			]
		);

		// Title.
		$title = $elements->render(
			[
				'attrName'      => 'title',
				'hoverSelector' => '{{parentSelector}}',
			]
		);

		// subTitle.
		$subTitle = $elements->render(
			[
				'attrName'      => 'subTitle',
				'hoverSelector' => '{{parentSelector}}',
			]
		);

		// item_card_header.
		$item_card_header = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'dotm_optimize_pricing_tables_item_header',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => ($useImage === "off" ? $item_icon_container : "") . ($useImage === "on" ? $image : "") . $title . $subTitle,
			]
		);

		// currency.
		$currency = $elements->render(
			[
				'attrName'      => 'currency',
				'hoverSelector' => '{{parentSelector}}',
			]
		);

		// price.
		$price = $elements->render(
			[
				'attrName'      => 'price',
				'hoverSelector' => '{{parentSelector}}',
			]
		);

		// item_price_wrapper.
		$item_price_wrapper = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'dotm_optimize_pricing_tables_item_price_wrapper',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $currency . $price,
			]
		);

		// frequency.
		$frequency = $elements->render(
			[
				'attrName'      => 'frequency',
				'hoverSelector' => '{{parentSelector}}',
			]
		);

		// item_price_section.
		$item_price_section = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'dotm_optimize_pricing_tables_item_price_section',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $item_price_wrapper . $frequency,
			]
		);

		// content.
		$content = $elements->render(
			[
				'attrName'      => 'content',
				'hoverSelector' => '{{parentSelector}}',
			]
		);

		// item_features.
		$item_features = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'dotm_optimize_pricing_tables_item_features',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $content,
			]
		);

		// button.
		$button = $elements->render(
			[
				'attrName'      => 'button',
				'hoverSelector' => '{{parentSelector}}',
				'attributes' => [
					'class' => "dotm_optimize_pricing_tables_item_button",
					'href' => $btn_link,
					'target' => $btn_target ? "_blank" : "_self",
				],
			]
		);

		// item_button_container.
		$item_button_container = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'dotm_optimize_pricing_tables_item_button_container',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $button,
			]
		);

		// item_card_container.
		$item_card_container = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'dotm_optimize_pricing_tables_item_card',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $item_card_header . $item_price_section . $item_features . $item_button_container,
			]
		);

		// featureText.
		$featureText = $elements->render(
			[
				'attrName'      => 'featureText',
				'hoverSelector' => '{{parentSelector}}',
			]
		);

		return Module::render(
			[
				// FE only.
				'orderIndex'         => $block->parsed_block['orderIndex'],
				'storeInstance'      => $block->parsed_block['storeInstance'],

				// VB equivalent.
				'id'                 => $block->parsed_block['id'],
				'name'               => $block->block_type->name,
				'moduleCategory'     => $block->block_type->category,
				'attrs'              => $attrs,
				'elements'           => $elements,
				'classnamesFunction' => [ OptimizePricingTablesItem::class, 'module_classnames' ],
				'stylesComponent'    => [ OptimizePricingTablesItem::class, 'module_styles' ],
				'parentAttrs'        => $parent_attrs,
				'parentId'           => $parent->id ?? '',
				'parentName'         => $parent->blockName ?? '',
				'children'           => ElementComponents::component(
					[
						'attrs'         => $attrs['module']['decoration'] ?? [],
						'id'            => $block->parsed_block['id'],

						// FE only.
						'orderIndex'    => $block->parsed_block['orderIndex'],
						'storeInstance' => $block->parsed_block['storeInstance'],
					]
				) . ($useFeature === "on" ? $featureText : "") . $item_card_container,
			]
		);
	}
}
