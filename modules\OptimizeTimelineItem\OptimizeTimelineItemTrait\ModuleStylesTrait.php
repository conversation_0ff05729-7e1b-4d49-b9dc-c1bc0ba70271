<?php

/**
 * OptimizeTimelineItem::module_styles().
 *
 * @package Builder\Packages\ModuleLibrary
 * @since ??
 */

namespace MEE\Modules\OptimizeTimelineItem\OptimizeTimelineItemTrait;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

use ET\Builder\FrontEnd\Module\Style;
use ET\Builder\Packages\Module\Options\Text\TextStyle;
use ET\Builder\Packages\Module\Options\Css\CssStyle;
use ET\Builder\Packages\Module\Layout\Components\StyleCommon\CommonStyle;
use ET\Builder\Packages\ModuleLibrary\ModuleRegistration;
use MEE\Modules\OptimizeTimelineItem\OptimizeTimelineItem;

trait ModuleStylesTrait
{

	use CustomCssTrait;
	use StyleDeclarationTrait;

	/**
	 * OptimizeTimelineItem's style components.
	 *
	 * This function is equivalent of JS function ModuleStyles located in
	 * src/components/OptimizeTimelineItem/styles.tsx.
	 *
	 * @param array $args {
	 *     An array of arguments.
	 *
	 * @type string $id Module ID. In VB, the ID of module is UUIDV4. In FE, the ID is order index.
	 * @type string $name Module name.
	 * @type string $attrs Module attributes.
	 * @type string $parentAttrs Parent attrs.
	 * @type string $orderClass Selector class name.
	 * @type string $parentOrderClass Parent selector class name.
	 * @type string $wrapperOrderClass Wrapper selector class name.
	 * @type string $settings Custom settings.
	 * @type string $state Attributes state.
	 * @type string $mode Style mode.
	 * @type ModuleElements $elements ModuleElements instance.
	 * }
	 * @since ??
	 */
	public static function module_styles($args)
	{
		$attrs        = $args['attrs'] ?? [];
		$order_class  = $args['orderClass'];
		$elements     = $args['elements'];
		$settings     = $args['settings'] ?? [];
		$parent_attrs = $args['parentAttrs'] ?? [];

		$parent_default_attributes = ModuleRegistration::get_default_attrs('dotm/optimize-timeline');
		$parent_attrs_with_default = array_replace_recursive($parent_default_attributes, $parent_attrs);


		$content_timeline_selector = "{$order_class} .dotm_optimize_timeline_item_timeline_content";
		$image_container_selector = "{$order_class} .dotm_optimize_timeline_item_image_container";
		$icon_container_selector = "{$order_class} .dotm_optimize_timeline_item_icon_container";
		$button_container_selector = "{$order_class} .dotm_optimize_timeline_item_button_container";

		$imageIconPlacement = $attrs['image']['advanced']['placement']['desktop']['value'] ?? '';
		$imageIconVR_alignment = $attrs['image']['advanced']['vr_alignment']['desktop']['value'] ?? '';
		$iconImage = $attrs['pointer']['advanced']['iconImage']['desktop']['value'] ?? 'icon';
		$iconAlignment = $attrs['icon']['advanced']['alignment']['desktop']['value'] ?? 'na';
		$imageAlignment = $attrs['image']['decoration']['alignment']['desktop']['value'] ?? 'na';

		Style::add(
			[
				'id'            => $args['id'],
				'name'          => $args['name'],
				'orderIndex'    => $args['orderIndex'],
				'storeInstance' => $args['storeInstance'],
				'styles'        => [
					// Module.
					$elements->style(
						[
							'attrName'   => 'module',
							'styleProps' => [
								'disabledOn'     => [
									'disabledModuleVisibility' => $settings['disabledModuleVisibility'] ?? null,
								],
								'advancedStyles' => [
									[
										'componentName' => 'divi/text',
										'props'         => [
											'selector' => $button_container_selector,
											'attr'     => $attrs['button']['advanced']['text'] ?? [],
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $content_timeline_selector,
											'attr'     => $attrs['image']['advanced']['placement'] ?? [],
											'property' => $imageIconPlacement === "left" ? "display: flex; gap: 15px; align-items: {$imageIconVR_alignment};" : ""
										]
									]
								]
							],
						]
					),

					// optional_container.
					$elements->style(
						[
							'attrName' => 'optional_container',
						]
					),

					// container.
					$elements->style(
						[
							'attrName' => 'container',
						]
					),

					// oppsiteText.
					$elements->style(
						[
							'attrName' => 'oppsiteText',
						]
					),

					// image.
					$elements->style(
						[
							'attrName'   => 'image',
							'styleProps' => [
								'advancedStyles' => [

									[
										'componentName' => 'divi/common',
										'props'         => [
											'attr'     => $attrs['image']['decoration']['imgWidth'] ?? $parent_attrs_with_default['image']['decoration']['imgWidth'],
											'property' => 'width',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $image_container_selector,
											'attr'     => $attrs['image']['decoration']['alignment'] ?? $parent_attrs_with_default['image']['decoration']['alignment'],
											'property' => "text-align: {$imageAlignment} !important;",
										]
									],
								]
							]
						]
					),

					// Title.
					$elements->style(
						[
							'attrName' => 'title',
						]
					),

					// Content.
					$elements->style(
						[
							'attrName' => 'content',
						]
					),

					// button.
					$elements->style(
						[
							'attrName' => 'button',
						]
					),

					// Icon.
					$elements->style(
						[
							'attrName'   => 'icon',
							'styleProps' => [
								'advancedStyles' => [
									[
										'componentName' => 'divi/common',
										'props'         => [
											'attr'                => $attrs['icon']['innerContent'] ?? $parent_attrs_with_default['icon']['innerContent'] ?? [],
											'declarationFunction' => [OptimizeTimelineItem::class, 'icon_font_declaration'],
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'attr'     => $attrs['icon']['advanced']['color'] ?? $parent_attrs_with_default['icon']['advanced']['color'] ?? [],
											'property' => 'color',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'attr'     => $attrs['icon']['advanced']['size'] ?? $parent_attrs_with_default['icon']['advanced']['size'] ?? [],
											'property' => 'font-size',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $icon_container_selector,
											'attr'     => $attrs['icon']['advanced']['alignment'] ?? $parent_attrs_with_default['icon']['advanced']['alignment'] ?? [],
											'property' => "text-align: {$iconAlignment} !important;",
										]
									],
								]
							]
						]
					),

					// pointerIcon.
					$elements->style(
						[
							'attrName'   => 'pointerIcon',
							'styleProps' => [
								'advancedStyles' => [
									[
										'componentName' => 'divi/common',
										'props'         => [
											'attr'                => $attrs['pointerIcon']['innerContent'] ?? $parent_attrs_with_default['pointerIcon']['innerContent'] ?? [],
											'declarationFunction' => [OptimizeTimelineItem::class, 'icon_font_declaration'],
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'attr'     => $attrs['pointerIcon']['advanced']['color'] ?? $parent_attrs_with_default['pointerIcon']['advanced']['color'] ?? [],
											'property' => 'color',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'attr'     => $attrs['pointerIcon']['advanced']['size'] ?? $parent_attrs_with_default['pointerIcon']['advanced']['size'] ?? [],
											'property' => 'font-size',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'attr'     => $attrs['pointerIcon']['decoration']['size'] ?? $parent_attrs_with_default['pointerIcon']['decoration']['size'] ?? [],
											'property' => 'width',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'attr'     => $attrs['pointerIcon']['decoration']['size'] ?? $parent_attrs_with_default['pointerIcon']['decoration']['size'] ?? [],
											'property' => 'height',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'attr'     => $attrs['pointerIcon']['decoration']['background'] ?? $parent_attrs_with_default['pointerIcon']['decoration']['background'] ?? [],
											'property' => 'background',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'attr'     => $attrs['pointerIcon']['decoration']['color'] ?? $parent_attrs_with_default['pointerIcon']['decoration']['color'] ?? [],
											'property' => $iconImage === "icon" ? "color" : "",
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'attr'     => $attrs['pointerIcon']['decoration']['IconSize'] ?? $parent_attrs_with_default['pointerIcon']['decoration']['IconSize'] ?? [],
											'property' => $iconImage === "icon" ? "font-size" : "",
										]
									],
								]
							]
						]
					),

					/*
					 * We need to add CssStyle at the very bottom of other
					 * components so that custom css can override module styles
					 * till we find a more elegant solution.
					 */
					CssStyle::style(
						[
							'selector'  => $args['orderClass'],
							'attr'      => $attrs['css'] ?? [],
							'cssFields' => self::custom_css(),
						]
					),

					// ATTENTION: The code is intentionally added and commented in FE only as an example of expected value format.
					// If you have custom style processing, the style output should be passed as an `array` of style declarations
					// to the `styles` property of the `Style::add` method. For example:
					// [
					// 	[
					// 		'atRules'     => false,
					// 		'selector'    => $icon_selector,
					// 		'declaration' => 'color: red;'
					// 	],
					// 	[
					// 		'atRules'     => '@media only screen and (max-width: 767px)',
					// 		'selector'    => $icon_selector,
					// 		'declaration' => 'color: green;'
					// 	],
					// ],

					// The code below is an example of how to use the `CssStyle::style` method to generate CSS style.
					CssStyle::style(
						[
							'selector'  => $args['orderClass'],
							'attr'      => $attrs['css'] ?? [],
							'cssFields' => self::custom_css(),
						]
					),
				],
			]
		);
	}
}
