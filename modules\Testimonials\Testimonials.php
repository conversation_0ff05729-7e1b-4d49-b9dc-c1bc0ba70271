<?php
/**
 * Module: Testimonials class.
 *
 * @package MEE\Modules\Testimonials
 * @since ??
 */

namespace MEE\Modules\Testimonials;

if ( ! defined( 'ABSPATH' ) ) {
	die( 'Direct access forbidden.' );
}

use ET\Builder\Framework\DependencyManagement\Interfaces\DependencyInterface;
use ET\Builder\Packages\ModuleLibrary\ModuleRegistration;


/**
 * `Testimonials` is consisted of functions used for Testimonials such as Front-End rendering, REST API Endpoints etc.
 *
 * This is a dependency class and can be used as a dependency for `DependencyTree`.
 *
 * @since ??
 */
class Testimonials implements DependencyInterface {
	use TestimonialsTrait\RenderCallbackTrait;
	use TestimonialsTrait\ModuleClassnamesTrait;
	use TestimonialsTrait\ModuleStylesTrait;
	use TestimonialsTrait\ModuleScriptDataTrait;

	/**
	 * Loads `Testimonials` and registers Front-End render callback and REST API Endpoints.
	 *
	 * @since ??
	 *
	 * @return void
	 */
	public function load() {
		$module_json_folder_path = DIVIOPTIMIZER_MODULES_JSON_PATH . 'Testimonials/';

		add_action(
			'init',
			function() use ( $module_json_folder_path ) {
				ModuleRegistration::register_module(
					$module_json_folder_path,
					[
						'render_callback' => [ Testimonials::class, 'render_callback' ],
					]
				);
			}
		);
	}
}
