<?php

/**
 * Module: OptimizerCounter class.
 *
 * @package MEE\Modules\OptimizerCounter
 * @since ??
 */

namespace MEE\Modules\OptimizerCounter;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

use ET\Builder\Framework\DependencyManagement\Interfaces\DependencyInterface;
use ET\Builder\Packages\ModuleLibrary\ModuleRegistration;


/**
 * `OptimizerCounter` is consisted of functions used for Static Module such as Front-End rendering, REST API Endpoints etc.
 *
 * This is a dependency class and can be used as a dependency for `DependencyTree`.
 *
 * @since ??
 */
class OptimizerCounter implements DependencyInterface
{
	use OptimizerCounterTrait\RenderCallbackTrait;
	use OptimizerCounterTrait\ModuleClassnamesTrait;
	use OptimizerCounterTrait\ModuleStylesTrait;
	use OptimizerCounterTrait\ModuleScriptDataTrait;

	/**
	 * Loads `OptimizerCounter` and registers Front-End render callback and REST API Endpoints.
	 *
	 * @since ??
	 *
	 * @return void
	 */
	public function load()
	{
		$module_json_folder_path = DIVIOPTIMIZER_MODULES_JSON_PATH . 'OptimizerCounter/';

		add_action(
			'init',
			function () use ($module_json_folder_path) {
				ModuleRegistration::register_module(
					$module_json_folder_path,
					[
						'render_callback' => [OptimizerCounter::class, 'render_callback'],
					]
				);
			}
		);
	}
}
