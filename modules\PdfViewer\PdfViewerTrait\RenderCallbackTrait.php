<?php

/**
 * PdfViewer::render_callback()
 *
 * @package MEE\Modules\PdfViewer
 * @since ??
 */

namespace MEE\Modules\PdfViewer\PdfViewerTrait;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

// phpcs:disable ET.Sniffs.ValidVariableName.UsedPropertyNotSnakeCase -- WP use snakeCase in \WP_Block_Parser_Block

use ET\Builder\Packages\Module\Module;
use ET\Builder\Framework\Utility\HTMLUtility;
use ET\Builder\FrontEnd\BlockParser\BlockParserStore;
use ET\Builder\Packages\Module\Options\Element\ElementComponents;
use MEE\Modules\PdfViewer\PdfViewer;

trait RenderCallbackTrait
{

	/**
	 * Static module render callback which outputs server side rendered HTML on the Front-End.
	 *
	 * @since ??
	 * @param array          $attrs    Block attributes that were saved by VB.
	 * @param string         $content  Block content.
	 * @param WP_Block       $block    Parsed block object that being rendered.
	 * @param ModuleElements $elements ModuleElements instance.
	 *
	 * @return string HTML rendered of Static module.
	 */
	public static function render_callback($attrs, $content, $block, $elements)
	{
		$url = $attrs['pdf_link']['innerContent']['desktop']['value'] ?? '';

		$page_num = HTMLUtility::render(
			[
				'tag'               => 'span',
				'attributes'        => [
					'id'       => 'page-num',
					'class'    => 'dotm_page_num',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => '1'
			]
		);

		$page_count = HTMLUtility::render(
			[
				'tag'               => 'span',
				'attributes'        => [
					'id'    => 'page-count',
					'class' => 'dotm_page_count',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => '0'
			]
		);
		$page_text = HTMLUtility::render(
			[
				'tag'               => 'span',
				'attributes'        => [],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => 'Page:'
			]
		);
		$page_info = HTMLUtility::render(
			[
				'tag'               => 'span',
				'attributes'        => [
					'class'    => 'page-info',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $page_text . $page_num . '/' . $page_count
			]
		);
		$prev_btn = HTMLUtility::render(
			[
				'tag'               => 'button',
				'attributes'        => [
					'id'    => 'prev-page',
					'class' => 'dotm_toolbar__button dotm_toolbar__button_prev',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => '<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6 dotm_svg">
                <path stroke-linecap="round" stroke-linejoin="round" d="M21 16.811c0 .864-.933 1.406-1.683.977l-7.108-4.061a1.125 1.125 0 0 1 0-1.954l7.108-4.061A1.125 1.125 0 0 1 21 8.689v8.122ZM11.25 16.811c0 .864-.933 1.406-1.683.977l-7.108-4.061a1.125 1.125 0 0 1 0-1.954l7.108-4.061a1.125 1.125 0 0 1 1.683.977v8.122Z" />
              </svg>'
			]
		);

		$next_btn = HTMLUtility::render(
			[
				'tag'               => 'button',
				'attributes'        => [
					'id'    => 'next-page',
					'class' => 'dotm_toolbar__button dotm_toolbar__button_next',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => '<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6 dotm_svg">
                <path stroke-linecap="round" stroke-linejoin="round" d="M3 8.689c0-.864.933-1.406 1.683-.977l7.108 4.061a1.125 1.125 0 0 1 0 1.954l-7.108 4.061A1.125 1.125 0 0 1 3 16.811V8.69ZM12.75 8.689c0-.864.933-1.406 1.683-.977l7.108 4.061a1.125 1.125 0 0 1 0 1.954l-7.108 4.061a1.125 1.125 0 0 1-1.683-.977V8.69Z" />
              </svg>'
			]
		);

		$zoom_lavel = HTMLUtility::render(
			[
				'tag'               => 'span',
				'attributes'        => [
					'id'    => 'zoom-level',
					'class' => 'zoom-level ',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => '100%'
			]
		);
		$zoom_out = HTMLUtility::render(
			[
				'tag'               => 'button',
				'attributes'        => [
					'id'    => 'zoom-out',
					'class' => 'dotm_toolbar__button dotm_toolbar__button_zoom-out',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => '<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6 dotm_svg">
  <path stroke-linecap="round" stroke-linejoin="round" d="m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607ZM13.5 10.5h-6" />
</svg>'
			]
		);

		$zoom_in = HTMLUtility::render(
			[
				'tag'               => 'button',
				'attributes'        => [
					'id'    => 'zoom-in',
					'class' => 'dotm_toolbar__button dotm_toolbar__button_zoom-in'
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => '<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6 dotm_svg">
  <path stroke-linecap="round" stroke-linejoin="round" d="m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607ZM10.5 7.5v6m3-3h-6" />
</svg>'
			]
		);

		$zoom_controls = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class'    => 'zoom-controls',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          =>  $zoom_out . $zoom_lavel . $zoom_in
			]
		);
		$rotate_btn = HTMLUtility::render(
			[
				'tag'               => 'button',
				'attributes'        => [
					'id'    => 'rotate',
					'class' => 'dotm_toolbar__button ',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => '<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6 dotm_svg">
                <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 12c0-1.232-.046-2.453-.138-3.662a4.006 4.006 0 0 0-3.7-3.7 48.678 48.678 0 0 0-7.324 0 4.006 4.006 0 0 0-3.7 3.7c-.017.22-.032.441-.046.662M19.5 12l3-3m-3 3-3-3m-12 3c0 1.232.046 2.453.138 3.662a4.006 4.006 0 0 0 3.7 3.7 48.656 48.656 0 0 0 7.324 0 4.006 4.006 0 0 0 3.7-3.7c.017-.22.032-.441.046-.662M4.5 12l3 3m-3-3-3 3" />
              </svg>'
			]
		);

		$donwload_btn = HTMLUtility::render(
			[
				'tag'               => 'button',
				'attributes'        => [
					'id'    => 'download-pdf',
					'class' => 'dotm_toolbar__button dotm_toolbar__button_download',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => '<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6 dotm_svg">
              <path stroke-linecap="round" stroke-linejoin="round" d="M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5M16.5 12 12 16.5m0 0L7.5 12m4.5 4.5V3" />
            </svg>'
			]
		);

		$print_btn = HTMLUtility::render(
			[
				'tag'               => 'button',
				'attributes'        => [
					'id'    => 'print-pdf',
					'class' => 'dotm_toolbar__button dotm_toolbar__button_print',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => '<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6 dotm_svg">
                <path stroke-linecap="round" stroke-linejoin="round" d="M6.72 13.829c-.24.03-.48.062-.72.096m.72-.096a42.415 42.415 0 0 1 10.56 0m-10.56 0L6.34 18m10.94-4.171c.24.03.48.062.72.096m-.72-.096L17.66 18m0 0 .229 2.523a1.125 1.125 0 0 1-1.12 1.227H7.231c-.662 0-1.18-.568-1.12-1.227L6.34 18m11.318 0h1.091A2.25 2.25 0 0 0 21 15.75V9.456c0-1.081-.768-2.015-1.837-2.175a48.055 48.055 0 0 0-1.913-.247M6.34 18H5.25A2.25 2.25 0 0 1 3 15.75V9.456c0-1.081.768-2.015 1.837-2.175a48.041 48.041 0 0 1 1.913-.247m10.5 0a48.536 48.536 0 0 0-10.5 0m10.5 0V3.375c0-.621-.504-1.125-1.125-1.125h-8.25c-.621 0-1.125.504-1.125 1.125v3.659M18 10.5h.008v.008H18V10.5Zm-3 0h.008v.008H15V10.5Z" />
              </svg>'
			]
		);


		$toolber = HTMLUtility::render(

			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'dotm_toolbar',
					'id'    => 'toolbar',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $prev_btn . $page_info . $next_btn . $zoom_controls . $rotate_btn . $donwload_btn . $print_btn
			]
		);

		$canvas = HTMLUtility::render(
			[
				'tag'               => 'canvas',
				'attributes'        => [
					'id'    => 'pdf-canvas',
					'class' => 'dotm_pdf_canvas',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => ''
			]
		);

		$pdf_container = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'id'    => 'pdf-container',
					'class' => 'dotm_pdf_container',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $canvas
			]
		);

		$pdf_view = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'id'    => 'pdf-viewer',
					'class' => 'dotm_pdf-viewer',

				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          =>  $toolber . $pdf_container
			]
		);

		$parent       = BlockParserStore::get_parent($block->parsed_block['id'], $block->parsed_block['storeInstance']);
		$parent_attrs = $parent->attrs ?? [];

		return Module::render(
			[
				// FE only.
				'orderIndex'          => $block->parsed_block['orderIndex'],
				'storeInstance'       => $block->parsed_block['storeInstance'],

				// VB equivalent.
				'attrs'               => $attrs,
				'elements'            => $elements,
				'id'                  => $block->parsed_block['id'],
				'name'                => $block->block_type->name,
				'moduleCategory'      => $block->block_type->category,
				'classnamesFunction'  => [PdfViewer::class, 'module_classnames'],
				'stylesComponent'     => [PdfViewer::class, 'module_styles'],
				'scriptDataComponent' => [PdfViewer::class, 'module_script_data'],
				'parentAttrs'         => $parent_attrs,
				'parentId'            => $parent->id ?? '',
				'parentName'          => $parent->blockName ?? '',
				'children'            => [
					ElementComponents::component(
						[
							'attrs'         => $attrs['module']['decoration'] ?? [],
							'id'            => $block->parsed_block['id'],

							// FE only.
							'orderIndex'    => $block->parsed_block['orderIndex'],
							'storeInstance' => $block->parsed_block['storeInstance'],
						]
					),
					HTMLUtility::render(
						[
							'tag'               => 'div',
							'attributes'        => [
								'class' => 'dotm__inner',
							],
							'childrenSanitizer' => 'et_core_esc_previously',
							'children'          =>  $pdf_view,
						]
					),
					HTMLUtility::render(
						[
							'tag' => 'script',
							'attributes' => [],
							'childrenSanitizer' => 'et_core_esc_previously',
							'children' => "document.addEventListener('DOMContentLoaded', function() {
							const option = " . json_encode($url) . ";
                                pdfViewer(option);
                            });",
						]
					),
				],
			]
		);
	}
}
