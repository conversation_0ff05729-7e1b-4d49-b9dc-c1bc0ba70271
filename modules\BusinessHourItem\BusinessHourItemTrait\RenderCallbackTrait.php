<?php

/**
 * BusinessHourItem::render_callback()
 *
 * @package MEE\Modules\BusinessHourItem
 * @since ??
 */

namespace MEE\Modules\BusinessHourItem\BusinessHourItemTrait;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

// phpcs:disable ET.Sniffs.ValidVariableName.UsedPropertyNotSnakeCase -- WP use snakeCase in \WP_Block_Parser_Block

use ET\Builder\Packages\Module\Module;
use ET\Builder\Framework\Utility\HTMLUtility;
use ET\Builder\FrontEnd\BlockParser\BlockParserStore;
use ET\Builder\Packages\IconLibrary\IconFont\Utils;
use ET\Builder\Packages\Module\Options\Element\ElementComponents;
use ET\Builder\Packages\ModuleLibrary\ModuleRegistration;
use MEE\Modules\BusinessHourItem\BusinessHourItem;

trait RenderCallbackTrait
{
	use ModuleClassnamesTrait;
	use ModuleStylesTrait;

	/**
	 * Child module render callback which outputs server side rendered HTML on the Front-End.
	 *
	 * @since ??
	 *
	 * @param array          $attrs Block attributes that were saved by VB.
	 * @param string         $content          Block content.
	 * @param WP_Block       $block            Parsed block object that being rendered.
	 * @param ModuleElements $elements         ModuleElements instance.
	 *
	 * @return string HTML rendered of Child module.
	 */
	public static function render_callback($attrs, $content, $block, $elements)
	{
		$parent = BlockParserStore::get_parent($block->parsed_block['id'], $block->parsed_block['storeInstance']);

		$parent_attrs = $parent->attrs ?? [];

		$parent_default_attributes = ModuleRegistration::get_default_attrs('dotm/business-hour');
		$parent_attrs_with_default = array_replace_recursive($parent_default_attributes, $parent_attrs);


		// Day.
		$day = $elements->render(
			[
				'attrName'      => 'day',
				'hoverSelector' => '{{parentSelector}}',
			]
		);

		// time.
		$time = $elements->render(
			[
				'attrName'      => 'time',
				'hoverSelector' => '{{parentSelector}}',
			]
		);

		$daySpan =  HTMLUtility::render(
			[
				'tag'               => 'span',
				'attributes'        => [
					'class' => 'dotm_business_hour_item_wrapper_day',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $day,
			]
		);


		$timeSpan =  HTMLUtility::render(
			[
				'tag'               => 'span',
				'attributes'        => [
					'class' => 'dotm_business_hour_item_wrapper_time',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $time,
			]
		);

		$separator = HTMLUtility::render(
			[
				'tag' 	=> "div",
				'attributes' 	=> [
					'class' => 'dotm_business_hour_item_wrapper_separator',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
			]
		);


		$itemWrapper = HTMLUtility::render(
			[
				'tag' 	=> "div",
				'attributes' 	=> [
					'class' => 'dotm_business_hour_item_wrapper',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $daySpan . $separator . $timeSpan,
			]
		);
		return Module::render(
			[
				// FE only.
				'orderIndex'         => $block->parsed_block['orderIndex'],
				'storeInstance'      => $block->parsed_block['storeInstance'],

				// VB equivalent.
				'id'                 => $block->parsed_block['id'],
				'name'               => $block->block_type->name,
				'moduleCategory'     => $block->block_type->category,
				'attrs'              => $attrs,
				'elements'           => $elements,
				'classnamesFunction' => [BusinessHourItem::class, 'module_classnames'],
				'stylesComponent'    => [BusinessHourItem::class, 'module_styles'],
				'parentAttrs'        => $parent_attrs,
				'parentId'           => $parent->id ?? '',
				'parentName'         => $parent->blockName ?? '',
				'children'           => ElementComponents::component(
					[
						'attrs'         => $attrs['module']['decoration'] ?? [],
						'id'            => $block->parsed_block['id'],

						// FE only.
						'orderIndex'    => $block->parsed_block['orderIndex'],
						'storeInstance' => $block->parsed_block['storeInstance'],
					]
				) . $itemWrapper,
			]
		);
	}
}
