// External dependencies.
import React, { ReactElement } from "react";

// WordPress dependencies.
import { __ } from "@wordpress/i18n";

// Divi dependencies.
import {
  AnimationGroup,
  BorderGroup,
  BoxShadowGroup,
  FieldContainer,
  FiltersGroup,
  FontGroup,
  FontBodyGroup,
  SizingGroup,
  SpacingGroup,
  TextGroup,
  TransformGroup,
  BackgroundGroup,
  ButtonGroupContainer,
} from "@divi/module";
import { GroupContainer } from "@divi/modal";
import { ColorPickerContainer, RangeContainer } from "@divi/field-library";
import { type Module } from "@divi/types";
import { OptimizePricingTablesAttrs } from "./types";

export const SettingsDesign = ({
  defaultSettingsAttrs,
}: Module.Settings.Panel.Props<OptimizePricingTablesAttrs>): ReactElement => (
  <React.Fragment>
    <GroupContainer
      id="gap"
      title={__("Eatch Gap", "divi-optimaizer-modules")}
    >
      <FieldContainer
        attrName="image.decoration.gap"
        label={__("Eatch Gap", "divi-optimaizer-modules")}
        description={__(
          "Input your value to action here.",
          "divi-optimaizer-modules"
        )}
        features={{
          sticky: false,
        }}
        defaultAttr={defaultSettingsAttrs?.image?.decoration?.gap}
      >
        <RangeContainer />
      </FieldContainer>
    </GroupContainer>
    <GroupContainer
      id="icon"
      title={__("Icon Style", "divi-optimaizer-modules")}
    >
      <FieldContainer
        attrName="icon.advanced.color"
        label={__("Icon Color", "divi-optimaizer-modules")}
        description={__(
          "Input your value to action title here.",
          "divi-optimaizer-modules"
        )}
        features={{
          sticky: false,
        }}
        defaultAttr={defaultSettingsAttrs?.icon?.advanced?.color}
      >
        <ColorPickerContainer />
      </FieldContainer>
      <FieldContainer
        attrName="icon.advanced.size"
        label={__("Icon Size", "divi-optimaizer-modules")}
        description={__(
          "Input your value to action title here.",
          "divi-optimaizer-modules"
        )}
        features={{
          sticky: false,
        }}
        defaultAttr={defaultSettingsAttrs?.icon?.advanced?.size}
      >
        <RangeContainer />
      </FieldContainer>
      <BackgroundGroup
        attrName="icon.decoration.background"
        grouped={false}
        fieldLabel="Icon"
        defaultGroupAttr={defaultSettingsAttrs?.icon?.decoration?.background}
      />
      <SpacingGroup
        attrName="icon.decoration.spacing"
        grouped={false}
        fieldLabel="Icon"
        defaultGroupAttr={defaultSettingsAttrs?.icon?.decoration?.spacing}
      />
      <BorderGroup
        attrName="icon.decoration.border"
        grouped={false}
        fieldLabel="Icon"
        defaultGroupAttr={defaultSettingsAttrs?.icon?.decoration?.border}
      />
    </GroupContainer>
    <GroupContainer
      id="image"
      title={__("Image Style", "divi-optimaizer-modules")}
    >
      <FieldContainer
        attrName="image.decoration.img_width"
        label={__("Image Width", "divi-optimaizer-modules")}
        description={__(
          "Input your value to action here.",
          "divi-optimaizer-modules"
        )}
        features={{
          sticky: false,
        }}
        defaultAttr={defaultSettingsAttrs?.image?.decoration?.img_width}
      >
        <RangeContainer max={1000} />
      </FieldContainer>
      <FieldContainer
        attrName="image.decoration.img_height"
        label={__("Image Height", "divi-optimaizer-modules")}
        description={__(
          "Input your value to action here.",
          "divi-optimaizer-modules"
        )}
        features={{
          sticky: false,
        }}
        defaultAttr={defaultSettingsAttrs?.image?.decoration?.img_height}
      >
        <RangeContainer max={1000} />
      </FieldContainer>
      <SpacingGroup
        attrName="image.decoration.spacing"
        grouped={false}
        fieldLabel="Image"
        defaultGroupAttr={defaultSettingsAttrs?.image?.decoration?.spacing}
      />
      <BorderGroup
        attrName="image.decoration.border"
        grouped={false}
        fieldLabel="Image"
        defaultGroupAttr={defaultSettingsAttrs?.image?.decoration?.border}
      />
    </GroupContainer>

    <GroupContainer
      id="heading"
      title={__("Heading Style", "divi-optimaizer-modules")}
    >
      <BackgroundGroup
        attrName="headingWrapper.decoration.background"
        groupLabel="Heading Background"
        fieldLabel="Heading"
        defaultGroupAttr={
          defaultSettingsAttrs?.headingWrapper?.decoration?.background?.asMutable(
            { deep: true }
          ) ?? {}
        }
      />
      <SpacingGroup
        attrName="headingWrapper.decoration.spacing"
        groupLabel="Heading Spacing"
        fieldLabel="Heading"
        defaultGroupAttr={
          defaultSettingsAttrs?.headingWrapper?.decoration?.spacing?.asMutable({
            deep: true,
          }) ?? {}
        }
      />
      <BorderGroup
        attrName="headingWrapper.decoration.border"
        groupLabel="Heading Border"
        fieldLabel="Heading"
        defaultGroupAttr={
          defaultSettingsAttrs?.headingWrapper?.decoration?.border?.asMutable({
            deep: true,
          }) ?? {}
        }
      />
      <TextGroup
        attrName="headingWrapper.advanced.text"
        groupLabel="Heading Alignment"
        defaultGroupAttr={
          defaultSettingsAttrs?.headingWrapper?.advanced?.text?.asMutable({
            deep: true,
          }) ?? {}
        }
        fields={{
          color: {
            render: false,
          },
          textShadowGroup: {
            render: false,
          },
        }}
      />
    </GroupContainer>

    {/* <TextGroup
      defaultGroupAttr={defaultSettingsAttrs?.module?.advanced?.text}
      fields={{
        color: {
          render: false,
        },
      }}
    /> */}
    <FontGroup
      groupLabel={__("Title Typography", "divi-optimaizer-modules")}
      attrName="title.decoration.font"
      fieldLabel={__("Title", "divi-optimaizer-modules")}
      defaultGroupAttr={
        defaultSettingsAttrs?.title?.decoration?.font?.asMutable({
          deep: true,
        }) ?? {}
      }
      fields={{
        headingLevel: {
          render: true,
        },
      }}
    />
    <FontGroup
      groupLabel={__("Subtitle Typography", "divi-optimaizer-modules")}
      attrName="subTitle.decoration.font"
      fieldLabel={__("Subtitle", "divi-optimaizer-modules")}
      defaultGroupAttr={
        defaultSettingsAttrs?.subTitle?.decoration?.font?.asMutable({
          deep: true,
        }) ?? {}
      }
      fields={{
        headingLevel: {
          render: true,
        },
      }}
    />
    <FontGroup
      groupLabel={__("Currency Typography", "divi-optimaizer-modules")}
      attrName="currency.decoration.font"
      fieldLabel={__("Currency", "divi-optimaizer-modules")}
      defaultGroupAttr={
        defaultSettingsAttrs?.currency?.decoration?.font?.asMutable({
          deep: true,
        }) ?? {}
      }
    />
    <FontGroup
      groupLabel={__("Price Typography", "divi-optimaizer-modules")}
      attrName="priceWrapper.decoration.font"
      fieldLabel={__("Price", "divi-optimaizer-modules")}
      defaultGroupAttr={
        defaultSettingsAttrs?.priceWrapper?.decoration?.font?.asMutable({
          deep: true,
        }) ?? {}
      }
    />
    <FontGroup
      groupLabel={__("Frequency Typography", "divi-optimaizer-modules")}
      attrName="frequency.decoration.font"
      fieldLabel={__("frequency", "divi-optimaizer-modules")}
      defaultGroupAttr={
        defaultSettingsAttrs?.frequency?.decoration?.font?.asMutable({
          deep: true,
        }) ?? {}
      }
    />
    <FontBodyGroup
      attrName="content.decoration.bodyFont"
      defaultGroupAttr={
        defaultSettingsAttrs?.content?.decoration?.bodyFont?.asMutable({
          deep: true,
        }) ?? {}
      }
    />
    <SizingGroup />
    <SpacingGroup />
    <BorderGroup />
    <BoxShadowGroup />
    <FiltersGroup />
    <TransformGroup />
    <AnimationGroup />
  </React.Fragment>
);
