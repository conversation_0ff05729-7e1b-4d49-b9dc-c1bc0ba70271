<?php

/**
 * ImageLogoCarousel::render_callback()
 *
 * @package MEE\Modules\ImageLogoCarousel
 * @since ??
 */

namespace MEE\Modules\ImageLogoCarousel\ImageLogoCarouselTrait;

if (!defined('ABSPATH')) {
    die('Direct access forbidden.');
}

// phpcs:disable ET.Sniffs.ValidVariableName.UsedPropertyNotSnakeCase -- WP use snakeCase in \WP_Block_Parser_Block

use ET\Builder\FrontEnd\BlockParser\BlockParserStore;
use ET\Builder\Packages\Module\Module;
use ET\Builder\Packages\Module\Options\Element\ElementComponents;
use MEE\Modules\ImageLogoCarousel\ImageLogoCarousel;
use ET\Builder\Framework\Utility\HTMLUtility;

trait RenderCallbackTrait
{
    use ModuleClassnamesTrait;
    use ModuleStylesTrait;
    use ModuleScriptDataTrait;

    /**
     * Fetch image data from WordPress by image IDs.
     *
     * @param string $carousel_image_ids Comma-separated image IDs.
     * @return array List of images with their URLs and details.
     */
    public static function fetch_images($carousel_image_ids)
    {
        $image_data = [];

        if (!empty($carousel_image_ids)) {
            $image_ids = explode(',', $carousel_image_ids);

            foreach ($image_ids as $id) {
                $id = intval(trim($id)); // Ensure ID is an integer

                if ($id > 0) {
                    $image = get_post($id);
                    if ($image) {
                        $image_url = wp_get_attachment_url($id);
                        if ($image_url) {
                            $image_data[] = [
                                'id' => $id,
                                'source_url' => esc_url($image_url),
                                'title' => esc_html(get_the_title($id)),
                                'alt' => esc_attr(get_post_meta($id, '_wp_attachment_image_alt', true)),
                            ];
                        }
                    }
                }
            }
        }

        return $image_data;
    }

    /**
     * ImageLogoCarousel render callback which outputs server-side rendered HTML on the Front-End.
     *
     * @since ??
     *
     * @param array          $attrs Block attributes that were saved by VB.
     * @param string         $content Block content.
     * @param \WP_Block      $block Parsed block object that is being rendered.
     * @param ModuleElements $elements ModuleElements instance.
     *
     * @return string HTML rendered of ImageLogoCarousel.
     */
    public static function render_callback($attrs, $content, $block, $elements)
    {
        $children_ids = $block->parsed_block['innerBlocks'] ? array_map(
            function ($inner_block) {
                return $inner_block['id'];
            },
            $block->parsed_block['innerBlocks']
        ) : [];

        $parent       = BlockParserStore::get_parent($block->parsed_block['id'], $block->parsed_block['storeInstance']);
        $parent_attrs = $parent->attrs ?? [];

        $use_autoPlay = $attrs['carousel']['advanced']['use_autoPlay']['desktop']['value'] ?? '';
        $autoPlay = $use_autoPlay === 'on';
        $show_arrows = $attrs['arrows']['advanced']['show_arrows']['desktop']['value'] ?? '';
        $isArrows = $show_arrows === 'on';
        $show_dots = $attrs['dots']['advanced']['show_dots']['desktop']['value'] ?? '';
        $isDots = $show_dots === 'on';
        $autoPlay_speed = $attrs['carousel']['advanced']['autoPlay_speed']['desktop']['value'] ?? '';

        $slide_to_show = $attrs['carousel']['advanced']['slide_to_show']['desktop']['value'] ?? '';
        $slide_to_scroll = $attrs['carousel']['advanced']['slide_to_scroll']['desktop']['value'] ?? '';

        $pauseOnHover = $attrs['carousel']['advanced']['pauseOnHover']['desktop']['value'] ?? '';
        $isPause = $pauseOnHover === 'on';

        $use_fade = $attrs['carousel']['advanced']['use_fade']['desktop']['value'] ?? '';
        $isFade = $use_fade === 'on';

        $slide_speed = $use_autoPlay === 'on' ? $attrs['carousel']['advanced']['slide_speed']['desktop']['value'] ?? '' : 500;

        $carousel_image_ids = $attrs['carousel_images']['innerContent']['desktop']['value'] ?? '';
        $use_title = $attrs['carousel']['advanced']['use_title']['desktop']['value'] ?? '';
        $use_caption = $attrs['carousel']['advanced']['use_caption']['desktop']['value'] ?? '';

        // Fetch images using the newly created function
        $images = self::fetch_images($carousel_image_ids);





        // Generate image slider content
        $image_html = '';
        foreach ($images as $image) {
            $title = HTMLUtility::render(
                [
                    'tag'               => 'h4',
                    'attributes'        => [
                        'class' => 'dotm_image_logo_image_title',
                    ],
                    'childrenSanitizer' => 'et_core_esc_previously',
                    'children'          => $image['title'], // Direct access to title from fetch_images
                ]
            );

            $caption = HTMLUtility::render(
                [
                    'tag'               => 'h5',
                    'attributes'        => [
                        'class' => 'dotm_image_logo_image_caption',
                    ],
                    'childrenSanitizer' => 'et_core_esc_previously',
                    'children'          => get_post($image['id'])->post_excerpt, // Get caption from post
                ]
            );

            $contents = HTMLUtility::render(
                [
                    'tag'               => 'div',
                    'attributes'        => [
                        'class' => 'dotm_image_logo_contents',
                    ],
                    'childrenSanitizer' => 'et_core_esc_previously',
                    'children'          => ($use_title === 'on' ? $title : '') . ($use_caption === 'on' ? $caption : ''),
                ]
            );
            $image_html .= '<div class="dotm_image_logo_image_container">';
            $image_html .= '<img src="' . $image['source_url'] . '" alt="' . $image['alt'] . '">';
            $image_html .= $use_title === 'on' || $use_caption === 'on' ? $contents : '';
            $image_html .= '</div>';
        }

        // Slick slider options
        $options = [
            'dots' => $isDots,
            'infinite' => true,
            'arrows' => $isArrows,
            'autoplay' => $autoPlay,
            'autoplaySpeed' => $autoPlay_speed,
            'centerMode' => false,
            'centerPadding' => "0px",
            'slidesToShow' => $slide_to_show,
            'slidesToScroll' => (int)$slide_to_scroll,
            'pauseOnHover' => $isPause,
            'fade' => $isFade,
            'speed' => $slide_speed,
            'pauseOnFocus' => false,
        ];




        // Content container with dynamically loaded images
        $slider_container = HTMLUtility::render(
            [
                'tag'               => 'div',
                'attributes'        => [
                    'class' => 'center slider',
                ],
                'childrenSanitizer' => 'et_core_esc_previously',
                'children'          => $image_html, // Injecting fetched images
            ]
        );

        return Module::render(
            [
                'orderIndex'          => $block->parsed_block['orderIndex'],
                'storeInstance'       => $block->parsed_block['storeInstance'],

                'id'                  => $block->parsed_block['id'],
                'name'                => $block->block_type->name,
                'moduleCategory'      => $block->block_type->category,
                'attrs'               => $attrs,
                'elements'            => $elements,
                'classnamesFunction'  => [ImageLogoCarousel::class, 'module_classnames'],
                'scriptDataComponent' => [ImageLogoCarousel::class, 'module_script_data'],
                'stylesComponent'     => [ImageLogoCarousel::class, 'module_styles'],
                'parentAttrs'         => $parent_attrs,
                'parentId'            => $parent->id ?? '',
                'parentName'          => $parent->blockName ?? '',
                'children'            => [
                    ElementComponents::component(
                        [
                            'attrs'         => $attrs['module']['decoration'] ?? [],
                            'id'            => $block->parsed_block['id'],
                            'orderIndex'    => $block->parsed_block['orderIndex'],
                            'storeInstance' => $block->parsed_block['storeInstance'],
                        ]
                    ) . $slider_container,
                    'childrenIds' => $children_ids,

                    HTMLUtility::render(
                        [
                            'tag' => 'script',
                            'attributes' => [],
                            'childrenSanitizer' => 'et_core_esc_previously',
                            'children' => "document.addEventListener('DOMContentLoaded', function() {
                                const option = " . json_encode($options) . ";
                                $('.center').slick(option);
                            });",
                        ]
                    ),
                ]
            ]
        );
    }
}
