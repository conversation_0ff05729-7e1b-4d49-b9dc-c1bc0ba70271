jQuery(function (t) { var s = t(".dsm-typing-effect .dsm-typing"); t(".dsm-typing-effect").length && t(s).each(function (s, a) { var e = t(this).data("dsm-typing-strings").split("|"), d = t(this).data("dsm-typing-loop"), i = parseFloat(t(this).data("dsm-typing-speed"), 10), n = parseFloat(t(this).data("dsm-typing-backdelay"), 10), p = parseFloat(t(this).data("dsm-typing-backspeed"), 10), o = t(this).data("dsm-typing-cursor"), y = t(this).data("dsm-typing-fadeout"), r = t(this).data("dsm-typing-shuffle"), h = parseFloat(t(this).data("dsm-typing-delay"), 10) + 500, g = t(this).data("dsm-typing-viewport"), l = t(this).data("dsm-typing-repeat"), m = this, c = { strings: e, loop: d, startDelay: h, typeSpeed: i, backSpeed: p, backDelay: n, cursorChar: o, fadeOut: y, shuffle: r, contentType: "null", onComplete: s => { "on" == t(this).data("dsm-typing-remove-cursor") && t(this).next(".typed-cursor").hide() } }, f = ""; t(this).waypoint({ handler: function (t) { "on" === l ? "down" === t ? f = new Typed(m, c) : f.destroy() : (this.destroy(), f = new Typed(m, c)) }, offset: g }) }) });