<?php
/**
 * OptimizeImageTilt::render_callback()
 *
 * @package MEE\Modules\OptimizeImageTilt
 * @since ??
 */

namespace MEE\Modules\OptimizeImageTilt\OptimizeImageTiltTrait;

if ( ! defined( 'ABSPATH' ) ) {
	die( 'Direct access forbidden.' );
}

// phpcs:disable ET.Sniffs.ValidVariableName.UsedPropertyNotSnakeCase -- WP use snakeCase in \WP_Block_Parser_Block

use ET\Builder\Packages\Module\Module;
use ET\Builder\Framework\Utility\HTMLUtility;
use ET\Builder\FrontEnd\BlockParser\BlockParserStore;
use ET\Builder\Packages\Module\Options\Element\ElementComponents;
use MEE\Modules\OptimizeImageTilt\OptimizeImageTilt;
use ET\Builder\Packages\IconLibrary\IconFont\Utils;

trait RenderCallbackTrait {

	/**
	 * OptimizeImageTilt render callback which outputs server side rendered HTML on the Front-End.
	 *
	 * @since ??
	 * @param array          $attrs    Block attributes that were saved by VB.
	 * @param string         $content  Block content.
	 * @param WP_Block       $block    Parsed block object that being rendered.
	 * @param ModuleElements $elements ModuleElements instance.
	 *
	 * @return string HTML rendered of Static module.
	 */
	public static function render_callback( $attrs, $content, $block, $elements ) {

		$scale_value = $attrs['tilt']['decoration']['scale']['desktop']['value'] ?? '';
		$use_reverse = $attrs['tilt']['decoration']['use_reverse']['desktop']['value'] ?? '';
		$use_glare = $attrs['tilt']['decoration']['use_glare']['desktop']['value'] ?? '';
		$use_icon = $attrs['icon']['advanced']['use']['desktop']['value'] ?? '';
		// $icon = getAttrByMode( $attrs['icon']['innerContent'] );
		$icon = $attrs['icon']['innerContent']['desktop']['value'] ?? [];

		$use_content = $attrs['tilt']['decoration']['use_content']['desktop']['value'] ?? '';
		$content_effect = $use_content === 'on' ? ($attrs['content_container']['decoration']['content_effect']['desktop']['value'] ?? '') : '';

		$option = [
			'scale_value' => $scale_value,
			'use_reverse' => $use_reverse,
			'use_glare' => $use_glare,
		];

		// overlay.
		$overlay = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'dotm_image_tilt_overlay',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => "",
			]
		);

		// Image.
		$image = $elements->render(
			[
				'attrName' => 'image',
			]
		);

		// glare
		$glare = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'dotm_image_tilt_glare',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => "",
			]
		);

		// icon
		$icon = HTMLUtility::render(
			[
				'tag'               => 'span',
				'attributes'        => [
					'class' => 'dotm_image_tilt_icon',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => Utils::process_font_icon($icon),
			]
		);

		// icon container
		$icon_container = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'dotm_image_tilt_icon_container',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $icon,
			]
		);

		// Title.
		$title = $elements->render(
			[
				'attrName' => 'title',
			]
		);

		// Content.
		$content = $elements->render(
			[
				'attrName' => 'content',
			]
		);

		// content container
		$content_container = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => "dotm_image_tilt_content_container $content_effect",
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => ($use_icon === 'on' ? $icon_container : '') . $title . $content,
			]
		);

		

		

		$parent       = BlockParserStore::get_parent( $block->parsed_block['id'], $block->parsed_block['storeInstance'] );
		$parent_attrs = $parent->attrs ?? [];

		return Module::render(
			[
				// FE only.
				'orderIndex'          => $block->parsed_block['orderIndex'],
				'storeInstance'       => $block->parsed_block['storeInstance'],

				// VB equivalent.
				'attrs'               => $attrs,
				'elements'            => $elements,
				'id'                  => $block->parsed_block['id'],
				'name'                => $block->block_type->name,
				'moduleCategory'      => $block->block_type->category,
				'classnamesFunction'  => [ OptimizeImageTilt::class, 'module_classnames' ],
				'stylesComponent'     => [ OptimizeImageTilt::class, 'module_styles' ],
				'scriptDataComponent' => [ OptimizeImageTilt::class, 'module_script_data' ],
				'parentAttrs'         => $parent_attrs,
				'parentId'            => $parent->id ?? '',
				'parentName'          => $parent->blockName ?? '',
				'children'            => [
					ElementComponents::component(
						[
							'attrs'         => $attrs['module']['decoration'] ?? [],
							'id'            => $block->parsed_block['id'],

							// FE only.
							'orderIndex'    => $block->parsed_block['orderIndex'],
							'storeInstance' => $block->parsed_block['storeInstance'],
						]
					),
					HTMLUtility::render(
						[
							'tag'               => 'div',
							'attributes'        => [
								'class' => 'dotm_image_tilt_container',
							],
							'childrenSanitizer' => 'et_core_esc_previously',
							'children'          => $overlay . $image . ($use_glare === 'on' ? $glare : '') . $content_container,
						]
					),
					HTMLUtility::render(
						[
							'tag' => 'script',
							'attributes' => [],
							'childrenSanitizer' => 'et_core_esc_previously',
							'children' => "document.addEventListener('DOMContentLoaded', function() {
                                const options = " . json_encode($option) . ";
                                tilt(options);
                            });",
						]
					),
				],
			]
		);
	}
}
