<?php

/**
 * Module: OptimizeCTA class.
 *
 * @package MEE\Modules\OptimizeCTA
 * @since ??
 */

namespace MEE\Modules\OptimizeCTA;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

use ET\Builder\Framework\DependencyManagement\Interfaces\DependencyInterface;
use ET\Builder\Packages\ModuleLibrary\ModuleRegistration;


/**
 * `OptimizeCTA` is consisted of functions used for OptimizeCTA such as Front-End rendering, REST API Endpoints etc.
 *
 * This is a dependency class and can be used as a dependency for `DependencyTree`.
 *
 * @since ??
 */
class OptimizeCTA implements DependencyInterface
{
	use OptimizeCTATrait\RenderCallbackTrait;
	use OptimizeCTATrait\ModuleClassnamesTrait;
	use OptimizeCTATrait\ModuleStylesTrait;
	use OptimizeCTATrait\ModuleScriptDataTrait;

	/**
	 * Loads `OptimizeCTA` and registers Front-End render callback and REST API Endpoints.
	 *
	 * @since ??
	 *
	 * @return void
	 */
	public function load()
	{
		$module_json_folder_path = DIVIOPTIMIZER_MODULES_JSON_PATH . 'OptimizeCTA/';

		add_action(
			'init',
			function () use ($module_json_folder_path) {
				ModuleRegistration::register_module(
					$module_json_folder_path,
					[
						'render_callback' => [OptimizeCTA::class, 'render_callback'],
					]
				);
			}
		);
	}
}
