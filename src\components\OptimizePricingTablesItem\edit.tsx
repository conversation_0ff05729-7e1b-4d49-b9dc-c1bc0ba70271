// External Dependencies.
import React, { ReactElement } from "react";

// Divi Dependencies.
import { ModuleContainer } from "@divi/module";
import { generateDefaultAttrs } from "@divi/module-library";
import { getAttrByMode } from "@divi/module-utils";
import { processFontIcon } from "@divi/icon-library";
import { ModuleMetadata } from "@divi/types";

// Local Dependencies.
import { OptimizePricingTablesItemEditProps } from "./types";
import { ModuleStyles } from "./styles";
import { moduleClassnames } from "./module-classnames";
import { isEmpty, merge } from "lodash";
import parentMetadata from "../OptimizePricingTables/module.json";

/**
 * OptimizePricingTablesItem edit component of visual builder.
 *
 * @since ??
 *
 * @param {OptimizePricingTablesItemEditProps} props React component props.
 *
 * @returns {ReactElement}
 */
export const OptimizePricingTablesItemEdit = (
  props: OptimizePricingTablesItemEditProps
): ReactElement => {
  const { attrs, elements, id, name, parentAttrs } = props;

  console.log(attrs, "parent-attrs");

  const parentDefaultAttrs = generateDefaultAttrs(
    parentMetadata as ModuleMetadata
  );
  const parentAttrsWithDefault = merge(parentDefaultAttrs, parentAttrs);
  const parentIconContent = getAttrByMode(
    parentAttrsWithDefault?.icon?.innerContent
  );

  const iconContent = getAttrByMode(attrs?.icon?.innerContent);

  const icon = isEmpty(iconContent) ? parentIconContent : iconContent;
  const useImage = attrs?.image?.advanced?.useImage?.desktop?.value ?? "off";
  const useFeature =
    attrs?.featureText?.advanced?.useFeature?.desktop?.value ?? "off";
  const btn_link = attrs?.button?.advanced?.link?.desktop?.value?.url ?? "";
  const btn_target =
    attrs?.button?.advanced?.link?.desktop?.value?.target ?? "";

  return (
    <ModuleContainer
      attrs={attrs}
      parentAttrs={parentAttrs}
      elements={elements}
      id={id}
      name={name}
      stylesComponent={ModuleStyles}
      classnamesFunction={moduleClassnames}
      tag="div"
    >
      {elements.styleComponents({
        attrName: "module",
      })}
      {useFeature === "on" &&
        elements.render({
          attrName: "featureText",
        })}
      <div className="dotm_optimize_pricing_tables_item_card">
        <div className="dotm_optimize_pricing_tables_item_header">
          {useImage === "off" && (
            <div className="dotm_optimize_pricing_tables_item_icon_container">
              <span className="dotm_optimize_pricing_tables_item_icon">
                {processFontIcon(icon)}
              </span>
            </div>
          )}
          {useImage === "on" &&
            elements.render({
              attrName: "image",
            })}
          {elements.render({
            attrName: "title",
          })}
          {elements.render({
            attrName: "subTitle",
          })}
        </div>

        <div className="dotm_optimize_pricing_tables_item_price_section">
          <div className="dotm_optimize_pricing_tables_item_price_wrapper">
            {elements.render({
              attrName: "currency",
            })}
            {elements.render({
              attrName: "price",
            })}
          </div>
          {elements.render({
            attrName: "frequency",
          })}
        </div>

        <div className="dotm_optimize_pricing_tables_item_features">
          {elements.render({
            attrName: "content",
          })}
        </div>
        <div className="dotm_optimize_pricing_tables_item_button_container">
          {elements.render({
            attrName: "button",
            htmlAttributes: {
              href: btn_link,
              target: btn_target ? "_blank" : "_self",
            },
          })}
        </div>
      </div>
    </ModuleContainer>
  );
};
