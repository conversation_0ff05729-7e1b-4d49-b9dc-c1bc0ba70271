<?php
/**
 * TwitterFollowButton::render_callback()
 *
 * @package MEE\Modules\TwitterFollowButton
 * @since ??
 */

namespace MEE\Modules\TwitterFollowButton\TwitterFollowButtonTrait;

if ( ! defined( 'ABSPATH' ) ) {
	die( 'Direct access forbidden.' );
}

// phpcs:disable ET.Sniffs.ValidVariableName.UsedPropertyNotSnakeCase -- WP use snakeCase in \WP_Block_Parser_Block

use ET\Builder\Packages\Module\Module;
use ET\Builder\Framework\Utility\HTMLUtility;
use ET\Builder\FrontEnd\BlockParser\BlockParserStore;
use ET\Builder\Packages\Module\Options\Element\ElementComponents;
use MEE\Modules\TwitterFollowButton\TwitterFollowButton;

trait RenderCallbackTrait {

	/**
	 * Static module render callback which outputs server side rendered HTML on the Front-End.
	 *
	 * @since ??
	 * @param array          $attrs    Block attributes that were saved by VB.
	 * @param string         $content  Block content.
	 * @param WP_Block       $block    Parsed block object that being rendered.
	 * @param ModuleElements $elements ModuleElements instance.
	 *
	 * @return string HTML rendered of Static module.
	 */
	public static function render_callback( $attrs, $content, $block, $elements ) {
	
		$userName = $attrs['twitter']['advanced']['userName']['desktop']['value'] ?? 'optimizly';
		$size     = $attrs['twitter']['advanced']['size']['desktop']['value'] ?? 's';
		// Content container.
		$content_container = HTMLUtility::render(
			[
				'tag'               => 'iframe',
				'attributes'        => [
					'class' => '',
					'src'   =>"https://platform.twitter.com/widgets/follow_button.html?screen_name=$userName&show_count=true&size=$size&lang=en&label=Follow&align=left&dnt=true",
					'style' => 'border: none; overflow: hidden;',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => '',
			]
		);

		$parent       = BlockParserStore::get_parent( $block->parsed_block['id'], $block->parsed_block['storeInstance'] );
		$parent_attrs = $parent->attrs ?? [];

		return Module::render(
			[
				// FE only.
				'orderIndex'          => $block->parsed_block['orderIndex'],
				'storeInstance'       => $block->parsed_block['storeInstance'],

				// VB equivalent.
				'attrs'               => $attrs,
				'elements'            => $elements,
				'id'                  => $block->parsed_block['id'],
				'name'                => $block->block_type->name,
				'moduleCategory'      => $block->block_type->category,
				'classnamesFunction'  => [ TwitterFollowButton::class, 'module_classnames' ],
				'stylesComponent'     => [ TwitterFollowButton::class, 'module_styles' ],
				'scriptDataComponent' => [ TwitterFollowButton::class, 'module_script_data' ],
				'parentAttrs'         => $parent_attrs,
				'parentId'            => $parent->id ?? '',
				'parentName'          => $parent->blockName ?? '',
				'children'            => [
					ElementComponents::component(
						[
							'attrs'         => $attrs['module']['decoration'] ?? [],
							'id'            => $block->parsed_block['id'],

							// FE only.
							'orderIndex'    => $block->parsed_block['orderIndex'],
							'storeInstance' => $block->parsed_block['storeInstance'],
						]
					),
					HTMLUtility::render(
						[
							'tag'               => 'div',
							'attributes'        => [
								'class' => 'dotm_twitter_follow_button_module',
							],
							'childrenSanitizer' => 'et_core_esc_previously',
							'children'          => $content_container,
						]
					),
				],
			]
		);
	}
}
