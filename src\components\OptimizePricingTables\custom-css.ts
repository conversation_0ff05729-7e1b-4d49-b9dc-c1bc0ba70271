// WordPress dependencies.
import { __ } from '@wordpress/i18n';

import metadata from './module.json';


const customCssFields = metadata.customCssFields as Record<'contentContainer' | 'title' | 'content' | 'icon', { subName: string, selectorSuffix: string, label: string }>;

customCssFields.contentContainer.label = __('Content Container', 'divi-optimaizer-modules');
customCssFields.title.label            = __('Title', 'divi-optimaizer-modules');
customCssFields.content.label          = __('Content', 'divi-optimaizer-modules');
customCssFields.icon.label             = __('Icon', 'divi-optimaizer-modules');

export const cssFields = { ...customCssFields };
