<?php

/**
 * Module: TextGradient class.
 *
 * @package MEE\Modules\TextGradient
 * @since ??
 */

namespace MEE\Modules\TextGradient;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

use ET\Builder\Framework\DependencyManagement\Interfaces\DependencyInterface;
use ET\Builder\Packages\ModuleLibrary\ModuleRegistration;


/**
 * `TextGradient` is consisted of functions used for Static Module such as Front-End rendering, REST API Endpoints etc.
 *
 * This is a dependency class and can be used as a dependency for `DependencyTree`.
 *
 * @since ??
 */
class TextGradient implements DependencyInterface
{
	use TextGradientTrait\RenderCallbackTrait;
	use TextGradientTrait\ModuleClassnamesTrait;
	use TextGradientTrait\ModuleStylesTrait;
	use TextGradientTrait\ModuleScriptDataTrait;

	/**
	 * Loads `TextGradient` and registers Front-End render callback and REST API Endpoints.
	 *
	 * @since ??
	 *
	 * @return void
	 */
	public function load()
	{
		$module_json_folder_path = DIVIOPTIMIZER_MODULES_JSON_PATH . 'TextGradient/';

		add_action(
			'init',
			function () use ($module_json_folder_path) {
				ModuleRegistration::register_module(
					$module_json_folder_path,
					[
						'render_callback' => [TextGradient::class, 'render_callback'],
					]
				);
			}
		);
	}
}
