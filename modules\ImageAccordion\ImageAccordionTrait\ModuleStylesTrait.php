<?php

/**
 * ImageAccordion::module_styles().
 *
 * @package MEE\Modules\ImageAccordion
 * @since ??
 */

namespace MEE\Modules\ImageAccordion\ImageAccordionTrait;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

use ET\Builder\FrontEnd\Module\Style;
use ET\Builder\Packages\Module\Layout\Components\StyleCommon\CommonStyle;
use ET\Builder\Packages\Module\Options\Css\CssStyle;
use MEE\Modules\ImageAccordionItem\ImageAccordionItem;

trait ModuleStylesTrait
{

	use CustomCssTrait;

	/**
	 * ImageAccordionItem's style components.
	 *
	 * This function is equivalent of JS function ModuleStyles located in
	 * src/components/ImageAccordion/styles.tsx.
	 *
	 * @param array $args {
	 *     An array of arguments.
	 *
	 * @type string $id Module ID. In VB, the ID of module is UUIDV4. In FE, the ID is order index.
	 * @type string $name Module name.
	 * @type string $attrs Module attributes.
	 * @type string $parentAttrs Parent attrs.
	 * @type string $orderClass Selector class name.
	 * @type string $parentOrderClass Parent selector class name.
	 * @type string $wrapperOrderClass Wrapper selector class name.
	 * @type string $settings Custom settings.
	 * @type string $state Attributes state.
	 * @type string $mode Style mode.
	 * @type ModuleElements $elements ModuleElements instance.
	 * }
	 * @since ??
	 */
	public static function module_styles($args)
	{
		$attrs        = $args['attrs'] ?? [];
		$parent_attrs = $args['parentAttrs'] ?? [];
		$order_class  = $args['orderClass'];
		$elements     = $args['elements'];
		$settings     = $args['settings'] ?? [];


		$button_selector = "{$order_class} .dotm_image_accordion_item_button_container";
		$image_as_icon_selector_container = "{$order_class} dotm_image_accordion_item_image_as_icon_container";
		$image_as_icon_selector = "{$order_class} .dotm_image_accordion_item_image_as_icon";


		Style::add(
			[
				'id'            => $args['id'],
				'name'          => $args['name'],
				'orderIndex'    => $args['orderIndex'],
				'storeInstance' => $args['storeInstance'],
				'styles'        => [
					// Module.
					$elements->style(
						[
							'attrName'   => 'module',
							'styleProps' => [
								'disabledOn' => [
									'disabledModuleVisibility' => $settings['disabledModuleVisibility'] ?? null,
								],
								'advancedStyles' => [
									[
										'componentName' => 'divi/text',
										'props'         => [
											'selector' => $button_selector,
											'attr'     => $attrs['button_container']['advanced']['text'] ?? [],
										]
									],
									[
										'componentName' => 'divi/text',
										'props'         => [
											'selector' => $image_as_icon_selector_container,
											'attr'     => $attrs['image_as_icon']['advanced']['text'] ?? [],
										]
									]
								]
							],
						]
					),

					CommonStyle::style(
						[
							'selector' => $image_as_icon_selector,
							'attr'     => $attrs['image_as_icon']['advanced']['size'] ?? [],
							'property' => 'width',
						]
					),

					// accordion_container.
					$elements->style(
						[
							'attrName' => 'accordion_container',
						]
					),

					// overlay.
					$elements->style(
						[
							'attrName' => 'overlay',
						]
					),

					// wrapper_container.
					$elements->style(
						[
							'attrName' => 'wrapper_container',
						]
					),

					// image_as_icon.
					$elements->style(
						[
							'attrName' => 'image_as_icon',
						]
					),

					// title.
					$elements->style(
						[
							'attrName' => 'title',
						]
					),

					// sub_title.
					$elements->style(
						[
							'attrName' => 'sub_title',
						]
					),

					// button_container.
					$elements->style(
						[
							'attrName' => 'button_container',
						]
					),

					// button.
					$elements->style(
						[
							'attrName' => 'button',
						]
					),
					
					// content.
					$elements->style(
						[
							'attrName' => 'content',
						]
					),



					// Icon.
					$elements->style(
						[
							'attrName'   => 'icon',
							'styleProps' => [
								'advancedStyles' => [
									[
										'componentName' => 'divi/common',
										'props'         => [
											'attr'                => $attrs['icon']['innerContent'] ?? [],
											'declarationFunction' => [ImageAccordionItem::class, 'icon_font_declaration'],
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'attr'     => $attrs['icon']['advanced']['color'] ?? [],
											'property' => 'color',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'attr'     => $attrs['icon']['advanced']['size'] ?? [],
											'property' => 'font-size',
										]
									],
								]
							]
						]
					),

					/*
					 * We need to add CssStyle at the very bottom of other
					 * components so that custom css can override module styles
					 * till we find a more elegant solution.
					 */
					CssStyle::style(
						[
							'selector'  => $args['orderClass'],
							'attr'      => $attrs['css'] ?? [],
							'cssFields' => self::custom_css(),
						]
					),

					// ATTENTION: The code is intentionally added and commented in FE only as an example of expected value format.
					// If you have custom style processing, the style output should be passed as an `array` of style declarations
					// to the `styles` property of the `Style::add` method. For example:
					// [
					// 	[
					// 		'atRules'     => false,
					// 		'selector'    => $icon_selector,
					// 		'declaration' => 'color: red;'
					// 	],
					// 	[
					// 		'atRules'     => '@media only screen and (max-width: 767px)',
					// 		'selector'    => $icon_selector,
					// 		'declaration' => 'color: green;'
					// 	],
					// ],

					// The code below is an example of how to use the `CssStyle::style` method to generate CSS style.
					CssStyle::style(
						[
							'selector'  => $args['orderClass'],
							'attr'      => $attrs['css'] ?? [],
							'cssFields' => self::custom_css(),
						]
					),
				],
			]
		);
	}
}
