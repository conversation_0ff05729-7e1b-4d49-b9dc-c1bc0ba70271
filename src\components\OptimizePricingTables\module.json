{"name": "dotm/optimize-pricing-tables", "d4Shortcode": "", "title": "Optimize Pricing Tables", "titles": "Optimize Pricing Tabless", "moduleIcon": "dotm/optimize-pricing-tables", "category": "module", "attributes": {"module": {"type": "object", "selector": "{{selector}}", "default": {"meta": {"adminLabel": {"desktop": {"value": "Optimize Pricing Tables"}}}}, "styleProps": {"border": {"important": true}}}, "headingWrapper": {"type": "object", "selector": "{{selector}} .dotm_optimize_pricing_tables_item_header", "default": {"decoration": {"spacing": {"desktop": {"value": {"padding": {"top": "30px", "bottom": "30px", "left": "20px", "right": "20px"}}}}, "background": {"desktop": {"value": {"color": "#d1d5db"}}}}, "advanced": {"text": {"text": {"desktop": {"value": {"orientation": "center"}}}}}}}, "title": {"type": "object", "selector": "{{selector}} .dotm_optimize_pricing_tables_item_title", "default": {"decoration": {"font": {"font": {"desktop": {"value": {"headingLevel": "h2", "weight": "600"}}}}}}, "styleProps": {"font": {"important": {"font": {"desktop": {"value": {"color": true}}}}}}}, "subTitle": {"type": "object", "selector": "{{selector}} .dotm_optimize_pricing_tables_item_subTitle", "default": {"decoration": {"font": {"font": {"desktop": {"value": {"headingLevel": "h4"}}}}}}, "styleProps": {"font": {"important": {"font": {"desktop": {"value": {"color": true}}}}}}}, "currency": {"type": "object", "selector": "{{selector}} .dotm_optimize_pricing_tables_item_currency", "default": {"decoration": {"font": {"font": {"desktop": {"value": {"weight": "400"}}}}}}}, "priceWrapper": {"type": "object", "selector": "{{selector}} .dotm_optimize_pricing_tables_item_price_wrapper", "default": {"decoration": {"font": {"font": {"desktop": {"value": {"weight": "700", "size": "72px", "lineHeight": "1em"}}}}}}}, "frequency": {"type": "object", "selector": "{{selector}} .dotm_optimize_pricing_tables_item_frequency", "default": {"decoration": {"font": {"font": {"desktop": {"value": {"color": "#6b7280", "size": "16px"}}}}}}}, "content": {"type": "object", "selector": "{{selector}} .dotm_optimize_pricing_tables_item_description", "styleProps": {"bodyFont": {"selectors": {"desktop": {"value": "{{selector}} .dotm_optimize_pricing_tables_item_description"}}}}}, "icon": {"type": "object", "selector": "{{selector}} .dotm_optimize_pricing_tables_item_icon", "default": {"innerContent": {"desktop": {"value": {"unicode": "&#x39;", "type": "divi", "weight": "400"}}}, "advanced": {"color": {"desktop": {"value": "#000000"}}, "size": {"desktop": {"value": "28px"}}}}}, "image": {"type": "object", "selector": "{{selector}} .dotm_optimize_pricing_tables_item_image", "default": {"decoration": {"img_width": {"desktop": {"value": "100px"}}, "img_height": {"desktop": {"value": "auto"}}}}}}, "customCssFields": {"contentContainer": {"subName": "contentContainer", "selectorSuffix": " .example_parent_module__content-container"}, "title": {"subName": "title", "selectorSuffix": " .example_parent_module__title"}, "content": {"subName": "content", "selectorSuffix": " .example_parent_module__content"}, "icon": {"subName": "icon", "selectorSuffix": " .example_parent_module__icon.et-pb-icon"}}}