<?php

/**
 * ImageLogoCarousel::module_styles().
 *
 * @package MEE\Modules\ImageLogoCarousel
 * @since ??
 */

namespace MEE\Modules\ImageLogoCarousel\ImageLogoCarouselTrait;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

use ET\Builder\FrontEnd\Module\Style;
use ET\Builder\Packages\Module\Layout\Components\StyleCommon\CommonStyle;
use ET\Builder\Packages\Module\Options\Css\CssStyle;
use MEE\Modules\ImageLogoCarousel\ImageLogoCarousel;

trait ModuleStylesTrait
{

	use CustomCssTrait;
	use StyleDeclarationTrait;

	/**
	 * ImageLogoCarousel's style components.
	 *
	 * This function is equivalent of JS function ModuleStyles located in
	 * src/components/ImageLogoCarousel/styles.tsx.
	 *
	 * @param array $args {
	 *     An array of arguments.
	 *
	 * @type string $id Module ID. In VB, the ID of module is UUIDV4. In FE, the ID is order index.
	 * @type string $name Module name.
	 * @type string $attrs Module attributes.
	 * @type string $parentAttrs Parent attrs.
	 * @type string $orderClass Selector class name.
	 * @type string $parentOrderClass Parent selector class name.
	 * @type string $wrapperOrderClass Wrapper selector class name.
	 * @type string $settings Custom settings.
	 * @type string $state Attributes state.
	 * @type string $mode Style mode.
	 * @type ModuleElements $elements ModuleElements instance.
	 * }
	 * @since ??
	 */
	public static function module_styles($args)
	{
		$attrs        = $args['attrs'] ?? [];
		$parent_attrs = $args['parentAttrs'] ?? [];
		$orderClass  = $args['orderClass'];
		$elements     = $args['elements'];
		$settings     = $args['settings'] ?? [];

		$dotSelector = "{$orderClass} .slick-dots li button:before";
		$activeDotSelector = "{$orderClass} .slick-dots li.slick-active button:before";
		$dotListSelector = "{$orderClass} .slick-dots";
		$arrwosSelector = "{$orderClass} .slick-arrow";
		$slidersSelector = "{$orderClass} .slick-track";

		$dots_direction = $attrs['dots']['decoration']['dots_direction']['desktop']['value'] ?? 'off';
		$show_contents_on_hover = $attrs['carousel']['advanced']['showContentsOnHover']['desktop']['value'] ?? '';

		Style::add(
			[
				'id'            => $args['id'],
				'name'          => $args['name'],
				'orderIndex'    => $args['orderIndex'],
				'storeInstance' => $args['storeInstance'],
				'styles'        => [
					// Module.
					$elements->style(
						[
							'attrName'   => 'module',
							'styleProps' => [
								'disabledOn' => [
									'disabledModuleVisibility' => $settings['disabledModuleVisibility'] ?? null,
								],
								'advancedStyles' => [
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector'            => $slidersSelector,
											'attr'                => $attrs['carousel']['advanced']['slider_gap'] ?? [],
											'property'            => 'gap',
										]
									]
								]
							],
						]
					),

					// dots.
					$elements->style(
						[
							'attrName' => 'dots',
							'styleProps' => [
								'advancedStyles' => [
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector'            => $dotSelector,
											'attr'                => $attrs['dots']['decoration']['dots_width'] ?? [],
											'property'            => 'width',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector'            => $dotSelector,
											'attr'                => $attrs['dots']['decoration']['dots_height'] ?? [],
											'property'            => 'height',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector'            => $dotSelector,
											'attr'                => $attrs['dots']['decoration']['dots_color'] ?? [],
											'property'            => 'background',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector'            => $activeDotSelector,
											'attr'                => $attrs['dots']['decoration']['active_dot_color'] ?? [],
											'property'            => 'background',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector'            => $dotListSelector,
											'attr'                => $attrs['dots']['decoration']['dots_gap'] ?? [],
											'property'            => 'gap',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector'            => $dotListSelector,
											'attr'                => $attrs['dots']['decoration']['vr_position'] ?? [],
											'property'            => 'bottom',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector'            => $dotListSelector,
											'attr'                => $attrs['dots']['decoration']['hr_position'] ?? [],
											'property'            => 'left',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector'            => $dotListSelector,
											'attr'                => $attrs['dots']['decoration']['dots_direction'] ?? [],
											'property'            => $dots_direction ===  'on' ? "flex-direction: column;" : "flex-direction: row;",
										]
									]
								]
							]
						]
					),

					// marge_arrow.
					$elements->style(
						[
							'attrName' => 'marge_arrow',
						]
					),

					// contents.
					$elements->style(
						[
							'attrName' => 'contents',
							'styleProps' => [
								'advancedStyles' => [
									[
										'componentName' => 'divi/common',
										'props'         => [
											'attr'                => $attrs['carousel']['advanced']['showContentsOnHover'] ?? [],
											'property'            => $show_contents_on_hover === 'on' ? "transform: translateY(100%);" : '',
										]
									],
								]
							]

						]
					),

					// title.
					$elements->style(
						[
							'attrName' => 'title',
						]
					),

					// caption.
					$elements->style(
						[
							'attrName' => 'caption',
						]
					),

					// arrows.
					$elements->style(
						[
							'attrName' => 'arrows',
							'styleProps' => [
								'advancedStyles' => [
									[
										'componentName' => 'divi/common',
										'props'         => [
											'attr'                => $attrs['arrows']['advanced']['color'] ?? [],
											'property'            => 'color',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'attr'                => $attrs['arrows']['advanced']['size'] ?? [],
											'property'            => 'font-size',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector'            => $arrwosSelector,
											'attr'                => $attrs['arrows']['advanced']['vr_possition'] ?? [],
											'property'            => 'top',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector'            => "{$orderClass} .slick-next",
											'attr'                => $attrs['arrows']['advanced']['next_hr_possition'] ?? [],
											'property'            => 'right',
										]
									]
								]
							]
						]
					),

					// prev_arrow.
					$elements->style(
						[
							'attrName'   => 'prev_arrow',
							'styleProps' => [
								'advancedStyles' => [
									[
										'componentName' => 'divi/common',
										'props'         => [
											'attr'                => $attrs['prev_arrow']['innerContent'] ?? [],
											'declarationFunction' => [ImageLogoCarousel::class, 'icon_font_declaration'],
										]
									]
								]
							]
						]
					),

					// nxt_arrow.
					$elements->style(
						[
							'attrName'   => 'nxt_arrow',
							'styleProps' => [
								'advancedStyles' => [
									[
										'componentName' => 'divi/common',
										'props'         => [
											'attr'                => $attrs['nxt_arrow']['innerContent'] ?? [],
											'declarationFunction' => [ImageLogoCarousel::class, 'icon_font_declaration'],
										]
									]
								]
							]
						]
					),

					/*
					 * We need to add CssStyle at the very bottom of other
					 * components so that custom css can override module styles
					 * till we find a more elegant solution.
					 */
					CssStyle::style(
						[
							'selector'  => $args['orderClass'],
							'attr'      => $attrs['css'] ?? [],
							'cssFields' => self::custom_css(),
						]
					),

					// ATTENTION: The code is intentionally added and commented in FE only as an example of expected value format.
					// If you have custom style processing, the style output should be passed as an `array` of style declarations
					// to the `styles` property of the `Style::add` method. For example:
					// [
					// 	[
					// 		'atRules'     => false,
					// 		'selector'    => $icon_selector,
					// 		'declaration' => 'color: red;'
					// 	],
					// 	[
					// 		'atRules'     => '@media only screen and (max-width: 767px)',
					// 		'selector'    => $icon_selector,
					// 		'declaration' => 'color: green;'
					// 	],
					// ],

					// The code below is an example of how to use the `CssStyle::style` method to generate CSS style.
					CssStyle::style(
						[
							'selector'  => $args['orderClass'],
							'attr'      => $attrs['css'] ?? [],
							'cssFields' => self::custom_css(),
						]
					),
				],
			]
		);
	}
}
