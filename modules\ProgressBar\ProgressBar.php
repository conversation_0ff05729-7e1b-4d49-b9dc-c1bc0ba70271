<?php

/**
 * Module: ProgressBar class.
 *
 * @package MEE\Modules\ProgressBar
 * @since ??
 */

namespace MEE\Modules\ProgressBar;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

use ET\Builder\Framework\DependencyManagement\Interfaces\DependencyInterface;
use ET\Builder\Packages\ModuleLibrary\ModuleRegistration;


/**
 * `ProgressBar` is consisted of functions used for Static Module such as Front-End rendering, REST API Endpoints etc.
 *
 * This is a dependency class and can be used as a dependency for `DependencyTree`.
 *
 * @since ??
 */
class ProgressBar implements DependencyInterface
{
	use ProgressBarTrait\RenderCallbackTrait;
	use ProgressBarTrait\ModuleClassnamesTrait;
	use ProgressBarTrait\ModuleStylesTrait;
	use ProgressBarTrait\ModuleScriptDataTrait;

	/**
	 * Loads `ProgressBar` and registers Front-End render callback and REST API Endpoints.
	 *
	 * @since ??
	 *
	 * @return void
	 */
	public function load()
	{
		$module_json_folder_path = DIVIOPTIMIZER_MODULES_JSON_PATH . 'ProgressBar/';

		add_action(
			'init',
			function () use ($module_json_folder_path) {
				ModuleRegistration::register_module(
					$module_json_folder_path,
					[
						'render_callback' => [ProgressBar::class, 'render_callback'],
					]
				);
			}
		);
	}
}
