.dotm_before_after_slider_image_container {
    position: relative;
    height: 100%;
    overflow: hidden;
    cursor: col-resize;
}

.dotm_before_after_slider_before_label {
    position: absolute;
    z-index: 4;   
}

.dotm_before_after_slider_after_label {
    position: absolute;
    z-index: 4;
}

.show_label_on_hover {
    scale: 0;
    transition: scale 0.3s ease-out;
}

.dotm_before_after_slider_image_container:hover .show_label_on_hover {
    scale: 1;
}

.dotm_before_after_slider_image_container img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.dotm_before_after_slider_image_container .dotm_before_after_slider_after_image {
    clip-path: inset(0 0 0 50%);
    transition: clip-path 0.1s ease-out;
}

.dotm_before_after_slider_slide_line {
    position: absolute;
    top: 0;
    width: 4px;
    height: 100%;
    z-index: 2;
    transform: translateX(-50%);
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
}

.dotm_before_after_slider_arrows {
    position: absolute;
    top: 50%;
    left: 50%;
    z-index: 3;
    transform: translate(-50%, -50%);
}

.dotm_before_after_slider_slide_line::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.dotm_before_after_slider_left_arrow {
    font-family: ETmodules !important;
    font-size: 35px;
    margin-top: 7px;
    position: relative;
    left: 8px;
    top: 5px;
    transition: all 0.3s ease-in-out;
}

.dotm_before_after_slider_right_arrow {
    font-family: ETmodules !important;
    font-size: 35px;
    position: relative;
    right: 8px;
    top: 5px;
    transition: all 0.3s ease-in-out;
}

.dotm_before_after_slider_image_container:hover .dotm_before_after_slider_left_arrow {
    left: 3px;
}

.dotm_before_after_slider_image_container:hover .dotm_before_after_slider_right_arrow {
    right: 3px;
}

.vertical .dotm_before_after_slider_slide_line {
    width: 100%;
    height: 4px;
    left: 0;
    transform: translateY(-50%);
}


.vertical .dotm_before_after_slider_arrows {
    transform: rotate(90deg) translate(-24% , 105%) !important;
}

.vertical .dotm_before_after_slider_slide_line::after {
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.vertical .dotm_before_after_slider_after_image {
    clip-path: inset(0 0 50% 0);
}

.control-panel {
    margin-bottom: 10px;
}

.control-panel button {
    margin: 5px;
    padding: 10px;
    font-size: 16px;
    cursor: pointer;
}

.interaction-mode {
    margin-bottom: 20px;
}

.interaction-mode label {
    margin-right: 10px;
}