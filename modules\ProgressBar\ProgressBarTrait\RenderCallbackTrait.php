<?php

/**
 * ProgressBar::render_callback()
 *
 * @package MEE\Modules\ProgressBar
 * @since ??
 */

namespace MEE\Modules\ProgressBar\ProgressBarTrait;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

// phpcs:disable ET.Sniffs.ValidVariableName.UsedPropertyNotSnakeCase -- WP use snakeCase in \WP_Block_Parser_Block

use ET\Builder\Packages\Module\Module;
use ET\Builder\Framework\Utility\HTMLUtility;
use ET\Builder\FrontEnd\BlockParser\BlockParserStore;
use ET\Builder\Packages\Module\Options\Element\ElementComponents;
use MEE\Modules\ProgressBar\ProgressBar;

trait RenderCallbackTrait
{

	/**
	 * ProgressBar render callback which outputs server side rendered HTML on the Front-End.
	 *
	 * @since ??
	 * @param array          $attrs    Block attributes that were saved by VB.
	 * @param string         $content  Block content.
	 * @param WP_Block       $block    Parsed block object that being rendered.
	 * @param ModuleElements $elements ModuleElements instance.
	 *
	 * @return string HTML rendered of Static module.
	 */
	public static function render_callback($attrs, $content, $block, $elements)
	{

		$progress_type = $attrs['progress']['advanced']['progress_type']['desktop']['value'] ?? '';
		$progress_bg_style = $attrs['progress']['advanced']['progress_bg_style']['desktop']['value'] ?? '';
		$progress_bg_color = $attrs['progress']['decoration']['progress_bg_color']['desktop']['value'] ?? '';
		$progress_strip_color1 = $attrs['progress']['decoration']['progress_strip_color1']['desktop']['value'] ?? '';
		$progress_strip_color2 = $attrs['progress']['decoration']['progress_strip_color2']['desktop']['value'] ?? '';
		$use_strip_animation = $attrs['progress']['decoration']['use_strip_animation']['desktop']['value'] ?? '';
		$use_count_for_linear = $attrs['progressBar']['decoration']['use_count']['desktop']['value'] ?? '';

		

		$progressBar = [
			'progress_type' => $progress_type,
			'progress_bg_style' => $progress_bg_style,
			'progress_bg_color' => $progress_bg_color,
			'progress_strip_color1' => $progress_strip_color1,
			'progress_strip_color2' => $progress_strip_color2,
		];


		$progress_bar = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'progress-bar',
					'id'    => 'progress-bar',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => "",
			]
		);

		$progress_count = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'progress-count',
					'id'    => 'progress-count',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => "0%",
			]
		);

		$arrow = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'arrow',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => "",
			]
		);

		$arrow_container = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'arrow-container',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $arrow,
			]
		);

		$progress_linear_percentage = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'progress-linear-percentage',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $progress_count . $arrow_container,
			]
		);


		// progress container.
		$linear_progress_bar = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'progress-container',
					'id'    => 'normal-progress',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $progress_bar . ($use_count_for_linear === "on" ? $progress_linear_percentage : ''),
			]
		);


		$circle_progress = '<svg class="progress-circle" style="transform: rotate(-90deg);" viewBox="0 0 100 100">
            <defs>
              <pattern id="striped-pattern" width="10" height="10" patternUnits="userSpaceOnUse" patternTransform="rotate(0)">
                <rect width="5" height="10" fill="' . esc_attr($progress_strip_color1) . '" />
                <rect x="5" width="5" height="10" fill="' . esc_attr($progress_strip_color2) . '" />
                ' . ($use_strip_animation === "on" ? (
                  '<animateTransform attributeName="patternTransform" type="translate" from="10 0" to="0 0" dur="0.5s" repeatCount="indefinite" />'
                ) : '') . '
              </pattern>
              <linearGradient id="circle-gradient" x1="0%" y1="0%" x2="100%" y2="0%">
                <stop offset="0%" stopColor="' . esc_attr($progress_strip_color1) . '" />
                <stop offset="100%" stopColor="' . esc_attr($progress_strip_color2) . '" />
              </linearGradient>
            </defs>
            <circle class="bg-circle" cx="50" cy="50" r="45" />
            <circle class="progress" cx="50" cy="50" r="45" />
			<div class="progress-percentage" id="progress-percentage">0%</div>
          </svg>';

		// circle container.
		$circle_progress_wrapper = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'progress-wrapper',
					'id'    => 'circle-progress',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $circle_progress,
			]
		);

		$parent       = BlockParserStore::get_parent($block->parsed_block['id'], $block->parsed_block['storeInstance']);
		$parent_attrs = $parent->attrs ?? [];

		return Module::render(
			[
				// FE only.
				'orderIndex'          => $block->parsed_block['orderIndex'],
				'storeInstance'       => $block->parsed_block['storeInstance'],

				// VB equivalent.
				'attrs'               => $attrs,
				'elements'            => $elements,
				'id'                  => $block->parsed_block['id'],
				'name'                => $block->block_type->name,
				'moduleCategory'      => $block->block_type->category,
				'classnamesFunction'  => [ProgressBar::class, 'module_classnames'],
				'stylesComponent'     => [ProgressBar::class, 'module_styles'],
				'scriptDataComponent' => [ProgressBar::class, 'module_script_data'],
				'parentAttrs'         => $parent_attrs,
				'parentId'            => $parent->id ?? '',
				'parentName'          => $parent->blockName ?? '',
				'children'            => [
					ElementComponents::component(
						[
							'attrs'         => $attrs['module']['decoration'] ?? [],
							'id'            => $block->parsed_block['id'],

							// FE only.
							'orderIndex'    => $block->parsed_block['orderIndex'],
							'storeInstance' => $block->parsed_block['storeInstance'],
						]
					),
					HTMLUtility::render(
						[
							'tag'               => 'div',
							'attributes'        => [
								'class' => 'dotm_progress_bar_container',
							],
							'childrenSanitizer' => 'et_core_esc_previously',
							'children'          => $linear_progress_bar . $circle_progress_wrapper,
						]
					),
					HTMLUtility::render(
                        [
                            'tag' => 'script',
                            'attributes' => [],
                            'childrenSanitizer' => 'et_core_esc_previously',
                            'children' => "document.addEventListener('DOMContentLoaded', function() {
                                const progress_props = " . json_encode($progressBar) . ";
                                initProgressBar(progress_props);
                            });",
                        ]
                    ),
				],
			]
		);
	}
}
