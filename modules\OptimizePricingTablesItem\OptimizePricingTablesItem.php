<?php
/**
 * Module: OptimizePricingTablesItem class.
 *
 * @package MEE\Modules\OptimizePricingTablesItem
 * @since ??
 */

namespace MEE\Modules\OptimizePricingTablesItem;

if ( ! defined( 'ABSPATH' ) ) {
	die( 'Direct access forbidden.' );
}

use ET\Builder\Framework\DependencyManagement\Interfaces\DependencyInterface;
use ET\Builder\Packages\ModuleLibrary\ModuleRegistration;

/**
 * `OptimizePricingTablesItem` is consisted of functions used for OptimizePricingTablesItem such as Front-End rendering, REST API Endpoints etc.
 *
 * This is a dependency class and can be used as a dependency for `DependencyTree`.
 *
 * @since ??
 */
class OptimizePricingTablesItem implements DependencyInterface {
	use OptimizePricingTablesItemTrait\RenderCallbackTrait;

	/**
	 * Loads `OptimizePricingTablesItem` and registers Front-End render callback and REST API Endpoints.
	 *
	 * @since ??
	 *
	 * @return void
	 */
	public function load() {
		$module_json_folder_path = DIVIOPTIMIZER_MODULES_JSON_PATH . 'OptimizePricingTablesItem/';

		add_action(
			'init',
			function() use ( $module_json_folder_path ) {
				ModuleRegistration::register_module(
					$module_json_folder_path,
					[
						'render_callback' => [ OptimizePricingTablesItem::class, 'render_callback' ],
					]
				);
			}
		);
	}
}
