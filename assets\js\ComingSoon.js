// Countdown Timer Initialization (Reusable by ID)
function initComingSoonTimer(id, launchDate, hideDay, hideSecond) {
  const section = document.getElementById(id);
  const daysEl = section.querySelector(".dotm_coming_soon_days");
  const hoursEl = section.querySelector(".dotm_coming_soon_hours");
  const minutesEl = section.querySelector(".dotm_coming_soon_minutes");
  const secondsEl = section.querySelector(".dotm_coming_soon_seconds");
  const warningEl = section.querySelector(".dotm_coming_soon_warning");

  function updateCountdown() {
    const now = new Date().getTime();
    const target = new Date(launchDate).getTime();
    const distance = target - now;

    let days = 0,
      hours = 0,
      minutes = 0,
      seconds = 0;

    if (distance > 0) {
      days = Math.floor(distance / (1000 * 60 * 60 * 24));
      hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
      minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
      seconds = Math.floor((distance % (1000 * 60)) / 1000);
      if (warningEl) warningEl.style.display = "none";
    } else {
      if (warningEl) warningEl.style.display = "block";
    }

    if (hideDay === "off") {
      daysEl.textContent = String(days).padStart(2, "0");
    }

    hoursEl.textContent = String(hours).padStart(2, "0");
    minutesEl.textContent = String(minutes).padStart(2, "0");
    if (hideSecond === "off") {
      secondsEl.textContent = String(seconds).padStart(2, "0");
    }
  }

  updateCountdown();
  setInterval(updateCountdown, 1000);
}

// Example usage: Set your launch date here
// initComingSoonTimer("coming-soon-section-1", "2025-07-01T00:00:00");
