<?php

/**
 * Module: Static Module class.
 *
 * @package MEE\Modules\TwitterButton
 * @since ??
 */

namespace MEE\Modules\TwitterButton;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

use ET\Builder\Framework\DependencyManagement\Interfaces\DependencyInterface;
use ET\Builder\Packages\ModuleLibrary\ModuleRegistration;


/**
 * `TwitterButton` is consisted of functions used for Static Module such as Front-End rendering, REST API Endpoints etc.
 *
 * This is a dependency class and can be used as a dependency for `DependencyTree`.
 *
 * @since ??
 */
class TwitterButton implements DependencyInterface
{
	use TwitterButtonTrait\RenderCallbackTrait;
	use TwitterButtonTrait\ModuleClassnamesTrait;
	use TwitterButtonTrait\ModuleStylesTrait;
	use TwitterButtonTrait\ModuleScriptDataTrait;

	/**
	 * Loads `TwitterButton` and registers Front-End render callback and REST API Endpoints.
	 *
	 * @since ??
	 *
	 * @return void
	 */
	public function load()
	{
		$module_json_folder_path = DIVIOPTIMIZER_MODULES_JSON_PATH . 'TwitterButton/';

		add_action(
			'init',
			function () use ($module_json_folder_path) {
				ModuleRegistration::register_module(
					$module_json_folder_path,
					[
						'render_callback' => [TwitterButton::class, 'render_callback'],
					]
				);
			}
		);
	}
}
