// External dependencies.
import React, { ReactElement } from 'react';

// Divi dependencies.
import {
  StyleContainer,
  StylesProps,
  CssStyle,
  TextStyle,
  CommonStyle,
} from '@divi/module';

// Local dependencies.
import { OptimizePricingTablesItemAttrs } from './types';
import { cssFields } from './custom-css';
import { iconFontDeclaration } from './style-declarations';
import { OptimizePricingTablesAttrs } from "../OptimizePricingTables/types";

/**
 * OptimizePricingTablesItem's style components.
 *
 * @since ??
 */
 export const ModuleStyles = ({
  attrs,
  parentAttrs,
  elements,
  settings,
  orderClass,
  mode,
  state,
  noStyleTag,
}: StylesProps<OptimizePricingTablesItemAttrs, OptimizePricingTablesAttrs>): ReactElement => {
  // const contentContainerSelector = `${orderClass} .example_child_module__content-container`;
  const headerContainerSelector = `${orderClass} .dotm_optimize_pricing_tables_item_header`;
  // const priceSectionSelector = `${orderClass} .dotm_optimize_pricing_tables_item_price_section`;
  const buttonContainerSelector = `${orderClass} .dotm_optimize_pricing_tables_item_button_container`;
  const tableItemSelector = `${orderClass} .dotm_optimize_pricing_tables_item`;
  const buttonType = attrs?.button?.advanced?.type?.desktop?.value ?? 'inline-block';
  const scaleValue = attrs?.featureText?.advanced?.highlight?.desktop?.value ?? '';
  const zIndex = attrs?.featureText?.advanced?.zIndex?.desktop?.value ?? '';

  return (
    <StyleContainer mode={mode} state={state} noStyleTag={noStyleTag}>
      {/* Module */}
      {elements.style({
        attrName: 'module',
        styleProps: {
          disabledOn: {
            disabledModuleVisibility: settings?.disabledModuleVisibility,
          },
          advancedStyles: [
            // {
            //   componentName: 'divi/text',
            //   props: {
            //     selector: contentContainerSelector,
            //     attr: attrs?.module?.advanced?.text,
            //   }
            // },
            {
              componentName: 'divi/text',
              props: {
                selector: headerContainerSelector,
                attr: attrs?.headingWrapper?.advanced?.text ?? parentAttrs?.headingWrapper?.advanced?.text,
                important: true,
              }
            },
            {
              componentName: 'divi/common',
              props: {
                attr: attrs?.featureText?.advanced?.highlight,
                property:  `transform: scale(${scaleValue}); z-index: ${zIndex};`,
              }
            },
          ]
        },
      })}

      {/* featureText */}
      {elements.style({
        attrName: 'featureText',
        styleProps: {
          advancedStyles: [
            {
              componentName: 'divi/common',
              props: {
                attr: attrs?.featureText?.advanced?.badgePlacement,
                property: `top`,
              }
            },
          ]
        },
      })}

      {/* headingWrapper */}
      {elements.style({
        attrName: 'headingWrapper',
      })}

      {/* priceContainer */}
      {elements.style({
        attrName: 'priceContainer',
        styleProps: {
          advancedStyles: [
            {
              componentName: 'divi/common',
              props: {
                attr: attrs?.priceContainer?.decoration?.alignment ?? parentAttrs?.priceContainer?.decoration?.alignment,
                property: `justify-content`
              }
            }
          ]
        },
      })}

      {/* priceWrapper */}
      {elements.style({
        attrName: 'priceWrapper',
      })}

      {/* Title */}
      {elements.style({
        attrName: 'title',
      })}

      {/* subTitle */}
      {elements.style({
        attrName: 'subTitle',
      })}

      {/* currency */}
      {elements.style({
        attrName: 'currency',
      })}

      {/* frequency */}
      {elements.style({
        attrName: 'frequency',
      })}

      {/* price */}
      {elements.style({
        attrName: 'price',
      })}

      {/* Content */}
      {elements.style({
        attrName: 'content',
      })}

      {/* button */}
      {elements.style({
        attrName: 'button',
        styleProps: {
          advancedStyles: [
            {
              componentName: 'divi/common',
              props: {
                attr: attrs?.button?.advanced?.type,
                property: "display"
              }
            },
            {
              componentName: 'divi/common',
              props: {
                selector: buttonContainerSelector,
                attr: attrs?.button?.advanced?.buttonAlignment,
                property: buttonType ===  'inline-block' ? `text-align` : "",
              }
            },
          ]
        }
      })}

      {/* Icon */}
      {elements.style({
        attrName: 'icon',
        styleProps: {
          advancedStyles: [
            {
              componentName: "divi/common",
              props: {
                attr: attrs?.icon?.innerContent ?? parentAttrs?.icon?.innerContent,
                declarationFunction: iconFontDeclaration,
              }
            },
            {
              componentName: "divi/common",
              props: {
                attr: attrs?.icon?.advanced?.color ?? parentAttrs?.icon?.advanced?.color,
                property:"color",
                important: true,
              }
            },
            {
              componentName: "divi/common",
              props: {
                attr:attrs?.icon?.advanced?.size ?? parentAttrs?.icon?.advanced?.size,
                property:"font-size",
                important: true,
              }
            }
          ]
        }
      })}

      {/* image */}
      {elements.style({
        attrName: 'image',
        styleProps: {
          advancedStyles: [
            {
              componentName: "divi/common",
              props: {
                attr: attrs?.image?.decoration?.img_width ?? parentAttrs?.image?.decoration?.img_width,
                property:"width",
                important: true,
              }
            },
            {
              componentName: "divi/common",
              props: {
                attr: attrs?.image?.decoration?.img_height ?? parentAttrs?.image?.decoration?.img_height,
                property:"height",
                important: true,
              }
            }
          ]
        }
      })}

      {/*
       * We need to add CssStyle at the very bottom of other components
       * so that custom css can override module styles till we find a
       * more elegant solution.
       */}
      <CssStyle
        selector={orderClass}
        attr={attrs.css}
        cssFields={cssFields}
      />
      <CssStyle
        selector={orderClass}
        attr={attrs.css}
        cssFields={cssFields}
      />
    </StyleContainer>
  );
};
