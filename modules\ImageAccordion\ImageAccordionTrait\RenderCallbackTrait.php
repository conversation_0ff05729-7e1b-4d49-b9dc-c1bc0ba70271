<?php

/**
 * ImageAccordionItem::render_callback()
 *
 * @package MEE\Modules\ImageAccordionItem
 * @since ??
 */

namespace MEE\Modules\ImageAccordion\ImageAccordionTrait;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

// phpcs:disable ET.Sniffs.ValidVariableName.UsedPropertyNotSnakeCase -- WP use snakeCase in \WP_Block_Parser_Block

use ET\Builder\Framework\Utility\HTMLUtility;
use ET\Builder\FrontEnd\BlockParser\BlockParserStore;
use ET\Builder\Packages\Module\Module;
use ET\Builder\Packages\Module\Options\Element\ElementComponents;
use MEE\Modules\ImageAccordion\ImageAccordion;

trait RenderCallbackTrait
{
	use ModuleClassnamesTrait;
	use ModuleStylesTrait;
	use ModuleScriptDataTrait;
	/**
	 * ImageAccordion render callback which outputs server side rendered HTML on the Front-End.
	 *
	 * @since ??
	 *
	 * @param array          $attrs Block attributes that were saved by VB.
	 * @param string         $content          Block content.
	 * @param \WP_Block      $block            Parsed block object that being rendered.
	 * @param ModuleElements $elements         ModuleElements instance.
	 *
	 * @return string HTML rendered of Parent module.
	 */
	public static function render_callback($attrs, $content, $block, $elements)
	{
		$children_ids = $block->parsed_block['innerBlocks'] ? array_map(
			function ($inner_block) {
				return $inner_block['id'];
			},
			$block->parsed_block['innerBlocks']
		) : [];

		$parent       = BlockParserStore::get_parent($block->parsed_block['id'], $block->parsed_block['storeInstance']);
		$parent_attrs = $parent->attrs ?? [];

		$uuid = substr(str_shuffle(str_repeat($x='0123456789abcdefghijklmnopqrstuvwxyz', ceil(10/strlen($x)) )), 1, 10) . (new \DateTime())->format('U');

		$accordion_type = $attrs['accordion_container']['advanced']['accordion_type']['desktop']['value'] ?? '';
		$event_type = $attrs['accordion_container']['advanced']['event_type']['desktop']['value'] ?? '';
		$content_animation_name = $attrs['accordion_container']['advanced']['content_animation']['desktop']['value'] ?? '';

		// Content container.
		$main_container = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => "dotm_image_accordion_container horizontal {$content_animation_name}",
					'id' => $uuid
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $content,
			]
		);

		return Module::render(
			[
				// FE only.
				'orderIndex'          => $block->parsed_block['orderIndex'],
				'storeInstance'       => $block->parsed_block['storeInstance'],

				// VB equivalent.
				'id'                  => $block->parsed_block['id'],
				'name'                => $block->block_type->name,
				'moduleCategory'      => $block->block_type->category,
				'attrs'               => $attrs,
				'elements'            => $elements,
				'classnamesFunction'  => [ImageAccordion::class, 'module_classnames'],
				'scriptDataComponent' => [ImageAccordion::class, 'module_script_data'],
				'stylesComponent'     => [ImageAccordion::class, 'module_styles'],
				'parentAttrs'         => $parent_attrs,
				'parentId'            => $parent->id ?? '',
				'parentName'          => $parent->blockName ?? '',
				'children'            => [
					ElementComponents::component(
						[
							'attrs'         => $attrs['module']['decoration'] ?? [],
							'id'            => $block->parsed_block['id'],

							// FE only.
							'orderIndex'    => $block->parsed_block['orderIndex'],
							'storeInstance' => $block->parsed_block['storeInstance'],
						]
					) . $main_container,
					'childrenIds'         => $children_ids,
					
					HTMLUtility::render(
						[
							'tag' => 'script',
							'attributes' => [],
							'childrenSanitizer' => 'et_core_esc_previously',
							'children' => "document.addEventListener('DOMContentLoaded', function() {
                                const id = " . json_encode($uuid) . ";
                                const trigger = " . json_encode($event_type) . ";
                                const direction = " . json_encode($accordion_type) . ";
                                const animation = " . json_encode($content_animation_name) . ";
                                accordion(id, trigger, direction, animation);
                            });",
						]
					),
				]
			]
		);
	}
}
