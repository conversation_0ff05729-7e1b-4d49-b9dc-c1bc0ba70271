<?php

/**
 * Module: ImageCarouselItem class.
 *
 * @package MEE\Modules\ImageCarouselItem
 * @since ??
 */

namespace MEE\Modules\ImageCarouselItem;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

use ET\Builder\Framework\DependencyManagement\Interfaces\DependencyInterface;
use ET\Builder\Packages\ModuleLibrary\ModuleRegistration;

/**
 * `ImageCarouselItem` is consisted of functions used for ImageCarouselItem such as Front-End rendering, REST API Endpoints etc.
 *
 * This is a dependency class and can be used as a dependency for `DependencyTree`.
 *
 * @since ??
 */
class ImageCarouselItem implements DependencyInterface
{
	use ImageCarouselItemTrait\RenderCallbackTrait;

	/**
	 * Loads `ImageCarouselItem` and registers Front-End render callback and REST API Endpoints.
	 *
	 * @since ??
	 *
	 * @return void
	 */
	public function load()
	{
		$module_json_folder_path = DIVIOPTIMIZER_MODULES_JSON_PATH . 'ImageCarouselItem/';

		add_action(
			'init',
			function () use ($module_json_folder_path) {
				ModuleRegistration::register_module(
					$module_json_folder_path,
					[
						'render_callback' => [ImageCarouselItem::class, 'render_callback'],
					]
				);
			}
		);
	}
}
