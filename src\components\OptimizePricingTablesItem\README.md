# Child Module
It is an example child module. Every child module should declare with the `child-module` category. Check the `module.json` to see the difference.


## Folder Structure
```
child-module
├── README.md
├── __mock-data__
│   └── attrs.ts
├── __tests__
│   ├── __snapshots__
│   │   └── edit.tsx.snap
│   └── edit.tsx
├── constants.ts
├── custom-css.ts
├── edit.tsx
├── index.ts
├── module.json
├── module.scss
├── placeholder-content.ts
├── settings-advanced.tsx
├── settings-content.tsx
├── settings-design.tsx
├── stories
│   └── edit.stories.tsx
├── style.scss
├── styles.tsx
└── types.ts
```