<?php

/**
 * ImageCarouselItem::render_callback()
 *
 * @package MEE\Modules\ImageCarouselItem
 * @since ??
 */

namespace MEE\Modules\ImageCarousel\ImageCarouselTrait;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

// phpcs:disable ET.Sniffs.ValidVariableName.UsedPropertyNotSnakeCase -- WP use snakeCase in \WP_Block_Parser_Block

use ET\Builder\FrontEnd\BlockParser\BlockParserStore;
use ET\Builder\Packages\Module\Module;
use ET\Builder\Framework\Utility\HTMLUtility;
use ET\Builder\Packages\Module\Options\Element\ElementComponents;
use MEE\Modules\ImageCarousel\ImageCarousel;
use ET\Builder\Packages\IconLibrary\IconFont\Utils;

trait RenderCallbackTrait
{
	use ModuleClassnamesTrait;
	use ModuleStylesTrait;
	use ModuleScriptDataTrait;
	/**
	 * ImageCarousel render callback which outputs server side rendered HTML on the Front-End.
	 *
	 * @since ??
	 *
	 * @param array          $attrs Block attributes that were saved by VB.
	 * @param string         $content          Block content.
	 * @param \WP_Block      $block            Parsed block object that being rendered.
	 * @param ModuleElements $elements         ModuleElements instance.
	 *
	 * @return string HTML rendered of Parent module.
	 */
	public static function render_callback($attrs, $content, $block, $elements)
	{
		$children_ids = $block->parsed_block['innerBlocks'] ? array_map(
			function ($inner_block) {
				return $inner_block['id'];
			},
			$block->parsed_block['innerBlocks']
		) : [];

		

		$parent       = BlockParserStore::get_parent($block->parsed_block['id'], $block->parsed_block['storeInstance']);
		$parent_attrs = $parent->attrs ?? [];

		$use_auto_play = $attrs['carousel']['advanced']['use_auto_play']['desktop']['value'] ?? '';
		$use_slide_effect = $attrs['carousel']['advanced']['use_slide_effect']['desktop']['value'] ?? '';
		$hide_arrows = $attrs['carousel']['advanced']['hide_arrows']['desktop']['value'] ?? 'off';
		$hide_indicators = $attrs['carousel']['advanced']['hide_indicators']['desktop']['value'] ?? 'off';
		$interval = $attrs['carousel']['advanced']['auto_slide_intervel']['desktop']['value'] ?? '';
		$autoStart = $use_auto_play === 'on';
		$navigation_btns = $attrs['navigation_btns']['innerContent']['desktop']['value'] ?? [];
		$transitionEffect = $use_slide_effect === 'on' ? 'slide' : 'fade';

		$uuid = substr(str_shuffle(str_repeat($x='0123456789abcdefghijklmnopqrstuvwxyz', ceil(10/strlen($x)) )), 1, 10) . (new \DateTime())->format('U');

		$slides = count($children_ids);

		$option = [
			'transitionEffect' => $transitionEffect,
			'interval' => $interval,
			'autoStart' => $autoStart,
			'hide_arrows' => $hide_arrows,
			'hide_indicators' => $hide_indicators,
		];

		if ($slides > 0) {
			$indicators = [];
			for ($i = 0; $i < $slides; $i++) {
				$indicators[] = HTMLUtility::render([
					'tag'        => 'button',
					'attributes' => ['class' => 'dotm_image_carousel_indicator'],
					'childrenSanitizer' => 'et_core_esc_previously',
					'children'   => "",
				]);
			}
			$indicators = implode('', $indicators);
		}

		$indicator_container = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'dotm_image_carousel_indicators',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $indicators,
			]
		);

		$left_icon = HTMLUtility::render(
			[
				'tag'               => 'span',
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => Utils::process_font_icon($navigation_btns),
			]
		);

		$left_arrow = HTMLUtility::render(
			[
				'tag'               => 'button',
				'attributes'        => [
					'class' => 'dotm_image_carousel_nav_button dotm_image_carousel_prev',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $left_icon,
			]
		);

		$right_icon = HTMLUtility::render(
			[
				'tag'               => 'span',

				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => Utils::process_font_icon($navigation_btns),
			]
		);

		$right_arrow = HTMLUtility::render(
			[
				'tag'               => 'button',
				'attributes'        => [
					'class' => 'dotm_image_carousel_nav_button dotm_image_carousel_next',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $right_icon,
			]
		);


		$container = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'dotm_image_carousel_container',
					'id'    => $uuid,
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $content . ($hide_arrows === 'off' ? $left_arrow : '') . ($hide_arrows === 'off' ? $right_arrow : '') . ($hide_indicators === 'off' ? $indicator_container : ''),
			]
		);

		return Module::render(
			[
				// FE only.
				'orderIndex'          => $block->parsed_block['orderIndex'],
				'storeInstance'       => $block->parsed_block['storeInstance'],

				// VB equivalent.
				'id'                  => $block->parsed_block['id'],
				'name'                => $block->block_type->name,
				'moduleCategory'      => $block->block_type->category,
				'attrs'               => $attrs,
				'elements'            => $elements,
				'classnamesFunction'  => [ImageCarousel::class, 'module_classnames'],
				'scriptDataComponent' => [ImageCarousel::class, 'module_script_data'],
				'stylesComponent'     => [ImageCarousel::class, 'module_styles'],
				'parentAttrs'         => $parent_attrs,
				'parentId'            => $parent->id ?? '',
				'parentName'          => $parent->blockName ?? '',
				'children'            => [
					ElementComponents::component(
						[
							'attrs'         => $attrs['module']['decoration'] ?? [],
							'id'            => $block->parsed_block['id'],

							// FE only.
							'orderIndex'    => $block->parsed_block['orderIndex'],
							'storeInstance' => $block->parsed_block['storeInstance'],
						]
					) . $container,
					'childrenIds'         => $children_ids,

					HTMLUtility::render(
						[
							'tag' => 'script',
							'attributes' => [],
							'childrenSanitizer' => 'et_core_esc_previously',
							'children' => "document.addEventListener('DOMContentLoaded', function() {
                                const options = " . json_encode($option) . ";
                                const uuid = " . json_encode($uuid) . ";
                                createCarousel(uuid, options);
                            });",
						]
					),
				]
			]
		);
	}
}
