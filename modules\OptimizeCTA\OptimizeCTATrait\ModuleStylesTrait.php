<?php

/**
 * OptimizeCTA::module_styles().
 *
 * @package MEE\Modules\OptimizeCTA
 * @since ??
 */

namespace MEE\Modules\OptimizeCTA\OptimizeCTATrait;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

use ET\Builder\FrontEnd\Module\Style;
use ET\Builder\Packages\Module\Options\Text\TextStyle;
use ET\Builder\Packages\Module\Options\Css\CssStyle;
use MEE\Modules\OptimizeCTA\OptimizeCTA;
use ET\Builder\Packages\IconLibrary\IconFont\Utils;

trait ModuleStylesTrait
{

	use CustomCssTrait;
	use StyleDeclarationTrait;

	/**
	 * OptimizeCTA's style components.
	 *
	 * This function is equivalent of JS function ModuleStyles located in
	 * src/components/OptimizeCTA/styles.tsx.
	 *
	 * @since ??
	 *
	 * @param array $args {
	 *     An array of arguments.
	 *
	 *      @type string $id                Module ID. In VB, the ID of module is UUIDV4. In FE, the ID is order index.
	 *      @type string $name              Module name.
	 *      @type string $attrs             Module attributes.
	 *      @type string $parentAttrs       Parent attrs.
	 *      @type string $orderClass        Selector class name.
	 *      @type string $parentOrderClass  Parent selector class name.
	 *      @type string $wrapperOrderClass Wrapper selector class name.
	 *      @type string $settings          Custom settings.
	 *      @type string $state             Attributes state.
	 *      @type string $mode              Style mode.
	 *      @type ModuleElements $elements  ModuleElements instance.
	 * }
	 */
	public static function module_styles($args)
	{
		$attrs    = $args['attrs'] ?? [];
		$elements = $args['elements'];
		$settings = $args['settings'] ?? [];
		$orderClass = $args['orderClass'];

		$container_selector = "{$orderClass} .dotm_optimize_cta_container";
		$image_container_selector = "{$orderClass} .dotm_optimize_cta_image_container";
		$content_container_selector = "{$orderClass} .dotm_optimize_cta_content_container";
		$buttons_container_selector = "{$orderClass} .dotm_optimize_cta_content_container_btns";

		$before_button_selector = "{$orderClass} .dotm_optimize_cta_content_container_btns_button::before";
		$after_button_selector = "{$orderClass} .dotm_optimize_cta_content_container_btns_button::after";
		$button_selector = "{$orderClass} .dotm_optimize_cta_content_container_btns_button";
		$use_btn_icon_right = $attrs['btn_icon']['advanced']['use']['desktop']['value'] ?? '';

		$before_extra_button_selector = "{$orderClass} .dotm_optimize_cta_content_container_btns_extra_button::before";
		$after_extra_button_selector = "{$orderClass} .dotm_optimize_cta_content_container_btns_extra_button::after";
		$extra_button_selector = "{$orderClass} .dotm_optimize_cta_content_container_btns_extra_button";
		$use_extra_btn_icon_right = $attrs['extra_btn_icon']['advanced']['use']['desktop']['value'] ?? '';

		$content_direction = $attrs['container']['advanced']['content_direction']['desktop']['value'] ?? '';
		$content_gap = $attrs['container']['advanced']['gap']['desktop']['value'] ?? '';

		$btn_icon = $attrs['btn_icon']['innerContent']['desktop']['value'] ?? [];
		$extra_btn_icon = $attrs['extra_btn_icon']['innerContent']['desktop']['value'] ?? [];

		

		Style::add(
			[
				'id'            => $args['id'],
				'name'          => $args['name'],
				'orderIndex'    => $args['orderIndex'],
				'storeInstance' => $args['storeInstance'],
				'styles'        => [
					// Module.
					$elements->style(
						[
							'attrName'   => 'module',
							'styleProps' => [
								'disabledOn'     => [
									'disabledModuleVisibility' => $settings['disabledModuleVisibility'] ?? null,
								],
								'advancedStyles' => [
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $container_selector,
											'attr'     => $attrs['container']['advanced']['content_direction'] ?? [],
											'property' => $content_direction === 'on' ? 'flex-direction: row-reverse;' : 'flex-direction: row;',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $container_selector,
											'attr'     => $attrs['container']['advanced']['content_alignment'] ?? [],
											'property' => "column-gap: {$content_gap}; align-items",
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $content_container_selector,
											'attr'     => $attrs['container']['decoration']['content_width'] ?? [],
											'property' => "width",
										]
									]


								]
							],
						]
					),

					// Image.
					$elements->style(
						[
							'attrName' => 'image',
							'styleProps' => [
								'advancedStyles' => [
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $image_container_selector,
											'attr'     => $attrs['image']['decoration']['img_width'] ?? [],
											'property' => "width",
										]
									]
								]
							]
						]
					),

					// Title.
					$elements->style(
						[
							'attrName' => 'title',
						]
					),

					// Content.
					$elements->style(
						[
							'attrName' => 'content',
						]
					),

					// button.
					$elements->style(
						[
							'attrName' => 'button',
							'styleProps' => [
								'advancedStyles' => [
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $buttons_container_selector,
											'attr'     => $attrs['container']['decoration']['button_possition'] ?? [],
											'property' => "text-align",
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $use_btn_icon_right === 'on' ? $after_button_selector : $before_button_selector,
											'attr'     => $attrs['btn_icon']['innerContent'] ?? [],
											'declarationFunction' => [OptimizeCTA::class, 'icon_font_declaration'],
										]
									],

									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $button_selector,
											'attr'     => $attrs['container']['decoration']['btn_icon_gap'] ?? [],
											'property' => $btn_icon !== '' ? "display: inline-flex; align-items: center; column-gap" : '',
										]
									],
								]
							]
						]
					),

					// button_hover.
					$elements->style(
						[
							'attrName' => 'button_hover',
						]
					),

					// extra_button.
					$elements->style(
						[
							'attrName' => 'extra_button',
							'styleProps' => [
								'advancedStyles' => [
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $use_extra_btn_icon_right === 'on' ? $after_extra_button_selector : $before_extra_button_selector,
											'attr'     => $attrs['extra_btn_icon']['innerContent'] ?? [],
											'declarationFunction' => [OptimizeCTA::class, 'icon_font_declaration'],

										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $extra_button_selector,
											'attr'     => $attrs['container']['decoration']['extra_btn_icon_gap'] ?? [],
											'property' => $extra_btn_icon !== '' ? "display: inline-flex; align-items: center; column-gap" : '',

										]
									],
								]
							]
						]
					),

					// extra_button_hover.
					$elements->style(
						[
							'attrName' => 'extra_button_hover',
						]
					),

					/*
					 * We need to add CssStyle at the very bottom of other
					 * components so that custom css can override module styles
					 * till we find a more elegant solution.
					 */
					CssStyle::style(
						[
							'selector'  => $args['orderClass'],
							'attr'      => $attrs['css'] ?? [],
							'cssFields' => OptimizeCTA::custom_css(),
						]
					),

					// ATTENTION: The code is intentionally added and commented in FE only as an example of expected value format.
					// If you have custom style processing, the style output should be passed as an `array` of style declarations
					// to the `styles` property of the `Style::add` method. For example:
					// [
					// 	[
					// 		'atRules'     => false,
					// 		'selector'    => "{$args['orderClass']} .example_static_module__content-container",
					// 		'declaration' => 'color: red;'
					// 	],
					// 	[
					// 		'atRules'     => '@media only screen and (max-width: 767px)',
					// 		'selector'    => "{$args['orderClass']} .example_static_module__content-container",
					// 		'declaration' => 'color: green;'
					// 	],
					// ],

					// The code below is an example of how to use the `CssStyle::style` method to generate CSS style.
					CssStyle::style(
						[
							'selector'  => $args['orderClass'],
							'attr'      => $attrs['css'] ?? [],
							'cssFields' => OptimizeCTA::custom_css(),
						]
					),
				],
			]
		);
	}
}
