<?php
/**
 * ChildModule::render_callback()
 *
 * @package MEE\Modules\ChildModule
 * @since ??
 */

namespace MEE\Modules\Breadcrumbs\BreadcrumbsTrait;

if ( ! defined( 'ABSPATH' ) ) {
	die( 'Direct access forbidden.' );
}

// phpcs:disable ET.Sniffs.ValidVariableName.UsedPropertyNotSnakeCase -- WP use snakeCase in \WP_Block_Parser_Block

use ET\Builder\FrontEnd\BlockParser\BlockParserStore;
use ET\Builder\Packages\Module\Module;
use ET\Builder\Packages\Module\Options\Element\ElementComponents;
use MEE\Modules\Breadcrumbs\Breadcrumbs;
use ET\Builder\Framework\Utility\HTMLUtility;

trait RenderCallbackTrait {
	use ModuleClassnamesTrait;
	use ModuleStylesTrait;
	use ModuleScriptDataTrait;
	/**
	 * Parent module render callback which outputs server side rendered HTML on the Front-End.
	 *
	 * @since ??
	 *
	 * @param array          $attrs Block attributes that were saved by VB.
	 * @param string         $content          Block content.
	 * @param \WP_Block      $block            Parsed block object that being rendered.
	 * @param ModuleElements $elements         ModuleElements instance.
	 *
	 * @return string HTML rendered of Parent module.
	 */
	public static function render_callback( $attrs, $content, $block, $elements ) {
		$children_ids = $block->parsed_block['innerBlocks'] ? array_map(
			function( $inner_block ) {
				return $inner_block['id'];
			},
			$block->parsed_block['innerBlocks']
		) : [];

		$parent       = BlockParserStore::get_parent( $block->parsed_block['id'], $block->parsed_block['storeInstance'] );
		$parent_attrs = $parent->attrs ?? [];

		$home_link_url = $attrs['home_btn']['innerContent']['links']['desktop']['value']['url'] ?? '';
		$home_link_target = $blockattrs['home_btn']['innerContent']['links']['desktop']['value']['target']?? '';
	
		$home_anchor = $elements->render(
			[
				'attrName'      => 'home_btn',
				'htmlAttributes' => [
					'href' => $home_link_url,
					'target' => $home_link_target == 'on' ? '_blank' : '_black',
				],
			]
		);

		$home_li= HTMLUtility::render(
			[
				'tag'               => 'li',
				'attributes'        => [
					'class' => 'dotm_breadrumbs_list_home',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $home_anchor,
			]
		);

		$main_ul = HTMLUtility::render(
			[
				'tag'               => 'ul',
				'attributes'        => [
					'class' => 'dotm_breadrumbs_list',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => $home_li . $content, 
				
			]
		);
		


		return Module::render(
			[
				// FE only.
				'orderIndex'          => $block->parsed_block['orderIndex'],
				'storeInstance'       => $block->parsed_block['storeInstance'],

				// VB equivalent.
				'id'                  => $block->parsed_block['id'],
				'name'                => $block->block_type->name,
				'moduleCategory'      => $block->block_type->category,
				'attrs'               => $attrs,
				'elements'            => $elements,
				'classnamesFunction'  => [ Breadcrumbs::class, 'module_classnames' ],
				'scriptDataComponent' => [ Breadcrumbs::class, 'module_script_data' ],
				'stylesComponent'     => [ Breadcrumbs::class, 'module_styles' ],
				'parentAttrs'         => $parent_attrs,
				'parentId'            => $parent->id ?? '',
				'parentName'          => $parent->blockName ?? '',
				'children'            => ElementComponents::component(
					[
						'attrs'         => $attrs['module']['decoration'] ?? [],
						'id'            => $block->parsed_block['id'],

						// FE only.
						'orderIndex'    => $block->parsed_block['orderIndex'],
						'storeInstance' => $block->parsed_block['storeInstance'],
					]
				) . $main_ul,
				'childrenIds'         => $children_ids,
				
			]
		);
	}
}
