<?php

namespace MEE\Modules\TextScroll\TextScrollTrait;



if (!defined('ABSPATH')) {
    die('Direct access forbidden.');
}

use ET\Builder\Packages\Module\Module;
use ET\Builder\Framework\Utility\HTMLUtility;
use ET\Builder\FrontEnd\BlockParser\BlockParserStore;
use MEE\Modules\TextScroll\TextScroll;
trait RenderCallbackTrait
{
    public static function render_callback($attrs, $content, $block, $elements)
    {
        // Extract attributes and set defaults
        $marquee_gap = $attrs['marquee_text']['advanced']['marquee_gap']['desktop']['value'] ?? '';
        $marquee_direction = $attrs['marquee_text']['advanced']['marquee_direction']['desktop']['value'] ?? '';
        $marquee_speed = $attrs['marquee_text']['advanced']['marquee_speed']['desktop']['value'] ?? '';
        $marquee_delay = $attrs['marquee_text']['advanced']['marquee_delay']['desktop']['value'] ?? '';
        $marquee_duplicate = $attrs['marquee_text']['advanced']['marquee_duplicate']['desktop']['value'] ?? '';
        $pause_on_hover = $attrs['marquee_text']['advanced']['pause_on_hover']['desktop']['value'] ?? '';

        // Convert settings to booleans
        $duplicated = $marquee_duplicate === 'on' ? true : false;
        $pauseOnHover = $pause_on_hover === 'on' ? true : false;

        // Prepare options to pass to JavaScript
        $marquee_options = [
            'marquee_speed' => (int)$marquee_speed,
            'marquee_gap' => (int)$marquee_gap,
            'marquee_delay' => (int)$marquee_delay,
            'marquee_direction' => $marquee_direction,
            'duplicated' => $duplicated,
            'pauseOnHover' => $pauseOnHover,
        ];

        // Render the marquee text
        $marquee_text = $elements->render(
            [
                'attrName' => 'marquee_text',
            ]
        );

        // Get parent block attributes
        $parent = BlockParserStore::get_parent($block->parsed_block['id'], $block->parsed_block['storeInstance']);
        $parent_attrs = $parent->attrs ?? [];

        // Generate the HTML output
        return Module::render(
            [
                'orderIndex' => $block->parsed_block['orderIndex'],
                'storeInstance' => $block->parsed_block['storeInstance'],
                'attrs' => $attrs,
                'elements' => $elements,
                'id' => $block->parsed_block['id'],
                'name' => $block->block_type->name,
                'moduleCategory' => $block->block_type->category,
                'classnamesFunction' => [TextScroll::class, 'module_classnames'],
                'stylesComponent' => [TextScroll::class, 'module_styles'],
                'scriptDataComponent' => [TextScroll::class, 'module_script_data'],
                'parentAttrs' => $parent_attrs,
                'parentId' => $parent->id ?? '',
                'parentName' => $parent->blockName ?? '',
                'children' => [
                    HTMLUtility::render(
                        [
                            'tag' => 'div',
                            'attributes' => [
                                'class' => 'dotm_text_scroll_marquee_container',
                            ],
                            'childrenSanitizer' => 'et_core_esc_previously',
                            'children' => $marquee_text,
                        ]
                    ),
                    HTMLUtility::render(
                        [
                            'tag' => 'script',
                            'attributes' => [],
                            'childrenSanitizer' => 'et_core_esc_previously',
                            'children' => "document.addEventListener('DOMContentLoaded', function() {
                                const marquee_options = " . json_encode($marquee_options) . ";
                                createMarquee(marquee_options);
                            });",
                        ]
                    ),
                ],
            ]
        );
    }
}
