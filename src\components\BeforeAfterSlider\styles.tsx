// External dependencies.
import React, { ReactElement } from 'react';

// Divi dependencies.
import {
  StyleContainer,
  StylesProps,
  CssStyle,
  TextStyle,
} from '@divi/module';

// Local dependencies.
import { BeforeAfterSliderAttrs } from './types';
import { cssFields } from './custom-css';
import { iconFontDeclaration } from './style-declarations';

/**
 * BeforeAfterSlider's style components.
 *
 * @since ??
 */
export const ModuleStyles = ({
  attrs,
  elements,
  settings,
  orderClass,
  mode,
  state,
  noStyleTag,
}: StylesProps<BeforeAfterSliderAttrs>): ReactElement => {
  const arrow_selector = `${orderClass} .dotm_before_after_slider_left_arrow, ${orderClass} .dotm_before_after_slider_right_arrow`;
  const slider_selector = `${orderClass} .dotm_before_after_slider_slide_line`;
  const circle_selector = `${orderClass} .dotm_before_after_slider_slide_line::after`;
  const label_before_selector = `${orderClass} .dotm_before_after_slider_before_label`;
  const label_after_selector = `${orderClass} .dotm_before_after_slider_after_label`;
  const vr_before_selector = `${orderClass} .vertical .dotm_before_after_slider_before_label`;
  const vr_after_selector = `${orderClass} .vertical .dotm_before_after_slider_after_label`;

  const slider_color = attrs?.slider_container?.advanced?.slider_color?.desktop?.value ?? '';
  const hide_cicle = attrs?.slider_container?.advanced?.hide_circle?.desktop?.value ?? '';
  const circle_round = attrs?.slider_container?.advanced?.circle_round?.desktop?.value ?? '';
  const containerMode = attrs?.slider_container?.advanced?.vertical_mode?.desktop?.value ?? 'off';
  const label_hr_position = attrs?.slider_container?.advanced?.label_hr_position?.desktop?.value ?? '';
  const label_vr_position = attrs?.slider_container?.advanced?.label_vr_position?.desktop?.value ?? '';


  return (
    <StyleContainer mode={mode} state={state} noStyleTag={noStyleTag}>
      {/* Module */}
      {elements.style({
        attrName: 'module',
        styleProps: {
          disabledOn: {
            disabledModuleVisibility: settings?.disabledModuleVisibility,
          },
          advancedStyles: [
            {
              componentName: "divi/common",
              props: {
                selector: slider_selector,
                attr: attrs?.slider_container?.advanced?.slider_color,
                property: `background: ${slider_color};`,
              }
            },
            {
              componentName: "divi/common",
              props: {
                selector: arrow_selector,
                attr: attrs?.slider_container?.advanced?.slider_color,
                property: `color: ${slider_color};`,
              }
            },
            {
              componentName: "divi/common",
              props: {
                selector: circle_selector,
                attr: attrs?.slider_container?.advanced?.slider_color,
                property: hide_cicle === 'on' ? `border: 3px solid ${slider_color};  box-shadow: 0 0 5px rgba(0, 0, 0, 0.2); border-radius: ${circle_round};` : '',
              }
            },
            {
              componentName: "divi/common",
              props: {
                selector: label_before_selector,
                attr: attrs?.slider_container?.advanced?.vertical_mode,
                property: containerMode === 'on' ? '' : `top: ${label_hr_position}; left: 10px; transform: translateY(-50%);`,
              }
            },
            {
              componentName: "divi/common",
              props: {
                selector: label_after_selector,
                attr: attrs?.slider_container?.advanced?.vertical_mode,
                property: containerMode === 'on' ? '' : `top: ${label_hr_position}; right: 10px; transform: translateY(-50%);`,
              }
            },
            {
              componentName: "divi/common",
              props: {
                selector: vr_before_selector,
                attr: attrs?.slider_container?.advanced?.vertical_mode,
                property: containerMode === 'on' ? `top: 10px; left: ${label_vr_position}; transform: translateX(-50%);` : '',
              }
            },
            {
              componentName: "divi/common",
              props: {
                selector: vr_after_selector,
                attr: attrs?.slider_container?.advanced?.vertical_mode,
                property: containerMode === 'on' ? `bottom: 10px; left: ${label_vr_position}; transform: translateX(-50%);` : '',
              }
            },
          ]
        },
      })}
      {/* before_label */}
      {elements.style({
        attrName: 'before_label',
      })}

      {/* after_label */}
      {elements.style({
        attrName: 'after_label',
      })}

      {/* before_image */}
      {elements.style({
        attrName: 'before_image',
      })}

      {/* after_image */}
      {elements.style({
        attrName: 'after_image',
      })}

      

      {/*
       * We need to add CssStyle at the very bottom of other components
       * so that custom css can override module styles till we find a
       * more elegant solution.
       */}
      <CssStyle
        selector={orderClass}
        attr={attrs.css}
        cssFields={cssFields}
      />

    </StyleContainer>
  );
};
