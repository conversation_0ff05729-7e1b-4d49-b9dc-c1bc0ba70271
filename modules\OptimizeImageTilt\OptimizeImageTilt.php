<?php

/**
 * Module: OptimizeImageTilt class.
 *
 * @package MEE\Modules\OptimizeImageTilt
 * @since ??
 */

namespace MEE\Modules\OptimizeImageTilt;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

use ET\Builder\Framework\DependencyManagement\Interfaces\DependencyInterface;
use ET\Builder\Packages\ModuleLibrary\ModuleRegistration;


/**
 * `OptimizeImageTilt` is consisted of functions used for Static Module such as Front-End rendering, REST API Endpoints etc.
 *
 * This is a dependency class and can be used as a dependency for `DependencyTree`.
 *
 * @since ??
 */
class OptimizeImageTilt implements DependencyInterface
{
	use OptimizeImageTiltTrait\RenderCallbackTrait;
	use OptimizeImageTiltTrait\ModuleClassnamesTrait;
	use OptimizeImageTiltTrait\ModuleStylesTrait;
	use OptimizeImageTiltTrait\ModuleScriptDataTrait;

	/**
	 * Loads `OptimizeImageTilt` and registers Front-End render callback and REST API Endpoints.
	 *
	 * @since ??
	 *
	 * @return void
	 */
	public function load()
	{
		$module_json_folder_path = DIVIOPTIMIZER_MODULES_JSON_PATH . 'OptimizeImageTilt/';

		add_action(
			'init',
			function () use ($module_json_folder_path) {
				ModuleRegistration::register_module(
					$module_json_folder_path,
					[
						'render_callback' => [OptimizeImageTilt::class, 'render_callback'],
					]
				);
			}
		);
	}
}
