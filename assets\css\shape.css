/* .shape {
    position: relative;
    width: 150px;
    height: 150px;
    overflow: hidden;
} */

/* .shape img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
} */

/* <div class="shape cloud">
        <img src="https://emi.parkview.com/media/Image/Dashboard_952_working_desk_1_22.jpg" alt="Cloud Shape">
    </div> */


.dotm_circle {
    border-radius: 50%;
}

.dotm_ellipse {
    border-radius: 50% / 30%;
}

.dotm_triangle {
    clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
}

.dotm_diamond {
    clip-path: polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%);
}

.dotm_pentagon {
    clip-path: polygon(50% 0%, 100% 38%, 82% 100%, 18% 100%, 0% 38%);
}

.dotm_hexagon {
    clip-path: polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%);
}

.dotm_octagon {
    clip-path: polygon(30% 0%, 70% 0%, 100% 30%, 100% 70%, 70% 100%, 30% 100%, 0% 70%, 0% 30%);
}

.dotm_star {
    clip-path: polygon(50% 0%, 61% 35%, 98% 35%, 68% 57%, 79% 91%, 50% 70%, 21% 91%, 32% 57%, 2% 35%, 39% 35%);
}

.dotm_heart {
    clip-path: polygon(50% 15%, 61% 0%, 80% 0%, 100% 20%, 100% 45%, 50% 100%, 0% 45%, 0% 20%, 20% 0%, 39% 0%);
}

.dotm_parallelogram {
    clip-path: polygon(25% 0%, 100% 0%, 75% 100%, 0% 100%);
}

.dotm_trapezoid {
    clip-path: polygon(20% 0%, 80% 0%, 100% 100%, 0% 100%);
}

.dotm_crescent {
    clip-path: path("M 75 0 A 75 75 0 1 1 75 150 A 50 50 0 1 0 75 0 Z");
}

.dotm_arrow {
    clip-path: polygon(50% 0%, 100% 50%, 75% 50%, 75% 100%, 25% 100%, 25% 50%, 0% 50%);
}

.dotm_message {
    clip-path: polygon(0% 0%, 100% 0%, 100% 75%, 75% 75%, 75% 100%, 50% 75%, 0% 75%);
}

.dotm_close {
    clip-path: polygon(20% 0%, 0% 20%, 30% 50%, 0% 80%, 20% 100%, 50% 70%, 80% 100%, 100% 80%, 70% 50%, 100% 20%, 80% 0%, 50% 30%);
}

.dotm_cloud {
    clip-path: path("M25 60c-15-20 20-40 40-30s40 20 30 50c-10 30-50 20-70 0s-5-20 0-20z");
}