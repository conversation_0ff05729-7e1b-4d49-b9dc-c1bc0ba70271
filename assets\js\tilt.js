function tilt(options = {}) {
    const { scale_value = 1.1, use_reverse = 'off', use_glare = 'off' } = options;
    const tiltContainers = document.querySelectorAll('.dotm_image_tilt_container');

    tiltContainers.forEach((container) => {
        const tiltGlare = container.querySelector('.dotm_image_tilt_glare');
        // let isReversed = use_reverse === 'on';

        container.addEventListener('mousemove', (e) => {
            const { width, height, left, top } = container.getBoundingClientRect();
            const centerX = width / 2;
            const centerY = height / 2;
            const x = e.clientX - left - centerX; // Distance from center
            const y = e.clientY - top - centerY; // Distance from center

            const xRotation = (y / centerY) * 30; // Centered tilt on Y axis
            const yRotation = (x / centerX) * -30; // Centered tilt on X axis
            // const tiltDirection = isReversed ? -1 : 1;

            container.style.transform = `rotateX(${xRotation}deg) rotateY(${yRotation}deg) scale(${scale_value})`;

            if (use_glare === 'on') {
                const glareOpacity = Math.max(Math.abs(xRotation), Math.abs(yRotation)) / 30; // Adjust glare opacity dynamically

                tiltGlare.style.background = `linear-gradient(135deg, rgba(255, 255, 255, ${glareOpacity}) 0%, rgba(255, 255, 255, 0) 100%)`;
                tiltGlare.style.transform = `scale(${scale_value})`;
            }
        });

        container.addEventListener('mouseleave', () => {
            container.style.transform = `rotateX(0deg) rotateY(0deg) scale(1)`;
            if (use_glare === 'on') {
                tiltGlare.style.background = `rgba(255, 255, 255, 0)`; // Hide glare effect
                tiltGlare.style.transform = `scale(1)`;
            }
        });
    });
}
