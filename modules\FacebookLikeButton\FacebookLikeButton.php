<?php

/**
 * Module: FacebookLikeButton class.
 *
 * @package MEE\Modules\FacebookLikeButton
 * @since ??
 */

namespace MEE\Modules\FacebookLikeButton;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

use ET\Builder\Framework\DependencyManagement\Interfaces\DependencyInterface;
use ET\Builder\Packages\ModuleLibrary\ModuleRegistration;


/**
 * `FacebookLikeButton` is consisted of functions used for Static Module such as Front-End rendering, REST API Endpoints etc.
 *
 * This is a dependency class and can be used as a dependency for `DependencyTree`.
 *
 * @since ??
 */
class FacebookLikeButton implements DependencyInterface
{
	use FacebookLikeButtonTrait\RenderCallbackTrait;
	use FacebookLikeButtonTrait\ModuleClassnamesTrait;
	use FacebookLikeButtonTrait\ModuleStylesTrait;
	use FacebookLikeButtonTrait\ModuleScriptDataTrait;

	/**
	 * Loads `FacebookLikeButton` and registers Front-End render callback and REST API Endpoints.
	 *
	 * @since ??
	 *
	 * @return void
	 */
	public function load()
	{
		$module_json_folder_path = DIVIOPTIMIZER_MODULES_JSON_PATH . 'FacebookLikeButton/';

		add_action(
			'init',
			function () use ($module_json_folder_path) {
				ModuleRegistration::register_module(
					$module_json_folder_path,
					[
						'render_callback' => [FacebookLikeButton::class, 'render_callback'],
					]
				);
			}
		);
	}
}
