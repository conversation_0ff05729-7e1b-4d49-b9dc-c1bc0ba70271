<?php
/**
 * TypingEffect::custom_css().
 *
 * @package MEE\Modules\TypingEffect
 * @since ??
 */

namespace MEE\Modules\TypingEffect\TypingEffectTrait;

if ( ! defined( 'ABSPATH' ) ) {
	die( 'Direct access forbidden.' );
}

trait CustomCssTrait {

	/**
	 * Custom CSS fields
	 *
	 * This function is equivalent of JS const cssFields located in
	 * src/components/typing-effect/custom-css.ts.
	 *
	 * A minor difference with the JS const cssFields, this function did not have `label` property on each array item.
	 *
	 * @since ??
	 */
	public static function custom_css() {
		return \WP_Block_Type_Registry::get_instance()->get_registered( 'optm/typing-effect' )->customCssFields;
	}

}
