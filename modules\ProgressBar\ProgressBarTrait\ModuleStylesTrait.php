<?php

/**
 * ProgressBar::module_styles().
 *
 * @package MEE\Modules\ProgressBar
 * @since ??
 */

namespace MEE\Modules\ProgressBar\ProgressBarTrait;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

use ET\Builder\FrontEnd\Module\Style;
use ET\Builder\Packages\Module\Options\Text\TextStyle;
use ET\Builder\Packages\Module\Options\Css\CssStyle;
use MEE\Modules\ProgressBar\ProgressBar;

trait ModuleStylesTrait
{

	use CustomCssTrait;

	/**
	 * ProgressBar's style components.
	 *
	 * This function is equivalent of JS function ModuleStyles located in
	 * src/components/ProgressBar/styles.tsx.
	 *
	 * @since ??
	 *
	 * @param array $args {
	 *     An array of arguments.
	 *
	 *      @type string $id                Module ID. In VB, the ID of module is UUIDV4. In FE, the ID is order index.
	 *      @type string $name              Module name.
	 *      @type string $attrs             Module attributes.
	 *      @type string $parentAttrs       Parent attrs.
	 *      @type string $orderClass        Selector class name.
	 *      @type string $parentOrderClass  Parent selector class name.
	 *      @type string $wrapperOrderClass Wrapper selector class name.
	 *      @type string $settings          Custom settings.
	 *      @type string $state             Attributes state.
	 *      @type string $mode              Style mode.
	 *      @type ModuleElements $elements  ModuleElements instance.
	 * }
	 */
	public static function module_styles($args)
	{
		$attrs    = $args['attrs'] ?? [];
		$elements = $args['elements'];
		$settings = $args['settings'] ?? [];
		$order_class = $args['orderClass'];

		$progressContainerSelector = "{$order_class} .progress-container";
		$progressSelector = "{$order_class} .progress-bar";
		$circleSelector = "{$order_class} .bg-circle";
		$arrowSelector = "{$order_class} .arrow";
		$arrowPlacementSelector = "{$order_class} .progress-linear-percentage";
		$progressBgStyle = $attrs['progress']['advanced']['progress_bg_style']['desktop']['value'] ?? '';
		$useStripAnimation = $attrs['progress']['decoration']['use_strip_animation']['desktop']['value'] ?? '';
		$progressHorizontalPlacement = $attrs['progressBar']['advanced']['progress_horizontal_placement']['desktop']['value'] ?? '0';
		$progressIndicatorWidth = $attrs['progressBar']['decoration']['indicator_width']['desktop']['value'] ?? '';
		$progressIndicatorHeight = $attrs['progressBar']['decoration']['indicator_height']['desktop']['value'] ?? '';

		Style::add(
			[
				'id'            => $args['id'],
				'name'          => $args['name'],
				'orderIndex'    => $args['orderIndex'],
				'storeInstance' => $args['storeInstance'],
				'styles'        => [
					// Module.
					$elements->style(
						[
							'attrName'   => 'module',
							'styleProps' => [
								'disabledOn'     => [
									'disabledModuleVisibility' => $settings['disabledModuleVisibility'] ?? null,
								],
								'advancedStyles' => [
									[
										'componentName' => 'divi/common',
										'props'         => [
											'attr'     => $attrs['progressBar']['advanced']['progress_placement'] ?? [],
											'property' => "position: fixed; z-index: 9998; left: {$progressHorizontalPlacement}; top",
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $progressSelector,
											'attr'     => $attrs['progress']['decoration']['use_strip_animation'] ?? [],
											'property' => $useStripAnimation === 'on' ? 'background-size: 29px 27px !important; animation: moveStripes 1s linear infinite;' : '',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $circleSelector,
											'attr'     => $attrs['progress_container']['decoration']['progress_line_bg'] ?? [],
											'property' => "stroke",
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $arrowSelector,
											'attr'     => $attrs['progressBar']['decoration']['indicator_color'] ?? [],
											'property' => "border-bottom-width: {$progressIndicatorHeight}; border-bottom-style: solid; border-left: {$progressIndicatorWidth} solid transparent; border-right: {$progressIndicatorWidth} solid transparent; border-bottom-color",
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $arrowPlacementSelector,
											'attr'     => $attrs['progressBar']['decoration']['indicator_placement'] ?? [],
											'property' => "bottom",
										]
									]

								]
							],
						]
					),

					// progressBar.
					($progressBgStyle === 'gradient') ? $elements->style(
						[
							'attrName' => 'progressBar',
						]
					) : '',

					// progress_container.
					$elements->style(
						[
							'attrName' => 'progress_container',
						]
					),

					// count.
					$elements->style(
						[
							'attrName' => 'count',
						]
					),
					
					// count_two.
					$elements->style(
						[
							'attrName' => 'count_two',
						]
					),

					// circle_progress.
					$elements->style(
						[
							'attrName' => 'circle_progress',
						]
					),

					/*
					 * We need to add CssStyle at the very bottom of other
					 * components so that custom css can override module styles
					 * till we find a more elegant solution.
					 */
					CssStyle::style(
						[
							'selector'  => $args['orderClass'],
							'attr'      => $attrs['css'] ?? [],
							'cssFields' => ProgressBar::custom_css(),
						]
					),

					// ATTENTION: The code is intentionally added and commented in FE only as an example of expected value format.
					// If you have custom style processing, the style output should be passed as an `array` of style declarations
					// to the `styles` property of the `Style::add` method. For example:
					// [
					// 	[
					// 		'atRules'     => false,
					// 		'selector'    => "{$args['orderClass']} .example_static_module__content-container",
					// 		'declaration' => 'color: red;'
					// 	],
					// 	[
					// 		'atRules'     => '@media only screen and (max-width: 767px)',
					// 		'selector'    => "{$args['orderClass']} .example_static_module__content-container",
					// 		'declaration' => 'color: green;'
					// 	],
					// ],

					// The code below is an example of how to use the `CssStyle::style` method to generate CSS style.
					CssStyle::style(
						[
							'selector'  => $args['orderClass'],
							'attr'      => $attrs['css'] ?? [],
							'cssFields' => ProgressBar::custom_css(),
						]
					),
				],
			]
		);
	}
}
