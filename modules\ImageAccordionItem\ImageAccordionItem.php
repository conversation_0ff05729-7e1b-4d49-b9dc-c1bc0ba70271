<?php
/**
 * Module: ImageAccordionItem class.
 *
 * @package MEE\Modules\ImageAccordionItem
 * @since ??
 */

namespace MEE\Modules\ImageAccordionItem;

if ( ! defined( 'ABSPATH' ) ) {
	die( 'Direct access forbidden.' );
}

use ET\Builder\Framework\DependencyManagement\Interfaces\DependencyInterface;
use ET\Builder\Packages\ModuleLibrary\ModuleRegistration;

/**
 * `ImageAccordionItem` is consisted of functions used for ImageAccordionItem such as Front-End rendering, REST API Endpoints etc.
 *
 * This is a dependency class and can be used as a dependency for `DependencyTree`.
 *
 * @since ??
 */
class ImageAccordionItem implements DependencyInterface {
	use ImageAccordionItemTrait\RenderCallbackTrait;

	/**
	 * Loads `ImageAccordionItem` and registers Front-End render callback and REST API Endpoints.
	 *
	 * @since ??
	 *
	 * @return void
	 */
	public function load() {
		$module_json_folder_path = DIVIOPTIMIZER_MODULES_JSON_PATH . 'ImageAccordionItem/';

		add_action(
			'init',
			function() use ( $module_json_folder_path ) {
				ModuleRegistration::register_module(
					$module_json_folder_path,
					[
						'render_callback' => [ ImageAccordionItem::class, 'render_callback' ],
					]
				);
			}
		);
	}
}
