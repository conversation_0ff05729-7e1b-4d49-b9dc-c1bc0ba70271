<?php

/**
 * Module: Static Module class.
 *
 * @package MEE\Modules\OptimizeTextContent
 * @since ??
 */

namespace MEE\Modules\OptimizeTextContent;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

use ET\Builder\Framework\DependencyManagement\Interfaces\DependencyInterface;
use ET\Builder\Packages\ModuleLibrary\ModuleRegistration;


/**
 * `OptimizeTextContent` is consisted of functions used for Static Module such as Front-End rendering, REST API Endpoints etc.
 *
 * This is a dependency class and can be used as a dependency for `DependencyTree`.
 *
 * @since ??
 */
class OptimizeTextContent implements DependencyInterface
{
	use OptimizeTextContentTrait\RenderCallbackTrait;
	use OptimizeTextContentTrait\ModuleClassnamesTrait;
	use OptimizeTextContentTrait\ModuleStylesTrait;
	use OptimizeTextContentTrait\ModuleScriptDataTrait;

	/**
	 * Loads `OptimizeTextContent` and registers Front-End render callback and REST API Endpoints.
	 *
	 * @since ??
	 *
	 * @return void
	 */
	public function load()
	{
		$module_json_folder_path = DIVIOPTIMIZER_MODULES_JSON_PATH . 'OptimizeTextContent/';

		add_action(
			'init',
			function () use ($module_json_folder_path) {
				ModuleRegistration::register_module(
					$module_json_folder_path,
					[
						'render_callback' => [OptimizeTextContent::class, 'render_callback'],
					]
				);
			}
		);
	}
}
