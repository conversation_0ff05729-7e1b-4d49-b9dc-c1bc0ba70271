<?php

/**
 * TextDivider::module_styles().
 *
 * @package MEE\Modules\TextDivider
 * @since ??
 */

namespace MEE\Modules\TextDivider\TextDividerTrait;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

use ET\Builder\FrontEnd\Module\Style;
use ET\Builder\Packages\Module\Options\Text\TextStyle;
use ET\Builder\Packages\Module\Options\Css\CssStyle;
use MEE\Modules\TextDivider\TextDivider;

trait ModuleStylesTrait
{

	use CustomCssTrait;
	use StyleDeclarationTrait;

	/**
	 * TextDivider's style components.
	 *
	 * This function is equivalent of JS function ModuleStyles located in
	 * src/components/TextDivider/styles.tsx.
	 *
	 * @since ??
	 *
	 * @param array $args {
	 *     An array of arguments.
	 *
	 *      @type string $id                Module ID. In VB, the ID of module is UUIDV4. In FE, the ID is order index.
	 *      @type string $name              Module name.
	 *      @type string $attrs             Module attributes.
	 *      @type string $parentAttrs       Parent attrs.
	 *      @type string $orderClass        Selector class name.
	 *      @type string $parentOrderClass  Parent selector class name.
	 *      @type string $wrapperOrderClass Wrapper selector class name.
	 *      @type string $settings          Custom settings.
	 *      @type string $state             Attributes state.
	 *      @type string $mode              Style mode.
	 *      @type ModuleElements $elements  ModuleElements instance.
	 * }
	 */
	public static function module_styles($args)
	{
		$attrs    = $args['attrs'] ?? [];
		$elements = $args['elements'];
		$settings = $args['settings'] ?? [];
		$order_Class = $args['orderClass'];

		$divider_selector = $order_Class . ' .dotm_text_divider_before, ' . $order_Class . ' .dotm_text_divider_after';
		$divider_container_selector = $order_Class . ' .dotm_text_divider';

		$divider_style = $attrs['divider']['advanced']['style_type']['desktop']['value'] ?? '';
		$divider_color = $attrs['divider']['advanced']['color']['desktop']['value'] ?? '';
		$divider_height = $attrs['divider']['advanced']['height']['desktop']['value'] ?? '';
		$divider_position = $attrs['divider']['advanced']['position']['desktop']['value'] ?? '';

		Style::add(
			[
				'id'            => $args['id'],
				'name'          => $args['name'],
				'orderIndex'    => $args['orderIndex'],
				'storeInstance' => $args['storeInstance'],
				'styles'        => [
					// Module.
					$elements->style(
						[
							'attrName'   => 'module',
							'styleProps' => [
								'disabledOn'     => [
									'disabledModuleVisibility' => $settings['disabledModuleVisibility'] ?? null,
								],
								'advancedStyles' => [
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $divider_container_selector,
											'attr'     => $attrs['divider']['advanced']['position'] ?? [],
											'property' => "align-items: {$divider_position};",
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $divider_container_selector,
											'attr'     => $attrs['divider']['advanced']['divider_gap'] ?? [],
											'property' => "gap",
										]
									]
								]
							],
						]
					),

					// divider.
					$elements->style(
						[
							'attrName' => 'divider',
							'styleProps' => [
								'disabledOn' => [
									'disabledDividerVisibility' => $settings['disabledDividerVisibility'] ?? null,
								],
								'advancedStyles' => [
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $divider_selector,
											'attr'     => $attrs['divider']['advanced']['style_type'] ?? [],
											'property' => "border-top: $divider_height $divider_style $divider_color;",
										]
									],
								]
							],
						]
					),

					// Title.
					$elements->style(
						[
							'attrName' => 'title',
						]
					),

					// image_wrapper.
					$elements->style(
						[
							'attrName' => 'image_wrapper',
							'styleProps' => [
								'advancedStyles' => [
									[
										'componentName' => 'divi/common',
										'props'         => [
											'attr'     => $attrs['image_wrapper']['decoration']['size'] ?? [],
											'property' => 'width',
										]
									],
								]
							]
						]
					),

					// image.
					$elements->style(
						[
							'attrName' => 'image',
						]
					),

					// Icon.
					$elements->style(
						[
							'attrName'   => 'icon',
							'styleProps' => [
								'advancedStyles' => [
									[
										'componentName' => 'divi/common',
										'props'         => [
											'attr'                => $attrs['icon']['innerContent'] ?? [],
											'declarationFunction' => [TextDivider::class, 'icon_font_declaration'],
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'attr'     => $attrs['icon']['advanced']['color'] ?? [],
											'property' => 'color',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'attr'     => $attrs['icon']['advanced']['size'] ?? [],
											'property' => 'font-size',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => "{$order_Class} .dotm_text_divider_icon_wrapper",
											'attr'     => $attrs['icon']['advanced']['size'] ?? [],
											'property' => 'height',
										]
									],
								]
							]
						]
					),

					/*
					 * We need to add CssStyle at the very bottom of other
					 * components so that custom css can override module styles
					 * till we find a more elegant solution.
					 */
					CssStyle::style(
						[
							'selector'  => $args['orderClass'],
							'attr'      => $attrs['css'] ?? [],
							'cssFields' => TextDivider::custom_css(),
						]
					),

					// ATTENTION: The code is intentionally added and commented in FE only as an example of expected value format.
					// If you have custom style processing, the style output should be passed as an `array` of style declarations
					// to the `styles` property of the `Style::add` method. For example:
					// [
					// 	[
					// 		'atRules'     => false,
					// 		'selector'    => "{$args['orderClass']} .example_static_module__content-container",
					// 		'declaration' => 'color: red;'
					// 	],
					// 	[
					// 		'atRules'     => '@media only screen and (max-width: 767px)',
					// 		'selector'    => "{$args['orderClass']} .example_static_module__content-container",
					// 		'declaration' => 'color: green;'
					// 	],
					// ],

					// The code below is an example of how to use the `CssStyle::style` method to generate CSS style.
					CssStyle::style(
						[
							'selector'  => $args['orderClass'],
							'attr'      => $attrs['css'] ?? [],
							'cssFields' => TextDivider::custom_css(),
						]
					),
				],
			]
		);
	}
}
