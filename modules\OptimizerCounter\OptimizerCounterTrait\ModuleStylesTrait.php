<?php

/**
 * OptimizerCounter::module_styles().
 *
 * @package MEE\Modules\OptimizerCounter
 * @since ??
 */

namespace MEE\Modules\OptimizerCounter\OptimizerCounterTrait;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

use ET\Builder\FrontEnd\Module\Style;
use ET\Builder\Packages\Module\Options\Text\TextStyle;
use ET\Builder\Packages\Module\Options\Css\CssStyle;
use MEE\Modules\OptimizerCounter\OptimizerCounter;

trait ModuleStylesTrait
{

	use CustomCssTrait;

	/**
	 * OptimizerCounter's style components.
	 *
	 * This function is equivalent of JS function ModuleStyles located in
	 * src/components/OptimizerCounter/styles.tsx.
	 *
	 * @since ??
	 *
	 * @param array $args {
	 *     An array of arguments.
	 *
	 *      @type string $id                Module ID. In VB, the ID of module is UUIDV4. In FE, the ID is order index.
	 *      @type string $name              Module name.
	 *      @type string $attrs             Module attributes.
	 *      @type string $parentAttrs       Parent attrs.
	 *      @type string $orderClass        Selector class name.
	 *      @type string $parentOrderClass  Parent selector class name.
	 *      @type string $wrapperOrderClass Wrapper selector class name.
	 *      @type string $settings          Custom settings.
	 *      @type string $state             Attributes state.
	 *      @type string $mode              Style mode.
	 *      @type ModuleElements $elements  ModuleElements instance.
	 * }
	 */
	public static function module_styles($args)
	{
		$attrs    = $args['attrs'] ?? [];
		$elements = $args['elements'];
		$settings = $args['settings'] ?? [];
		$orderClass = $args['orderClass'];

		// Selectors for different counter elements
		$counterWrapperSelector = "$orderClass .dotm_optimizer_counter_wrapper";
		$counterBarSelector = "$orderClass .dotm_optimizer_counter_bar";
		$counterBarProgressSelector = "$orderClass .dotm_optimizer_counter_bar_progress";
		$counterCircleSelector = "$orderClass .dotm_optimizer_counter_circle";
		$counterCircleProgressSelector = "$orderClass .dotm_optimizer_counter_circle_progress";
		$counterCircleBgSelector = "$orderClass .dotm_optimizer_counter_circle_bg";
		$counterBlocksSelector = "$orderClass .dotm_optimizer_counter_blocks";
		$counterBlocksItemSelector = "$orderClass .dotm_optimizer_counter_blocks_item";
		$counterBlocksItemActiveSelector = "$orderClass .dotm_optimizer_counter_blocks_item.active";
		$stripSelector = "$orderClass .dotm_optimizer_counter_bar_progress.striped";

		$circleSize = $attrs['counterCircle']['decoration']['circleSize']['desktop']['value'] ?? '';

		$progressBgColor2 = $attrs['counterBar']['decoration']['progressBgColor2']['desktop']['value'] ?? '';
		$progressAnimationType = $attrs['counterAnimation']['advanced']['progressAnimationType']['desktop']['value'] ?? '';

		Style::add(
			[
				'id'            => $args['id'],
				'name'          => $args['name'],
				'orderIndex'    => $args['orderIndex'],
				'storeInstance' => $args['storeInstance'],
				'styles'        => [
					// Module.
					$elements->style(
						[
							'attrName'   => 'module',
							'styleProps' => [
								'disabledOn'     => [
									'disabledModuleVisibility' => $settings['disabledModuleVisibility'] ?? null,
								],
								'advancedStyles' => [
									[
										'componentName' => 'divi/text',
										'props'         => [
											'selector' => "{$args['orderClass']} .example_static_module__content-container",
											'attr'     => $attrs['module']['advanced']['text'] ?? [],
										]
									]
								]
							],
						]
					),

					// counterWrapper.
					$elements->style(
						[
							'attrName'   => 'counterWrapper',
							'styleProps' => [
								'disabledOn'     => [
									'disabledModuleVisibility' => $settings['disabledModuleVisibility'] ?? null,
								],
								'advancedStyles' => [
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $counterBarProgressSelector,
											'attr'     => $attrs['counterBar']['decoration']['progressBgColor'] ?? [],
											'property' => 'background-color',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $counterBarProgressSelector,
											'attr'     => $attrs['counterBar']['decoration']['height'] ?? [],
											'property' => 'height',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $counterBarSelector,
											'attr'     => $attrs['counterBar']['decoration']['BgColor'] ?? [],
											'property' => 'background-color',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $counterBarSelector,
											'attr'     => $attrs['counterBar']['decoration']['height'] ?? [],
											'property' => 'height',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $counterWrapperSelector,
											'attr'     => $attrs['counterWrapper']['decoration']['aligment'] ?? [],
											'property' => 'align-items',
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $stripSelector,
											'attr'     => $attrs['counterBar']['decoration']['progressBgColor2'] ?? [],
											'property' => $progressAnimationType === 'striped' ? "background-image: linear-gradient(
												45deg,
												{$progressBgColor2} 25%,
												transparent 25%,
												transparent 50%,
												{$progressBgColor2} 50%,
												{$progressBgColor2} 75%,
												transparent 75%,
												transparent
											);" : '',
										]
									],




								]
							],
						]
					),

					// counterBar.
					$elements->style(
						[
							'attrName' => 'counterBar',
						]
					),

					// counterBarProgress.
					$elements->style(
						[
							'attrName' => 'counterBarProgress',
						]
					),

					// counterCircle.
					$elements->style(
						[
							'attrName'   => 'counterCircle',
							'styleProps' => [
								'disabledOn'     => [
									'disabledModuleVisibility' => $settings['disabledModuleVisibility'] ?? null,
								],
								'advancedStyles' => [
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $counterCircleSelector,
											'attr'     => $attrs['counterCircle']['decoration']['circleSize'] ?? [],
											'property' => "width: {$circleSize}px; height: {$circleSize}px;",
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $counterCircleProgressSelector,
											'attr'     => $attrs['counterCircle']['decoration']['strokeProgressColor'] ?? [],
											'property' => "stroke",
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $counterCircleBgSelector,
											'attr'     => $attrs['counterCircle']['decoration']['strokeColor'] ?? [],
											'property' => "stroke",
										]
									],

								]
							],
						]
					),

					// counterValue.
					$elements->style(
						[
							'attrName' => 'counterValue',
						]
					),

					// counterHalfCircle.
					$elements->style(
						[
							'attrName' => 'counterHalfCircle',
						]
					),

					// roundedAttribues.
					$elements->style(
						[
							'attrName' => 'roundedAttribues',
						]
					),

					// counterBlocks.
					$elements->style(
						[
							'attrName'   => 'counterBlocks',
							'styleProps' => [
								'disabledOn'     => [
									'disabledModuleVisibility' => $settings['disabledModuleVisibility'] ?? null,
								],
								'advancedStyles' => [
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $counterBlocksSelector,
											'attr'     => $attrs['counterBlocks']['decoration']['blockSpacing'] ?? [],
											'property' => "--block-spacing: " . ($attrs['counterBlocks']['decoration']['blockSpacing']['desktop']['value'] ?? "5") . "px;",
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $counterBlocksSelector,
											'attr'     => $attrs['counterWrapper']['decoration']['aligment'] ?? [],
											'property' => "justify-content",
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $counterBlocksItemSelector,
											'attr'     => $attrs['counterBlocks']['decoration']['blockWidth'] ?? [],
											'property' => "width",
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $counterBlocksItemSelector,
											'attr'     => $attrs['counterBlocks']['decoration']['blockHeight'] ?? [],
											'property' => "height",
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $counterBlocksItemSelector,
											'attr'     => $attrs['counterBlocks']['decoration']['blockBgColor'] ?? [],
											'property' => "background-color",
										]
									],
									// [
									// 	'componentName' => 'divi/common',
									// 	'props'         => [
									// 		'selector' => $counterBlocksItemActiveSelector,
									// 		'attr'     => $attrs['counterBlocks']['decoration']['blockColor'] ?? [],
									// 		'property' => "background-color: " . ($attrs['counterBlocks']['decoration']['blockColor']['desktop']['value'] ?? "#6200ee") . " !important;",
									// 	]
									// ],
								]
							],
						]
					),

					// counterBlocksItem.
					$elements->style(
						[
							'attrName' => 'counterBlocksItem',
						]
					),

					// labelTop.
					$elements->style(
						[
							'attrName' => 'labelTop',
						]
					),

					// labelBottom.
					$elements->style(
						[
							'attrName' => 'labelBottom',
						]
					),

					/*
					 * We need to add CssStyle at the very bottom of other
					 * components so that custom css can override module styles
					 * till we find a more elegant solution.
					 */
					CssStyle::style(
						[
							'selector'  => $args['orderClass'],
							'attr'      => $attrs['css'] ?? [],
							'cssFields' => OptimizerCounter::custom_css(),
						]
					),

					// ATTENTION: The code is intentionally added and commented in FE only as an example of expected value format.
					// If you have custom style processing, the style output should be passed as an `array` of style declarations
					// to the `styles` property of the `Style::add` method. For example:
					// [
					// 	[
					// 		'atRules'     => false,
					// 		'selector'    => "{$args['orderClass']} .example_static_module__content-container",
					// 		'declaration' => 'color: red;'
					// 	],
					// 	[
					// 		'atRules'     => '@media only screen and (max-width: 767px)',
					// 		'selector'    => "{$args['orderClass']} .example_static_module__content-container",
					// 		'declaration' => 'color: green;'
					// 	],
					// ],

					// The code below is an example of how to use the `CssStyle::style` method to generate CSS style.
					CssStyle::style(
						[
							'selector'  => $args['orderClass'],
							'attr'      => $attrs['css'] ?? [],
							'cssFields' => OptimizerCounter::custom_css(),
						]
					),
				],
			]
		);
	}
}
